import{_ as J,a1 as ae,O as q,W as A,B,G as O,v as N,E as f,Q as Z,c as k,a as e,w as l,b as d,y as $,f as p,h as r,C as Q,D as X,m as S,t as w,x as R,F as U,l as L,e as h,I as le,p as ee,q as te,j as oe,M as G,a7 as se}from"./index-MGgq8mV5.js";const ne={name:"ConfigManagement",components:{Search:N,Refresh:O,Plus:B,Edit:A,Delete:q,Connection:ae},data(){return{searchForm:{config_name:"",config_type:"",is_active:null},tableData:[],loading:!1,updating:!1,pagination:{page:1,pageSize:10,total:0},dialogVisible:!1,dialogTitle:"",isEdit:!1,submitting:!1,formData:{config_name:"",config_type:"webhook",webhook_url:"",webhook_method:"POST",webhook_headers:{},email_smtp_host:"",email_smtp_port:587,email_username:"",email_password:"",email_from:"",email_to:"",dingtalk_webhook:"",dingtalk_secret:"",wechat_webhook:"",is_active:!0,description:""},formRules:{config_name:[{required:!0,message:"请输入配置名称",trigger:"blur"}],config_type:[{required:!0,message:"请选择推送类型",trigger:"change"}],webhook_url:[{validator:(a,t,n)=>{try{if(this.formData&&this.formData.config_type==="webhook"){const c=(t||"").toString().trim();if(!c){n(new Error("请输入Webhook URL"));return}if(!/^https?:\/\/.+/.test(c)){n(new Error("请输入正确的URL格式"));return}}n()}catch(c){console.warn("webhook_url验证器错误:",c),n()}},trigger:"blur"}],email_smtp_host:[{validator:(a,t,n)=>{try{if(this.formData&&this.formData.config_type==="email"&&!(t||"").toString().trim()){n(new Error("请输入SMTP主机"));return}n()}catch(c){console.warn("email_smtp_host验证器错误:",c),n()}},trigger:"blur"}],email_smtp_port:[{validator:(a,t,n)=>{try{if(this.formData.config_type==="email"&&!t){n(new Error("请输入SMTP端口"));return}n()}catch(c){console.warn("端口验证器错误:",c),n()}},trigger:"blur"}],email_username:[{validator:(a,t,n)=>{try{if(this.formData&&this.formData.config_type==="email"&&!(t||"").toString().trim()){n(new Error("请输入邮箱用户名"));return}n()}catch(c){console.warn("email_username验证器错误:",c),n()}},trigger:"blur"}],email_password:[{validator:(a,t,n)=>{try{if(this.formData&&this.formData.config_type==="email"&&!(t||"").toString().trim()){n(new Error("请输入邮箱密码"));return}n()}catch(c){console.warn("email_password验证器错误:",c),n()}},trigger:"blur"}],email_from:[{validator:(a,t,n)=>{try{if(this.formData&&this.formData.config_type==="email"){const c=(t||"").toString().trim();if(!c){n(new Error("请输入发送方邮箱"));return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(c)){n(new Error("请输入正确的邮箱格式"));return}}n()}catch(c){console.warn("email_from验证器错误:",c),n()}},trigger:"blur"}],email_to:[{validator:(a,t,n)=>{try{if(this.formData&&this.formData.config_type==="email"&&!(t||"").toString().trim()){n(new Error("请输入接收方邮箱"));return}n()}catch(c){console.warn("email_to验证器错误:",c),n()}},trigger:"blur"}],dingtalk_webhook:[{validator:(a,t,n)=>{try{if(this.formData&&this.formData.config_type==="dingtalk"){const c=(t||"").toString().trim();if(!c){n(new Error("请输入钉钉机器人Webhook地址"));return}if(!/^https?:\/\/.+/.test(c)){n(new Error("请输入正确的URL格式"));return}}n()}catch(c){console.warn("dingtalk_webhook验证器错误:",c),n()}},trigger:"blur"}],wechat_webhook:[{validator:(a,t,n)=>{try{if(this.formData&&this.formData.config_type==="wechat"){const c=(t||"").toString().trim();if(!c){n(new Error("请输入企业微信机器人Webhook地址"));return}if(!/^https?:\/\/.+/.test(c)){n(new Error("请输入正确的URL格式"));return}}n()}catch(c){console.warn("wechat_webhook验证器错误:",c),n()}},trigger:"blur"}]},showHeadersDialog:!1,headers:[]}},computed:{searchIcon(){return N},refreshIcon(){return O},plusIcon(){return B},editIcon(){return A},deleteIcon(){return q},connectionIcon(){return ae}},mounted(){this.loadData()},methods:{async loadData(){this.loading=!0;try{const a={...this.searchForm,page:this.pagination.page,pageSize:this.pagination.pageSize},t=await this.$axios.post("/api/get_msg_push_configs",a);t.data.success?(this.tableData=t.data.data,this.pagination.total=t.data.total):f.error(t.data.message||"获取数据失败")}catch(a){console.error("加载数据失败:",a);let t="获取数据失败";a.response&&a.response.data&&a.response.data.message?t=a.response.data.message:a.message&&(t=`获取数据失败: ${a.message}`),f.error(t)}finally{this.loading=!1}},refreshData(){this.loadData()},handleSearch(){this.pagination.page=1,this.loadData()},handleReset(){this.searchForm={config_name:"",config_type:"",is_active:null},this.pagination.page=1,this.loadData()},handleSizeChange(a){this.pagination.pageSize=a,this.pagination.page=1,this.loadData()},handleCurrentChange(a){this.pagination.page=a,this.loadData()},handleAdd(){this.isEdit=!1,this.dialogTitle="新增推送配置",this.resetFormData(),this.dialogVisible=!0},handleEdit(a){this.isEdit=!0,this.dialogTitle="编辑推送配置",this.formData={...a},this.formData.webhook_headers=a.webhook_headers||{},this.headers=this.objectToHeaders(this.formData.webhook_headers),this.dialogVisible=!0},async handleDelete(a){try{await Z.confirm(`确定要删除推送配置"${a.config_name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await this.$axios.post("/api/delete_msg_push_config",{id:a.id});t.data.success?(f.success("删除成功"),this.loadData()):f.error(t.data.message||"删除失败")}catch(t){if(t!=="cancel"){console.error("删除失败:",t);let n="删除失败";t.response&&t.response.data&&t.response.data.message?n=t.response.data.message:t.message&&(n=`删除失败: ${t.message}`),f.error(n)}}},async handleTest(a){const t=this.tableData.findIndex(n=>n.id===a.id);t!==-1&&(this.tableData[t]={...this.tableData[t],testing:!0});try{const n=await this.$axios.post("/api/test_msg_push_config",{id:a.id});n.data.success?f.success("推送测试成功"):f.error(n.data.message||"推送测试失败")}catch(n){console.error("推送测试失败:",n);let c="推送测试失败";n.response&&n.response.data&&n.response.data.message?c=n.response.data.message:n.response&&n.response.data&&n.response.data.error?c=`推送测试失败: ${n.response.data.error}`:n.message&&(c=`推送测试失败: ${n.message}`),f.error(c)}finally{t!==-1&&(this.tableData[t]={...this.tableData[t],testing:!1})}},async handleStatusChange(a){this.updating=!0;try{const t=await this.$axios.post("/api/update_msg_push_config",{id:a.id,config_name:a.config_name,config_type:a.config_type,webhook_url:a.webhook_url,webhook_method:a.webhook_method,webhook_headers:a.webhook_headers,email_smtp_host:a.email_smtp_host,email_smtp_port:a.email_smtp_port,email_username:a.email_username,email_password:a.email_password,email_from:a.email_from,email_to:a.email_to,dingtalk_webhook:a.dingtalk_webhook,dingtalk_secret:a.dingtalk_secret,wechat_webhook:a.wechat_webhook,is_active:a.is_active,description:a.description});t.data.success?f.success("状态更新成功"):(a.is_active=!a.is_active,f.error(t.data.message||"状态更新失败"))}catch(t){a.is_active=!a.is_active,console.error("状态更新失败:",t);let n="状态更新失败";t.response&&t.response.data&&t.response.data.message?n=t.response.data.message:t.message&&(n=`状态更新失败: ${t.message}`),f.error(n)}finally{this.updating=!1}},async handleSubmit(){try{await this.$refs.formRef.validate()}catch(a){console.log("表单验证失败:",a);return}this.submitting=!0;try{const a=this.isEdit?"/api/update_msg_push_config":"/api/add_msg_push_config",t={...this.formData};t.webhook_headers=this.headersToObject(this.headers);const n=await this.$axios.post(a,t);console.log("API响应状态:",n.status),console.log("API响应数据:",n.data),console.log("响应数据类型:",typeof n.data),console.log("是否包含success字段:","success"in n.data);let c=!1,o="";n.data&&typeof n.data=="object"?"success"in n.data?(c=n.data.success,o=n.data.message||(this.isEdit?"更新成功":"新增成功")):n.data.id?(c=!0,o=this.isEdit?"更新成功":"新增成功",console.log("检测到直接返回数据记录格式，视为操作成功")):(c=!1,o=n.data.message||n.data.error||"操作失败"):(c=n.status>=200&&n.status<300,o=c?this.isEdit?"更新成功":"新增成功":"操作失败"),c?(f.success(o),this.dialogVisible=!1,this.isEdit||(this.pagination.page=1,this.searchForm={config_name:"",config_type:"",is_active:null}),this.loadData()):(f.error(o),console.error("操作失败 - 后端返回:",n.data))}catch(a){console.error("提交失败:",a);let t="操作失败";if(a.response){const{status:n,data:c}=a.response;if(c&&c.message)t=c.message;else if(c&&c.error)t=`操作失败: ${c.error}`;else switch(n){case 400:t="请求参数错误，请检查填写内容";break;case 401:t="登录已过期，请重新登录";break;case 403:t="权限不足，无法执行此操作";break;case 404:t="请求的资源不存在";break;case 500:t="服务器内部错误，请联系管理员";break;default:t=`操作失败 (状态码: ${n})`}}else a.request?t="网络连接失败，请检查网络连接":t=`操作失败: ${a.message}`;f.error(t)}finally{this.submitting=!1}},handleDialogClose(){this.$nextTick(()=>{var a;(a=this.$refs.formRef)==null||a.clearValidate()}),this.resetFormData()},resetFormData(){this.formData={config_name:"",config_type:"webhook",webhook_url:"",webhook_method:"POST",webhook_headers:{},email_smtp_host:"",email_smtp_port:587,email_username:"",email_password:"",email_from:"",email_to:"",dingtalk_webhook:"",dingtalk_secret:"",wechat_webhook:"",is_active:!0,description:""},this.headers=[]},handleConfigTypeChange(){this.formData.config_type!=="webhook"&&(this.formData.webhook_url="",this.formData.webhook_method="POST",this.formData.webhook_headers={},this.headers=[]),this.formData.config_type!=="email"&&(this.formData.email_smtp_host="",this.formData.email_smtp_port=587,this.formData.email_username="",this.formData.email_password="",this.formData.email_from="",this.formData.email_to=""),this.formData.config_type!=="dingtalk"&&(this.formData.dingtalk_webhook="",this.formData.dingtalk_secret=""),this.formData.config_type!=="wechat"&&(this.formData.wechat_webhook=""),this.$nextTick(()=>{var a;(a=this.$refs.formRef)==null||a.clearValidate()})},addHeader(){this.headers.push({key:"",value:""})},removeHeader(a){this.headers.splice(a,1)},saveHeaders(){this.showHeadersDialog=!1},objectToHeaders(a){return Object.entries(a||{}).map(([t,n])=>({key:t,value:n}))},headersToObject(a){const t={};return a.forEach(n=>{n.key&&n.value&&(t[n.key]=n.value)}),t},getConfigTypeName(a){return{webhook:"Webhook",email:"邮件",dingtalk:"钉钉",wechat:"企业微信"}[a]||a},getConfigTypeTagType(a){return{webhook:"primary",email:"success",dingtalk:"warning",wechat:"info"}[a]||"default"},formatDateTime(a){return a?new Date(a).toLocaleString("zh-CN"):""}}},re={class:"config-management"},de={class:"button-container"},ue={class:"action-buttons"},me={class:"pagination-container"},ce={class:"dialog-footer"},pe={class:"headers-config"},_e={class:"dialog-footer"};function he(a,t,n,c,o,i){const b=d("el-input"),_=d("el-form-item"),g=d("el-col"),m=d("el-option"),V=d("el-select"),y=d("el-button"),D=d("el-row"),I=d("el-form"),T=d("el-card"),v=d("el-table-column"),C=d("el-tag"),E=d("el-switch"),P=d("el-table"),M=d("el-pagination"),H=d("el-input-number"),x=d("el-dialog"),j=X("loading");return h(),k("div",re,[e(T,{class:"search-card",shadow:"never"},{default:l(()=>[e(I,{model:o.searchForm,ref:"searchFormRef","label-width":"80px","label-position":"right",class:"search-form"},{default:l(()=>[e(D,{gutter:20},{default:l(()=>[e(g,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(_,{label:"配置名称"},{default:l(()=>[e(b,{modelValue:o.searchForm.config_name,"onUpdate:modelValue":t[0]||(t[0]=u=>o.searchForm.config_name=u),placeholder:"请输入配置名称",clearable:"",onKeyup:$(i.handleSearch,["enter"]),class:"form-control"},null,8,["modelValue","onKeyup"])]),_:1})]),_:1}),e(g,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(_,{label:"推送类型"},{default:l(()=>[e(V,{modelValue:o.searchForm.config_type,"onUpdate:modelValue":t[1]||(t[1]=u=>o.searchForm.config_type=u),placeholder:"请选择推送类型",clearable:"",class:"form-control"},{default:l(()=>[e(m,{label:"Webhook",value:"webhook"}),e(m,{label:"邮件",value:"email"}),e(m,{label:"钉钉",value:"dingtalk"}),e(m,{label:"企业微信",value:"wechat"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(_,{label:"启用状态"},{default:l(()=>[e(V,{modelValue:o.searchForm.is_active,"onUpdate:modelValue":t[2]||(t[2]=u=>o.searchForm.is_active=u),placeholder:"请选择启用状态",clearable:"",class:"form-control"},{default:l(()=>[e(m,{label:"启用",value:!0}),e(m,{label:"禁用",value:!1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{xs:24,sm:12,md:8,lg:24,class:"search-buttons-col"},{default:l(()=>[e(_,null,{default:l(()=>[p("div",de,[e(y,{type:"primary",onClick:i.handleSearch,icon:i.searchIcon},{default:l(()=>[r("搜索")]),_:1},8,["onClick","icon"]),e(y,{onClick:i.handleReset,icon:i.refreshIcon},{default:l(()=>[r("重置")]),_:1},8,["onClick","icon"]),e(y,{type:"success",onClick:i.handleAdd,icon:i.plusIcon},{default:l(()=>[r("新增配置")]),_:1},8,["onClick","icon"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(T,{class:"table-card",shadow:"never"},{default:l(()=>[Q((h(),S(P,{data:o.tableData,border:"",stripe:"",height:"600",style:{width:"100%"}},{default:l(()=>[e(v,{prop:"config_name",label:"配置名称",width:"150","show-overflow-tooltip":""}),e(v,{prop:"config_type",label:"推送类型",width:"100",align:"center"},{default:l(({row:u})=>[e(C,{type:i.getConfigTypeTagType(u.config_type)},{default:l(()=>[r(w(i.getConfigTypeName(u.config_type)),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"webhook_url",label:"Webhook地址",width:"200","show-overflow-tooltip":""}),e(v,{prop:"email_to",label:"邮件接收方",width:"150","show-overflow-tooltip":""}),e(v,{prop:"is_active",label:"启用状态",width:"100",align:"center"},{default:l(({row:u})=>[e(E,{modelValue:u.is_active,"onUpdate:modelValue":z=>u.is_active=z,onChange:z=>i.handleStatusChange(u),disabled:o.updating},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),e(v,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""}),e(v,{prop:"created_at",label:"创建时间",width:"160",align:"center"},{default:l(({row:u})=>[r(w(i.formatDateTime(u.created_at)),1)]),_:1}),e(v,{label:"操作",width:"240",align:"center",fixed:"right"},{default:l(({row:u})=>[p("div",ue,[e(y,{type:"primary",size:"small",onClick:z=>i.handleTest(u),loading:u.testing,icon:i.connectionIcon},{default:l(()=>[r(" 测试 ")]),_:2},1032,["onClick","loading","icon"]),e(y,{type:"warning",size:"small",onClick:z=>i.handleEdit(u),icon:i.editIcon},{default:l(()=>[r(" 编辑 ")]),_:2},1032,["onClick","icon"]),e(y,{type:"danger",size:"small",onClick:z=>i.handleDelete(u),icon:i.deleteIcon},{default:l(()=>[r(" 删除 ")]),_:2},1032,["onClick","icon"])])]),_:1})]),_:1},8,["data"])),[[j,o.loading]]),p("div",me,[e(M,{"current-page":o.pagination.page,"onUpdate:currentPage":t[3]||(t[3]=u=>o.pagination.page=u),"page-size":o.pagination.pageSize,"onUpdate:pageSize":t[4]||(t[4]=u=>o.pagination.pageSize=u),total:o.pagination.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:i.handleSizeChange,onCurrentChange:i.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),e(x,{title:o.dialogTitle,modelValue:o.dialogVisible,"onUpdate:modelValue":t[22]||(t[22]=u=>o.dialogVisible=u),width:"800px",onClose:i.handleDialogClose},{footer:l(()=>[p("span",ce,[e(y,{onClick:t[21]||(t[21]=u=>o.dialogVisible=!1)},{default:l(()=>[r("取消")]),_:1}),e(y,{type:"primary",onClick:i.handleSubmit,loading:o.submitting},{default:l(()=>[r(" 确定 ")]),_:1},8,["onClick","loading"])])]),default:l(()=>[e(I,{model:o.formData,rules:o.formRules,ref:"formRef","label-width":"120px"},{default:l(()=>[e(D,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"配置名称",prop:"config_name"},{default:l(()=>[e(b,{modelValue:o.formData.config_name,"onUpdate:modelValue":t[5]||(t[5]=u=>o.formData.config_name=u),placeholder:"请输入配置名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"推送类型",prop:"config_type"},{default:l(()=>[e(V,{modelValue:o.formData.config_type,"onUpdate:modelValue":t[6]||(t[6]=u=>o.formData.config_type=u),placeholder:"请选择推送类型",onChange:i.handleConfigTypeChange},{default:l(()=>[e(m,{label:"Webhook推送",value:"webhook"}),e(m,{label:"邮件推送",value:"email"}),e(m,{label:"钉钉推送",value:"dingtalk"}),e(m,{label:"企业微信推送",value:"wechat"})]),_:1},8,["modelValue","onChange"])]),_:1})]),_:1})]),_:1}),o.formData.config_type==="webhook"?(h(),k(U,{key:0},[e(_,{label:"Webhook URL",prop:"webhook_url"},{default:l(()=>[e(b,{modelValue:o.formData.webhook_url,"onUpdate:modelValue":t[7]||(t[7]=u=>o.formData.webhook_url=u),placeholder:"请输入Webhook URL"},null,8,["modelValue"])]),_:1}),e(D,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"请求方法",prop:"webhook_method"},{default:l(()=>[e(V,{modelValue:o.formData.webhook_method,"onUpdate:modelValue":t[8]||(t[8]=u=>o.formData.webhook_method=u)},{default:l(()=>[e(m,{label:"POST",value:"POST"}),e(m,{label:"PUT",value:"PUT"}),e(m,{label:"PATCH",value:"PATCH"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"请求头"},{default:l(()=>[e(y,{onClick:t[9]||(t[9]=u=>o.showHeadersDialog=!0),size:"small"},{default:l(()=>[r(" 配置请求头 ")]),_:1})]),_:1})]),_:1})]),_:1})],64)):R("",!0),o.formData.config_type==="email"?(h(),k(U,{key:1},[e(D,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"SMTP主机",prop:"email_smtp_host"},{default:l(()=>[e(b,{modelValue:o.formData.email_smtp_host,"onUpdate:modelValue":t[10]||(t[10]=u=>o.formData.email_smtp_host=u),placeholder:"请输入SMTP主机"},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"SMTP端口",prop:"email_smtp_port"},{default:l(()=>[e(H,{modelValue:o.formData.email_smtp_port,"onUpdate:modelValue":t[11]||(t[11]=u=>o.formData.email_smtp_port=u),min:1,max:65535},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(D,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"用户名",prop:"email_username"},{default:l(()=>[e(b,{modelValue:o.formData.email_username,"onUpdate:modelValue":t[12]||(t[12]=u=>o.formData.email_username=u),placeholder:"请输入邮箱用户名"},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"密码",prop:"email_password"},{default:l(()=>[e(b,{modelValue:o.formData.email_password,"onUpdate:modelValue":t[13]||(t[13]=u=>o.formData.email_password=u),type:"password",placeholder:"请输入邮箱密码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(D,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"发送方邮箱",prop:"email_from"},{default:l(()=>[e(b,{modelValue:o.formData.email_from,"onUpdate:modelValue":t[14]||(t[14]=u=>o.formData.email_from=u),placeholder:"请输入发送方邮箱"},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"接收方邮箱",prop:"email_to"},{default:l(()=>[e(b,{modelValue:o.formData.email_to,"onUpdate:modelValue":t[15]||(t[15]=u=>o.formData.email_to=u),placeholder:"多个邮箱用逗号分隔"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})],64)):R("",!0),o.formData.config_type==="dingtalk"?(h(),k(U,{key:2},[e(_,{label:"机器人Webhook",prop:"dingtalk_webhook"},{default:l(()=>[e(b,{modelValue:o.formData.dingtalk_webhook,"onUpdate:modelValue":t[16]||(t[16]=u=>o.formData.dingtalk_webhook=u),placeholder:"请输入钉钉机器人Webhook地址"},null,8,["modelValue"])]),_:1}),e(_,{label:"签名密钥"},{default:l(()=>[e(b,{modelValue:o.formData.dingtalk_secret,"onUpdate:modelValue":t[17]||(t[17]=u=>o.formData.dingtalk_secret=u),placeholder:"请输入签名密钥(可选)"},null,8,["modelValue"])]),_:1})],64)):R("",!0),o.formData.config_type==="wechat"?(h(),S(_,{key:3,label:"机器人Webhook",prop:"wechat_webhook"},{default:l(()=>[e(b,{modelValue:o.formData.wechat_webhook,"onUpdate:modelValue":t[18]||(t[18]=u=>o.formData.wechat_webhook=u),placeholder:"请输入企业微信机器人Webhook地址"},null,8,["modelValue"])]),_:1})):R("",!0),e(D,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"启用状态"},{default:l(()=>[e(E,{modelValue:o.formData.is_active,"onUpdate:modelValue":t[19]||(t[19]=u=>o.formData.is_active=u)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(_,{label:"描述"},{default:l(()=>[e(b,{modelValue:o.formData.description,"onUpdate:modelValue":t[20]||(t[20]=u=>o.formData.description=u),type:"textarea",rows:3,placeholder:"请输入描述信息"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue","onClose"]),e(x,{title:"配置HTTP请求头",modelValue:o.showHeadersDialog,"onUpdate:modelValue":t[24]||(t[24]=u=>o.showHeadersDialog=u),width:"600px"},{footer:l(()=>[p("span",_e,[e(y,{onClick:t[23]||(t[23]=u=>o.showHeadersDialog=!1)},{default:l(()=>[r("取消")]),_:1}),e(y,{type:"primary",onClick:i.saveHeaders},{default:l(()=>[r("确定")]),_:1},8,["onClick"])])]),default:l(()=>[p("div",pe,[e(y,{type:"primary",size:"small",onClick:i.addHeader,style:{"margin-bottom":"10px"}},{default:l(()=>[r(" 添加请求头 ")]),_:1},8,["onClick"]),(h(!0),k(U,null,L(o.headers,(u,z)=>(h(),k("div",{key:z,class:"header-item"},[e(b,{modelValue:u.key,"onUpdate:modelValue":s=>u.key=s,placeholder:"Header名称",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),e(b,{modelValue:u.value,"onUpdate:modelValue":s=>u.value=s,placeholder:"Header值",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),e(y,{type:"danger",size:"small",onClick:s=>i.removeHeader(z),icon:i.deleteIcon},null,8,["onClick","icon"])]))),128))])]),_:1},8,["modelValue"])])}const fe=J(ne,[["render",he],["__scopeId","data-v-a82a776c"]]),ge={name:"TaskManagement",components:{Search:N,Refresh:O,Plus:B,Edit:A,Delete:q,VideoPlay:le},data(){return{searchForm:{task_name:"",task_type:"",status:"",is_active:null},tableData:[],loading:!1,updating:!1,pagination:{page:1,pageSize:10,total:0},dialogVisible:!1,dialogTitle:"",isEdit:!1,submitting:!1,formData:{task_name:"",task_type:"change_notification",config_id:null,schedule_type:"manual",schedule_value:"",template_id:null,filter_conditions:{},is_active:!0,description:""},formRules:{task_name:[{required:!0,message:"请输入任务名称",trigger:"blur"}],task_type:[{required:!0,message:"请选择任务类型",trigger:"change"}],config_id:[{required:!0,message:"请选择推送配置",trigger:"change"}],schedule_type:[{required:!0,message:"请选择调度类型",trigger:"change"}],schedule_value:[{validator:(a,t,n)=>{this.formData.schedule_type==="manual"||t?n():n(new Error("请设置调度值"))},trigger:"change"}]},configOptions:[],templateOptions:[],weekDay:"",weekTime:"",monthDay:1,monthTime:"",showFilterDialog:!1,filterConditions:{change_level:[],change_systems:[]},changeLevelOptions:[],changeSystemOptions:[]}},computed:{hasFilterConditions(){return Object.keys(this.formData.filter_conditions||{}).length>0},searchIcon(){return N},refreshIcon(){return O},plusIcon(){return B},editIcon(){return A},deleteIcon(){return q},runIcon(){return le}},mounted(){this.loadData(),this.loadOptions()},methods:{async loadData(){this.loading=!0;try{const a=await this.$axios.post("/api/get_msg_push_tasks",{...this.searchForm,page:this.pagination.page,pageSize:this.pagination.pageSize});a.data.success?(this.tableData=a.data.data,this.pagination.total=a.data.total):f.error(a.data.message||"获取数据失败")}catch(a){console.error("加载数据失败:",a),f.error("获取数据失败")}finally{this.loading=!1}},async loadOptions(){try{const a=await this.$axios.post("/api/get_msg_push_options");a.data.success&&(this.configOptions=a.data.data.configs,this.templateOptions=a.data.data.templates)}catch(a){console.error("加载选项数据失败:",a)}},async loadFilterOptions(){try{const a=await this.$axios.get("/api/get_change_level_options");a.data.success&&(this.changeLevelOptions=a.data.data);const t=await this.$axios.get("/api/get_change_system_options");t.data.success&&(this.changeSystemOptions=t.data.data)}catch(a){console.error("加载过滤条件选项失败:",a),f.error("加载过滤条件选项失败")}},refreshData(){this.loadData()},handleSearch(){this.pagination.page=1,this.loadData()},handleReset(){this.searchForm={task_name:"",task_type:"",status:"",is_active:null},this.pagination.page=1,this.loadData()},handleSizeChange(a){this.pagination.pageSize=a,this.pagination.page=1,this.loadData()},handleCurrentChange(a){this.pagination.page=a,this.loadData()},handleAdd(){this.isEdit=!1,this.dialogTitle="新增推送任务",this.resetFormData(),this.dialogVisible=!0},handleEdit(a){this.isEdit=!0,this.dialogTitle="编辑推送任务",this.formData={...a},this.formData.filter_conditions=a.filter_conditions||{},this.filterConditions={...this.formData.filter_conditions},this.parseScheduleValue(a.schedule_type,a.schedule_value),this.dialogVisible=!0},async handleDelete(a){try{await Z.confirm(`确定要删除推送任务"${a.task_name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await this.$axios.post("/api/delete_msg_push_task",{id:a.id});t.data.success?(f.success("删除成功"),this.loadData()):f.error(t.data.message||"删除失败")}catch(t){t!=="cancel"&&(console.error("删除失败:",t),f.error("删除失败"))}},async handleExecute(a){a.executing=!0;try{const t=await this.$axios.post("/api/execute_msg_push_task",{id:a.id});t.data.success?(f.success(t.data.message||"任务执行成功"),this.loadData()):f.error(t.data.message||"任务执行失败")}catch(t){console.error("任务执行失败:",t),f.error("任务执行失败")}finally{a.executing=!1}},async handleStatusChange(a){this.updating=!0;try{const t=await this.$axios.post("/api/update_msg_push_task",{id:a.id,task_name:a.task_name,task_type:a.task_type,config_id:a.config_id,schedule_type:a.schedule_type,schedule_value:a.schedule_value,template_id:a.template_id,filter_conditions:a.filter_conditions,is_active:a.is_active,description:a.description});t.data.success?f.success("状态更新成功"):(a.is_active=!a.is_active,f.error(t.data.message||"状态更新失败"))}catch(t){a.is_active=!a.is_active,console.error("状态更新失败:",t),f.error("状态更新失败")}finally{this.updating=!1}},async handleSubmit(){try{await this.$refs.formRef.validate()}catch{return}this.buildScheduleValue(),this.submitting=!0;try{const a=this.isEdit?"/api/update_msg_push_task":"/api/add_msg_push_task",t={...this.formData};t.filter_conditions=this.filterConditions;const n=await this.$axios.post(a,t);n.data.success?(f.success(this.isEdit?"更新成功":"新增成功"),this.dialogVisible=!1,this.loadData()):f.error(n.data.message||"操作失败")}catch(a){console.error("提交失败:",a),f.error("操作失败")}finally{this.submitting=!1}},handleDialogClose(){var a;(a=this.$refs.formRef)==null||a.clearValidate(),this.resetFormData()},resetFormData(){this.formData={task_name:"",task_type:"change_notification",config_id:null,schedule_type:"manual",schedule_value:"",template_id:null,filter_conditions:{},is_active:!0,description:""},this.weekDay="",this.weekTime="",this.monthDay=1,this.monthTime="",this.filterConditions={change_level:[],change_systems:[]}},handleScheduleTypeChange(){this.formData.schedule_value="",this.weekDay="",this.weekTime="",this.monthDay=1,this.monthTime=""},parseScheduleValue(a,t){if(t)switch(a){case"weekly":const[n,c]=t.split(":");this.weekDay=n,this.weekTime=c;break;case"monthly":const[o,i]=t.split(":");this.monthDay=parseInt(o),this.monthTime=i;break}},buildScheduleValue(){switch(this.formData.schedule_type){case"weekly":this.weekDay&&this.weekTime&&(this.formData.schedule_value=`${this.weekDay}:${this.weekTime}`);break;case"monthly":this.monthDay&&this.monthTime&&(this.formData.schedule_value=`${this.monthDay}:${this.monthTime}`);break;case"manual":this.formData.schedule_value="";break}},saveFilterConditions(){this.formData.filter_conditions={...this.filterConditions},this.showFilterDialog=!1},getTaskTypeName(a){return{change_notification:"变更通知",event_notification:"事件通知",custom:"自定义通知"}[a]||a},getTaskTypeTagType(a){return{change_notification:"primary",event_notification:"warning",custom:"info"}[a]||"default"},getScheduleTypeName(a){return{manual:"手动",once:"单次",daily:"每日",weekly:"每周",monthly:"每月",cron:"Cron"}[a]||a},getScheduleTypeTagType(a){return{manual:"info",once:"warning",daily:"success",weekly:"primary",monthly:"danger",cron:"default"}[a]||"default"},getStatusName(a){return{pending:"待执行",running:"执行中",success:"成功",failed:"失败"}[a]||a},getStatusTagType(a){return{pending:"info",running:"warning",success:"success",failed:"danger"}[a]||"default"},getConfigTypeName(a){return{webhook:"Webhook",email:"邮件",dingtalk:"钉钉",wechat:"企业微信"}[a]||a},formatDateTime(a){return a?new Date(a).toLocaleString("zh-CN"):""}}},ie=a=>(ee("data-v-424e5cd0"),a=a(),te(),a),be={class:"task-management"},ye={class:"button-container"},ve={class:"action-buttons"},De={class:"pagination-container"},we={key:0,style:{"margin-left":"10px",color:"#67c23a"}},ke={class:"dialog-footer"},Ve={class:"filter-config"},Ce=ie(()=>p("div",{class:"help-text"},"可选择多个变更级别，空表示不限制",-1)),Te=ie(()=>p("div",{class:"help-text"},"可选择多个变更系统，空表示不限制",-1)),xe={class:"dialog-footer"};function Se(a,t,n,c,o,i){const b=d("el-input"),_=d("el-form-item"),g=d("el-col"),m=d("el-option"),V=d("el-select"),y=d("el-button"),D=d("el-row"),I=d("el-form"),T=d("el-card"),v=d("el-table-column"),C=d("el-tag"),E=d("el-switch"),P=d("el-table"),M=d("el-pagination"),H=d("el-date-picker"),x=d("el-time-picker"),j=d("el-input-number"),u=d("el-dialog"),z=X("loading");return h(),k("div",be,[e(T,{class:"search-card",shadow:"never"},{default:l(()=>[e(I,{model:o.searchForm,ref:"searchFormRef","label-width":"80px","label-position":"right",class:"search-form"},{default:l(()=>[e(D,{gutter:20},{default:l(()=>[e(g,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(_,{label:"任务名称"},{default:l(()=>[e(b,{modelValue:o.searchForm.task_name,"onUpdate:modelValue":t[0]||(t[0]=s=>o.searchForm.task_name=s),placeholder:"请输入任务名称",clearable:"",onKeyup:$(i.handleSearch,["enter"]),class:"form-control"},null,8,["modelValue","onKeyup"])]),_:1})]),_:1}),e(g,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(_,{label:"任务类型"},{default:l(()=>[e(V,{modelValue:o.searchForm.task_type,"onUpdate:modelValue":t[1]||(t[1]=s=>o.searchForm.task_type=s),placeholder:"请选择任务类型",clearable:"",class:"form-control"},{default:l(()=>[e(m,{label:"变更通知",value:"change_notification"}),e(m,{label:"事件通知",value:"event_notification"}),e(m,{label:"自定义通知",value:"custom"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(_,{label:"执行状态"},{default:l(()=>[e(V,{modelValue:o.searchForm.status,"onUpdate:modelValue":t[2]||(t[2]=s=>o.searchForm.status=s),placeholder:"请选择执行状态",clearable:"",class:"form-control"},{default:l(()=>[e(m,{label:"待执行",value:"pending"}),e(m,{label:"执行中",value:"running"}),e(m,{label:"成功",value:"success"}),e(m,{label:"失败",value:"failed"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(_,{label:"启用状态"},{default:l(()=>[e(V,{modelValue:o.searchForm.is_active,"onUpdate:modelValue":t[3]||(t[3]=s=>o.searchForm.is_active=s),placeholder:"请选择启用状态",clearable:"",class:"form-control"},{default:l(()=>[e(m,{label:"启用",value:!0}),e(m,{label:"禁用",value:!1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{xs:24,sm:12,md:8,lg:24,class:"search-buttons-col"},{default:l(()=>[e(_,null,{default:l(()=>[p("div",ye,[e(y,{type:"primary",onClick:i.handleSearch,icon:i.searchIcon},{default:l(()=>[r("搜索")]),_:1},8,["onClick","icon"]),e(y,{onClick:i.handleReset,icon:i.refreshIcon},{default:l(()=>[r("重置")]),_:1},8,["onClick","icon"]),e(y,{type:"success",onClick:i.handleAdd,icon:i.plusIcon},{default:l(()=>[r("新增任务")]),_:1},8,["onClick","icon"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(T,{class:"table-card",shadow:"never"},{default:l(()=>[Q((h(),S(P,{data:o.tableData,border:"",stripe:"",height:"600",style:{width:"100%"}},{default:l(()=>[e(v,{prop:"task_name",label:"任务名称",width:"150","show-overflow-tooltip":""}),e(v,{prop:"task_type",label:"任务类型",width:"120",align:"center"},{default:l(({row:s})=>[e(C,{type:i.getTaskTypeTagType(s.task_type)},{default:l(()=>[r(w(i.getTaskTypeName(s.task_type)),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"config_name",label:"推送配置",width:"150","show-overflow-tooltip":""}),e(v,{prop:"schedule_type",label:"调度类型",width:"100",align:"center"},{default:l(({row:s})=>[e(C,{size:"small",type:i.getScheduleTypeTagType(s.schedule_type)},{default:l(()=>[r(w(i.getScheduleTypeName(s.schedule_type)),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"schedule_value",label:"调度值",width:"120","show-overflow-tooltip":""}),e(v,{prop:"next_run_time",label:"下次执行",width:"160",align:"center"},{default:l(({row:s})=>[r(w(i.formatDateTime(s.next_run_time)),1)]),_:1}),e(v,{prop:"status",label:"状态",width:"100",align:"center"},{default:l(({row:s})=>[e(C,{type:i.getStatusTagType(s.status)},{default:l(()=>[r(w(i.getStatusName(s.status)),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"is_active",label:"启用状态",width:"100",align:"center"},{default:l(({row:s})=>[e(E,{modelValue:s.is_active,"onUpdate:modelValue":F=>s.is_active=F,onChange:F=>i.handleStatusChange(s),disabled:o.updating},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),e(v,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""}),e(v,{label:"操作",width:"280",align:"center",fixed:"right"},{default:l(({row:s})=>[p("div",ve,[e(y,{type:"success",size:"small",onClick:F=>i.handleExecute(s),loading:s.executing,icon:i.runIcon,disabled:!s.is_active},{default:l(()=>[r(" 执行 ")]),_:2},1032,["onClick","loading","icon","disabled"]),e(y,{type:"warning",size:"small",onClick:F=>i.handleEdit(s),icon:i.editIcon},{default:l(()=>[r(" 编辑 ")]),_:2},1032,["onClick","icon"]),e(y,{type:"danger",size:"small",onClick:F=>i.handleDelete(s),icon:i.deleteIcon},{default:l(()=>[r(" 删除 ")]),_:2},1032,["onClick","icon"])])]),_:1})]),_:1},8,["data"])),[[z,o.loading]]),p("div",De,[e(M,{"current-page":o.pagination.page,"onUpdate:currentPage":t[4]||(t[4]=s=>o.pagination.page=s),"page-size":o.pagination.pageSize,"onUpdate:pageSize":t[5]||(t[5]=s=>o.pagination.pageSize=s),total:o.pagination.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:i.handleSizeChange,onCurrentChange:i.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),e(u,{title:o.dialogTitle,modelValue:o.dialogVisible,"onUpdate:modelValue":t[23]||(t[23]=s=>o.dialogVisible=s),width:"800px",onClose:i.handleDialogClose},{footer:l(()=>[p("span",ke,[e(y,{onClick:t[22]||(t[22]=s=>o.dialogVisible=!1)},{default:l(()=>[r("取消")]),_:1}),e(y,{type:"primary",onClick:i.handleSubmit,loading:o.submitting},{default:l(()=>[r(" 确定 ")]),_:1},8,["onClick","loading"])])]),default:l(()=>[e(I,{model:o.formData,rules:o.formRules,ref:"formRef","label-width":"120px"},{default:l(()=>[e(D,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"任务名称",prop:"task_name"},{default:l(()=>[e(b,{modelValue:o.formData.task_name,"onUpdate:modelValue":t[6]||(t[6]=s=>o.formData.task_name=s),placeholder:"请输入任务名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"任务类型",prop:"task_type"},{default:l(()=>[e(V,{modelValue:o.formData.task_type,"onUpdate:modelValue":t[7]||(t[7]=s=>o.formData.task_type=s),placeholder:"请选择任务类型"},{default:l(()=>[e(m,{label:"变更通知",value:"change_notification"}),e(m,{label:"事件通知",value:"event_notification"}),e(m,{label:"自定义通知",value:"custom"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(D,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"推送配置",prop:"config_id"},{default:l(()=>[e(V,{modelValue:o.formData.config_id,"onUpdate:modelValue":t[8]||(t[8]=s=>o.formData.config_id=s),placeholder:"请选择推送配置",filterable:""},{default:l(()=>[(h(!0),k(U,null,L(o.configOptions,s=>(h(),S(m,{key:s.id,label:`${s.config_name} (${i.getConfigTypeName(s.config_type)})`,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"消息模板",prop:"template_id"},{default:l(()=>[e(V,{modelValue:o.formData.template_id,"onUpdate:modelValue":t[9]||(t[9]=s=>o.formData.template_id=s),placeholder:"请选择消息模板",filterable:""},{default:l(()=>[(h(!0),k(U,null,L(o.templateOptions,s=>(h(),S(m,{key:s.id,label:`${s.template_name} (${s.message_format})`,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(D,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"调度类型",prop:"schedule_type"},{default:l(()=>[e(V,{modelValue:o.formData.schedule_type,"onUpdate:modelValue":t[10]||(t[10]=s=>o.formData.schedule_type=s),placeholder:"请选择调度类型",onChange:i.handleScheduleTypeChange},{default:l(()=>[e(m,{label:"手动执行",value:"manual"}),e(m,{label:"单次执行",value:"once"}),e(m,{label:"每日执行",value:"daily"}),e(m,{label:"每周执行",value:"weekly"}),e(m,{label:"每月执行",value:"monthly"}),e(m,{label:"Cron表达式",value:"cron"})]),_:1},8,["modelValue","onChange"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"调度值",prop:"schedule_value"},{default:l(()=>[o.formData.schedule_type==="once"?(h(),S(H,{key:0,modelValue:o.formData.schedule_value,"onUpdate:modelValue":t[11]||(t[11]=s=>o.formData.schedule_value=s),type:"datetime",placeholder:"选择执行时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])):o.formData.schedule_type==="daily"?(h(),S(x,{key:1,modelValue:o.formData.schedule_value,"onUpdate:modelValue":t[12]||(t[12]=s=>o.formData.schedule_value=s),placeholder:"选择执行时间",format:"HH:mm","value-format":"HH:mm"},null,8,["modelValue"])):o.formData.schedule_type==="weekly"?(h(),k(U,{key:2},[e(V,{modelValue:o.weekDay,"onUpdate:modelValue":t[13]||(t[13]=s=>o.weekDay=s),placeholder:"选择星期"},{default:l(()=>[e(m,{label:"星期一",value:"1"}),e(m,{label:"星期二",value:"2"}),e(m,{label:"星期三",value:"3"}),e(m,{label:"星期四",value:"4"}),e(m,{label:"星期五",value:"5"}),e(m,{label:"星期六",value:"6"}),e(m,{label:"星期日",value:"0"})]),_:1},8,["modelValue"]),e(x,{modelValue:o.weekTime,"onUpdate:modelValue":t[14]||(t[14]=s=>o.weekTime=s),placeholder:"选择时间",format:"HH:mm","value-format":"HH:mm",style:{"margin-left":"10px"}},null,8,["modelValue"])],64)):o.formData.schedule_type==="monthly"?(h(),k(U,{key:3},[e(j,{modelValue:o.monthDay,"onUpdate:modelValue":t[15]||(t[15]=s=>o.monthDay=s),min:1,max:28,placeholder:"日期"},null,8,["modelValue"]),e(x,{modelValue:o.monthTime,"onUpdate:modelValue":t[16]||(t[16]=s=>o.monthTime=s),placeholder:"选择时间",format:"HH:mm","value-format":"HH:mm",style:{"margin-left":"10px"}},null,8,["modelValue"])],64)):o.formData.schedule_type==="cron"?(h(),S(b,{key:4,modelValue:o.formData.schedule_value,"onUpdate:modelValue":t[17]||(t[17]=s=>o.formData.schedule_value=s),placeholder:"请输入Cron表达式 (如: 0 9 * * *)"},null,8,["modelValue"])):(h(),S(b,{key:5,modelValue:o.formData.schedule_value,"onUpdate:modelValue":t[18]||(t[18]=s=>o.formData.schedule_value=s),placeholder:"手动执行无需设置",disabled:""},null,8,["modelValue"]))]),_:1})]),_:1})]),_:1}),e(_,{label:"过滤条件"},{default:l(()=>[e(y,{onClick:t[19]||(t[19]=s=>o.showFilterDialog=!0),size:"small"},{default:l(()=>[r(" 配置过滤条件 ")]),_:1}),i.hasFilterConditions?(h(),k("span",we," 已配置过滤条件 ")):R("",!0)]),_:1}),e(D,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"启用状态"},{default:l(()=>[e(E,{modelValue:o.formData.is_active,"onUpdate:modelValue":t[20]||(t[20]=s=>o.formData.is_active=s)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(_,{label:"描述"},{default:l(()=>[e(b,{modelValue:o.formData.description,"onUpdate:modelValue":t[21]||(t[21]=s=>o.formData.description=s),type:"textarea",rows:3,placeholder:"请输入任务描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue","onClose"]),e(u,{title:"配置过滤条件",modelValue:o.showFilterDialog,"onUpdate:modelValue":t[27]||(t[27]=s=>o.showFilterDialog=s),width:"600px",onOpen:i.loadFilterOptions},{footer:l(()=>[p("span",xe,[e(y,{onClick:t[26]||(t[26]=s=>o.showFilterDialog=!1)},{default:l(()=>[r("取消")]),_:1}),e(y,{type:"primary",onClick:i.saveFilterConditions},{default:l(()=>[r("确定")]),_:1},8,["onClick"])])]),default:l(()=>[p("div",Ve,[e(I,{"label-width":"120px"},{default:l(()=>[e(_,{label:"变更级别"},{default:l(()=>[e(V,{modelValue:o.filterConditions.change_level,"onUpdate:modelValue":t[24]||(t[24]=s=>o.filterConditions.change_level=s),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:"选择变更级别",style:{width:"100%"}},{default:l(()=>[(h(!0),k(U,null,L(o.changeLevelOptions,s=>(h(),S(m,{key:s.dict_code,label:s.dict_name,value:s.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),Ce]),_:1}),e(_,{label:"变更系统"},{default:l(()=>[e(V,{modelValue:o.filterConditions.change_systems,"onUpdate:modelValue":t[25]||(t[25]=s=>o.filterConditions.change_systems=s),multiple:"",filterable:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:"选择变更系统",style:{width:"100%"}},{default:l(()=>[(h(!0),k(U,null,L(o.changeSystemOptions,s=>(h(),S(m,{key:s.system_abbreviation,label:s.system_abbreviation,value:s.system_abbreviation},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),Te]),_:1})]),_:1})])]),_:1},8,["modelValue","onOpen"])])}const Ue=J(ge,[["render",Se],["__scopeId","data-v-424e5cd0"]]),ze={name:"HistoryManagement",components:{Search:N,Refresh:O,DataAnalysis:se,View:G,RefreshRight:oe},data(){return{searchForm:{task_id:null,push_status:"",start_date:"",end_date:""},dateRange:[],tableData:[],loading:!1,pagination:{page:1,pageSize:10,total:0},taskOptions:[],statistics:{total_success:0,total_failed:0,total_pending:0,success_rate:0},detailVisible:!1,detailData:null,statisticsVisible:!1}},computed:{searchIcon(){return N},refreshIcon(){return O},dataAnalysisIcon(){return se},refreshRightIcon(){return oe},viewIcon(){return G}},watch:{dateRange(a){a&&a.length===2?(this.searchForm.start_date=a[0],this.searchForm.end_date=a[1]):(this.searchForm.start_date="",this.searchForm.end_date="")}},mounted(){this.loadData(),this.loadTaskOptions(),this.loadStatistics()},methods:{async loadData(){this.loading=!0;try{const a=await this.$axios.post("/api/get_msg_push_history",{...this.searchForm,page:this.pagination.page,pageSize:this.pagination.pageSize});a.data.success?(this.tableData=a.data.data,this.pagination.total=a.data.total):f.error(a.data.message||"获取数据失败")}catch(a){console.error("加载数据失败:",a),f.error("获取数据失败")}finally{this.loading=!1}},async loadTaskOptions(){try{const a=await this.$axios.post("/api/get_msg_push_tasks",{page:1,pageSize:1e3});a.data.success&&(this.taskOptions=a.data.data)}catch(a){console.error("加载任务选项失败:",a)}},async loadStatistics(){try{const a=await this.$axios.post("/api/get_msg_push_history",{page:1,pageSize:1e3});if(a.data.success){const t=a.data.data,n=t.length,c=t.filter(b=>b.push_status==="success").length,o=t.filter(b=>b.push_status==="failed").length,i=t.filter(b=>b.push_status==="pending").length;this.statistics={total_success:c,total_failed:o,total_pending:i,success_rate:n>0?Math.round(c/n*100):0}}}catch(a){console.error("加载统计数据失败:",a)}},refreshData(){this.loadData(),this.loadStatistics()},handleSearch(){this.pagination.page=1,this.loadData()},handleReset(){this.searchForm={task_id:null,push_status:"",start_date:"",end_date:""},this.dateRange=[],this.pagination.page=1,this.$nextTick(()=>{var a;(a=this.$refs.searchFormRef)==null||a.clearValidate()}),this.loadData()},handleSizeChange(a){this.pagination.pageSize=a,this.pagination.page=1,this.loadData()},handleCurrentChange(a){this.pagination.page=a,this.loadData()},handleViewDetail(a){this.detailData={...a},this.detailVisible=!0},async handleRetry(a){a.retrying=!0;try{const t=await this.$axios.post("/api/retry_msg_push",{id:a.id});t.data.success?(f.success("重试成功"),this.loadData()):f.error(t.data.message||"重试失败")}catch(t){console.error("重试失败:",t),f.error("重试失败")}finally{a.retrying=!1}},handleStatistics(){this.statisticsVisible=!0,this.$nextTick(()=>{this.renderChart()})},renderChart(){const a=this.$refs.chartContainer;a&&(a.innerHTML=`
          <div style="text-align: center; padding: 50px;">
            <h3>推送状态分布</h3>
            <p>成功: ${this.statistics.total_success}</p>
            <p>失败: ${this.statistics.total_failed}</p>
            <p>待推送: ${this.statistics.total_pending}</p>
            <p>成功率: ${this.statistics.success_rate}%</p>
          </div>
        `)},getConfigTypeName(a){return{webhook:"Webhook",email:"邮件",dingtalk:"钉钉",wechat:"企业微信"}[a]||a},getConfigTypeTagType(a){return{webhook:"primary",email:"success",dingtalk:"warning",wechat:"info"}[a]||"default"},getStatusName(a){return{pending:"待推送",sending:"推送中",success:"成功",failed:"失败"}[a]||a},getStatusTagType(a){return{pending:"info",sending:"warning",success:"success",failed:"danger"}[a]||"default"},formatDateTime(a){return a?new Date(a).toLocaleString("zh-CN"):""},formatJson(a){try{return typeof a=="string"?JSON.stringify(JSON.parse(a),null,2):typeof a=="object"?JSON.stringify(a,null,2):a||""}catch{return a||""}}}},W=a=>(ee("data-v-119ceb86"),a=a(),te(),a),Me={class:"history-management"},Fe={class:"button-container"},Ie={class:"stat-item"},Re={class:"stat-number success"},Ee=W(()=>p("div",{class:"stat-label"},"成功推送",-1)),He={class:"stat-item"},Oe={class:"stat-number danger"},Ne=W(()=>p("div",{class:"stat-label"},"失败推送",-1)),Pe={class:"stat-item"},je={class:"stat-number warning"},Le=W(()=>p("div",{class:"stat-label"},"待推送",-1)),We={class:"stat-item"},Ye={class:"stat-number info"},qe=W(()=>p("div",{class:"stat-label"},"成功率",-1)),Ae={class:"action-buttons"},Be={class:"pagination-container"},Je={key:0,class:"detail-content"},Ke=W(()=>p("h4",null,"消息标题：",-1)),Ge=W(()=>p("h4",null,"消息内容：",-1)),Qe={class:"message-content"},Xe=W(()=>p("h4",null,"响应消息：",-1)),Ze={class:"response-content"},$e={class:"related-data"},et={class:"dialog-footer"},tt={class:"statistics-content"},at={ref:"chartContainer",style:{height:"400px",width:"100%"}},lt={class:"dialog-footer"};function ot(a,t,n,c,o,i){const b=d("el-option"),_=d("el-select"),g=d("el-form-item"),m=d("el-col"),V=d("el-date-picker"),y=d("el-row"),D=d("el-button"),I=d("el-form"),T=d("el-card"),v=d("el-table-column"),C=d("el-tag"),E=d("el-table"),P=d("el-pagination"),M=d("el-descriptions-item"),H=d("el-descriptions"),x=d("el-divider"),j=d("el-alert"),u=d("el-dialog"),z=X("loading");return h(),k("div",Me,[e(T,{class:"search-card",shadow:"never"},{default:l(()=>[e(I,{model:o.searchForm,ref:"searchFormRef","label-width":"80px","label-position":"right",class:"search-form"},{default:l(()=>[e(y,{gutter:20},{default:l(()=>[e(m,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(g,{label:"推送任务"},{default:l(()=>[e(_,{modelValue:o.searchForm.task_id,"onUpdate:modelValue":t[0]||(t[0]=s=>o.searchForm.task_id=s),placeholder:"请选择推送任务",clearable:"",filterable:"",class:"form-control"},{default:l(()=>[(h(!0),k(U,null,L(o.taskOptions,s=>(h(),S(b,{key:s.id,label:s.task_name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(g,{label:"推送状态"},{default:l(()=>[e(_,{modelValue:o.searchForm.push_status,"onUpdate:modelValue":t[1]||(t[1]=s=>o.searchForm.push_status=s),placeholder:"请选择推送状态",clearable:"",class:"form-control"},{default:l(()=>[e(b,{label:"待推送",value:"pending"}),e(b,{label:"推送中",value:"sending"}),e(b,{label:"成功",value:"success"}),e(b,{label:"失败",value:"failed"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(g,{label:"推送时间"},{default:l(()=>[e(V,{modelValue:o.dateRange,"onUpdate:modelValue":t[2]||(t[2]=s=>o.dateRange=s),type:"datetimerange","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",class:"form-control date-range-picker"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,{gutter:20},{default:l(()=>[e(m,{xs:24,sm:12,md:8,lg:24,class:"search-buttons-col"},{default:l(()=>[e(g,null,{default:l(()=>[p("div",Fe,[e(D,{type:"primary",onClick:i.handleSearch,icon:i.searchIcon},{default:l(()=>[r("搜索")]),_:1},8,["onClick","icon"]),e(D,{onClick:i.handleReset,icon:i.refreshIcon},{default:l(()=>[r("重置")]),_:1},8,["onClick","icon"]),e(D,{type:"info",onClick:i.handleStatistics,icon:i.dataAnalysisIcon},{default:l(()=>[r("统计分析")]),_:1},8,["onClick","icon"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(y,{gutter:20,class:"stats-cards"},{default:l(()=>[e(m,{span:6},{default:l(()=>[e(T,{shadow:"hover"},{default:l(()=>[p("div",Ie,[p("div",Re,w(o.statistics.total_success),1),Ee])]),_:1})]),_:1}),e(m,{span:6},{default:l(()=>[e(T,{shadow:"hover"},{default:l(()=>[p("div",He,[p("div",Oe,w(o.statistics.total_failed),1),Ne])]),_:1})]),_:1}),e(m,{span:6},{default:l(()=>[e(T,{shadow:"hover"},{default:l(()=>[p("div",Pe,[p("div",je,w(o.statistics.total_pending),1),Le])]),_:1})]),_:1}),e(m,{span:6},{default:l(()=>[e(T,{shadow:"hover"},{default:l(()=>[p("div",We,[p("div",Ye,w(o.statistics.success_rate)+"%",1),qe])]),_:1})]),_:1})]),_:1}),e(T,{class:"table-card",shadow:"never"},{default:l(()=>[Q((h(),S(E,{data:o.tableData,border:"",stripe:"",height:"500",style:{width:"100%"}},{default:l(()=>[e(v,{prop:"task_name",label:"推送任务",width:"150","show-overflow-tooltip":""}),e(v,{prop:"config_type",label:"推送方式",width:"100",align:"center"},{default:l(({row:s})=>[e(C,{size:"small",type:i.getConfigTypeTagType(s.config_type)},{default:l(()=>[r(w(i.getConfigTypeName(s.config_type)),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"message_title",label:"消息标题",width:"200","show-overflow-tooltip":""}),e(v,{prop:"push_status",label:"推送状态",width:"100",align:"center"},{default:l(({row:s})=>[e(C,{type:i.getStatusTagType(s.push_status)},{default:l(()=>[r(w(i.getStatusName(s.push_status)),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"response_code",label:"响应码",width:"80",align:"center"},{default:l(({row:s})=>[e(C,{size:"small",type:s.response_code>=200&&s.response_code<300?"success":"danger"},{default:l(()=>[r(w(s.response_code||"-"),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"retry_count",label:"重试次数",width:"80",align:"center"}),e(v,{prop:"push_time",label:"推送时间",width:"160",align:"center"},{default:l(({row:s})=>[r(w(i.formatDateTime(s.push_time)),1)]),_:1}),e(v,{prop:"success_time",label:"成功时间",width:"160",align:"center"},{default:l(({row:s})=>[r(w(i.formatDateTime(s.success_time)),1)]),_:1}),e(v,{label:"操作",width:"200",align:"center",fixed:"right"},{default:l(({row:s})=>[p("div",Ae,[e(D,{type:"primary",size:"small",onClick:F=>i.handleViewDetail(s),icon:i.viewIcon},{default:l(()=>[r(" 详情 ")]),_:2},1032,["onClick","icon"]),e(D,{type:"warning",size:"small",onClick:F=>i.handleRetry(s),loading:s.retrying,disabled:s.push_status==="success",icon:i.refreshRightIcon},{default:l(()=>[r(" 重试 ")]),_:2},1032,["onClick","loading","disabled","icon"])])]),_:1})]),_:1},8,["data"])),[[z,o.loading]]),p("div",Be,[e(P,{"current-page":o.pagination.page,"onUpdate:currentPage":t[3]||(t[3]=s=>o.pagination.page=s),"page-size":o.pagination.pageSize,"onUpdate:pageSize":t[4]||(t[4]=s=>o.pagination.pageSize=s),total:o.pagination.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:i.handleSizeChange,onCurrentChange:i.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),e(u,{title:"推送详情",modelValue:o.detailVisible,"onUpdate:modelValue":t[6]||(t[6]=s=>o.detailVisible=s),width:"800px"},{footer:l(()=>[p("span",et,[e(D,{onClick:t[5]||(t[5]=s=>o.detailVisible=!1)},{default:l(()=>[r("关闭")]),_:1})])]),default:l(()=>[o.detailData?(h(),k("div",Je,[e(H,{column:2,border:""},{default:l(()=>[e(M,{label:"推送任务"},{default:l(()=>[r(w(o.detailData.task_name),1)]),_:1}),e(M,{label:"推送方式"},{default:l(()=>[e(C,{type:i.getConfigTypeTagType(o.detailData.config_type)},{default:l(()=>[r(w(i.getConfigTypeName(o.detailData.config_type)),1)]),_:1},8,["type"])]),_:1}),e(M,{label:"推送状态"},{default:l(()=>[e(C,{type:i.getStatusTagType(o.detailData.push_status)},{default:l(()=>[r(w(i.getStatusName(o.detailData.push_status)),1)]),_:1},8,["type"])]),_:1}),e(M,{label:"响应码"},{default:l(()=>[e(C,{type:o.detailData.response_code>=200&&o.detailData.response_code<300?"success":"danger"},{default:l(()=>[r(w(o.detailData.response_code||"-"),1)]),_:1},8,["type"])]),_:1}),e(M,{label:"重试次数"},{default:l(()=>[r(w(o.detailData.retry_count)+" / "+w(o.detailData.max_retry),1)]),_:1}),e(M,{label:"推送时间"},{default:l(()=>[r(w(i.formatDateTime(o.detailData.push_time)),1)]),_:1}),e(M,{label:"成功时间"},{default:l(()=>[r(w(i.formatDateTime(o.detailData.success_time)||"-"),1)]),_:1})]),_:1}),e(x,null,{default:l(()=>[r("消息内容")]),_:1}),e(y,{gutter:20},{default:l(()=>[e(m,{span:24},{default:l(()=>[Ke,p("p",null,w(o.detailData.message_title),1)]),_:1})]),_:1}),e(y,{gutter:20},{default:l(()=>[e(m,{span:24},{default:l(()=>[Ge,p("pre",Qe,w(o.detailData.message_content),1)]),_:1})]),_:1}),e(x,null,{default:l(()=>[r("响应信息")]),_:1}),e(y,{gutter:20},{default:l(()=>[e(m,{span:24},{default:l(()=>[Xe,p("pre",Ze,w(i.formatJson(o.detailData.response_message)),1)]),_:1})]),_:1}),o.detailData.error_message?(h(),k(U,{key:0},[e(x,null,{default:l(()=>[r("错误信息")]),_:1}),e(y,{gutter:20},{default:l(()=>[e(m,{span:24},{default:l(()=>[e(j,{title:o.detailData.error_message,type:"error",closable:!1,"show-icon":""},null,8,["title"])]),_:1})]),_:1})],64)):R("",!0),o.detailData.related_data?(h(),k(U,{key:1},[e(x,null,{default:l(()=>[r("关联数据")]),_:1}),e(y,{gutter:20},{default:l(()=>[e(m,{span:24},{default:l(()=>[p("pre",$e,w(i.formatJson(o.detailData.related_data)),1)]),_:1})]),_:1})],64)):R("",!0)])):R("",!0)]),_:1},8,["modelValue"]),e(u,{title:"推送统计分析",modelValue:o.statisticsVisible,"onUpdate:modelValue":t[8]||(t[8]=s=>o.statisticsVisible=s),width:"800px"},{footer:l(()=>[p("span",lt,[e(D,{onClick:t[7]||(t[7]=s=>o.statisticsVisible=!1)},{default:l(()=>[r("关闭")]),_:1})])]),default:l(()=>[p("div",tt,[p("div",at,null,512)])]),_:1},8,["modelValue"])])}const st=J(ze,[["render",ot],["__scopeId","data-v-119ceb86"]]),it={name:"TemplateManagement",components:{Search:N,Refresh:O,Plus:B,Edit:A,Delete:q,View:G},data(){return{searchForm:{template_name:"",template_type:""},tableData:[],loading:!1,pagination:{page:1,pageSize:10,total:0},dialogVisible:!1,dialogTitle:"",isEdit:!1,submitting:!1,formData:{template_name:"",template_type:"change_notification",message_format:"text",title_template:"",content_template:"",variables:{},is_default:!1,description:""},formRules:{template_name:[{required:!0,message:"请输入模板名称",trigger:"blur"}],template_type:[{required:!0,message:"请选择模板类型",trigger:"change"}],message_format:[{required:!0,message:"请选择消息格式",trigger:"change"}],content_template:[{required:!0,message:"请输入内容模板",trigger:"blur"}]},showVariablesDialog:!1,variables:[],previewVisible:!1,previewData:null,sampleData:{change_id:"BG-20250117-001",change_name:"系统升级维护",change_personnel:"张三,李四",planned_time:"2025-01-17 02:00:00",change_level:"中风险",change_system:"核心交易系统"}}},computed:{hasVariables(){return Object.keys(this.formData.variables||{}).length>0},previewTitle(){var a;return(a=this.previewData)!=null&&a.title_template?this.replaceVariables(this.previewData.title_template,this.sampleData):""},previewContent(){var a;return(a=this.previewData)!=null&&a.content_template?this.replaceVariables(this.previewData.content_template,this.sampleData):""},searchIcon(){return N},refreshIcon(){return O},plusIcon(){return B},deleteIcon(){return q},editIcon(){return A},viewIcon(){return G}},mounted(){this.loadData()},methods:{async loadData(){this.loading=!0;try{const a=await this.$axios.post("/api/get_msg_push_templates",{...this.searchForm,page:this.pagination.page,pageSize:this.pagination.pageSize});a.data.success?(this.tableData=a.data.data,this.pagination.total=a.data.total):f.error(a.data.message||"获取数据失败")}catch(a){console.error("加载数据失败:",a),f.error("获取数据失败")}finally{this.loading=!1}},refreshData(){this.loadData()},handleSearch(){this.pagination.page=1,this.loadData()},handleReset(){this.searchForm={template_name:"",template_type:""},this.pagination.page=1,this.$nextTick(()=>{var a;(a=this.$refs.searchFormRef)==null||a.clearValidate()}),this.loadData()},handleSizeChange(a){this.pagination.pageSize=a,this.pagination.page=1,this.loadData()},handleCurrentChange(a){this.pagination.page=a,this.loadData()},handleAdd(){this.isEdit=!1,this.dialogTitle="新增消息模板",this.resetFormData(),this.dialogVisible=!0},handleEdit(a){this.isEdit=!0,this.dialogTitle="编辑消息模板",this.formData={...a},this.formData.variables=a.variables||{},this.variables=this.objectToVariables(this.formData.variables),this.dialogVisible=!0},async handleDelete(a){if(a.is_default){f.warning("默认模板不能删除");return}try{await Z.confirm(`确定要删除消息模板"${a.template_name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await this.$axios.post("/api/delete_msg_push_template",{id:a.id});t.data.success?(f.success("删除成功"),this.loadData()):f.error(t.data.message||"删除失败")}catch(t){t!=="cancel"&&(console.error("删除失败:",t),f.error("删除失败"))}},handlePreview(a){this.previewData={...a},this.previewVisible=!0},handlePreviewForm(){if(!this.formData.content_template){f.warning("请先输入内容模板");return}this.previewData={...this.formData},this.previewVisible=!0},async handleSubmit(){try{await this.$refs.formRef.validate()}catch{return}this.submitting=!0;try{const a=this.isEdit?"/api/update_msg_push_template":"/api/add_msg_push_template",t={...this.formData};t.variables=this.variablesToObject(this.variables);const n=await this.$axios.post(a,t);n.data.success?(f.success(this.isEdit?"更新成功":"新增成功"),this.dialogVisible=!1,this.loadData()):f.error(n.data.message||"操作失败")}catch(a){console.error("提交失败:",a),f.error("操作失败")}finally{this.submitting=!1}},handleDialogClose(){var a;(a=this.$refs.formRef)==null||a.clearValidate(),this.resetFormData()},resetFormData(){this.formData={template_name:"",template_type:"change_notification",message_format:"text",title_template:"",content_template:"",variables:{},is_default:!1,description:""},this.variables=[]},addVariable(){this.variables.push({key:"",description:""})},removeVariable(a){this.variables.splice(a,1)},saveVariables(){this.formData.variables=this.variablesToObject(this.variables),this.showVariablesDialog=!1},objectToVariables(a){return Object.entries(a||{}).map(([t,n])=>({key:t,description:n}))},variablesToObject(a){const t={};return a.forEach(n=>{n.key&&n.description&&(t[n.key]=n.description)}),t},replaceVariables(a,t){let n=a;for(const[c,o]of Object.entries(t)){const i=new RegExp(`\\$\\{${c}\\}`,"g");n=n.replace(i,o||"")}return n},getTemplateTypeName(a){return{change_notification:"变更通知",event_notification:"事件通知",custom:"自定义通知"}[a]||a},getTemplateTypeTagType(a){return{change_notification:"primary",event_notification:"warning",custom:"info"}[a]||"default"},getFormatName(a){return{text:"文本",html:"HTML",markdown:"Markdown",json:"JSON"}[a]||a},getFormatTagType(a){return{text:"success",html:"warning",markdown:"primary",json:"info"}[a]||"default"},formatDateTime(a){return a?new Date(a).toLocaleString("zh-CN"):""}}},Y=a=>(ee("data-v-73a1c737"),a=a(),te(),a),nt={class:"template-management"},rt={class:"button-container"},dt={key:1},ut={class:"action-buttons"},mt={class:"pagination-container"},ct=Y(()=>p("span",{style:{"margin-left":"10px",color:"#999","font-size":"12px"}}," (每种类型只能有一个默认模板) ",-1)),pt=Y(()=>p("div",{class:"template-help"},[p("small",null,"可用变量：${change_id}, ${change_name}, ${change_personnel}, ${planned_time} 等")],-1)),_t=Y(()=>p("div",{class:"template-help"},[p("small",null," 变更通知可用变量：${change_id}(变更编号), ${change_name}(变更名称), ${change_personnel}(变更人员), ${planned_time}(计划时间), ${change_level}(变更级别), ${change_system}(变更系统) ")],-1)),ht={key:0,style:{"margin-left":"10px",color:"#67c23a"}},ft={class:"dialog-footer"},gt={class:"variables-config"},bt={class:"dialog-footer"},yt={key:0,class:"preview-content"},vt=Y(()=>p("h4",null,"标题模板：",-1)),Dt={class:"template-preview"},wt=Y(()=>p("h4",null,"内容模板：",-1)),kt={class:"template-preview"},Vt=Y(()=>p("h4",null,"预览标题：",-1)),Ct={class:"preview-result"},Tt=Y(()=>p("h4",null,"预览内容：",-1)),xt={class:"preview-result"},St={key:0},Ut=["innerHTML"],zt={key:2},Mt={class:"dialog-footer"};function Ft(a,t,n,c,o,i){const b=d("el-input"),_=d("el-form-item"),g=d("el-col"),m=d("el-option"),V=d("el-select"),y=d("el-row"),D=d("el-button"),I=d("el-form"),T=d("el-card"),v=d("el-table-column"),C=d("el-tag"),E=d("el-table"),P=d("el-pagination"),M=d("el-switch"),H=d("el-dialog"),x=d("el-descriptions-item"),j=d("el-descriptions"),u=d("el-divider"),z=X("loading");return h(),k("div",nt,[e(T,{class:"search-card",shadow:"never"},{default:l(()=>[e(I,{model:o.searchForm,ref:"searchFormRef","label-width":"80px","label-position":"right",class:"search-form"},{default:l(()=>[e(y,{gutter:20},{default:l(()=>[e(g,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(_,{label:"模板名称"},{default:l(()=>[e(b,{modelValue:o.searchForm.template_name,"onUpdate:modelValue":t[0]||(t[0]=s=>o.searchForm.template_name=s),placeholder:"请输入模板名称",clearable:"",onKeyup:$(i.handleSearch,["enter"]),class:"form-control"},null,8,["modelValue","onKeyup"])]),_:1})]),_:1}),e(g,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[e(_,{label:"模板类型"},{default:l(()=>[e(V,{modelValue:o.searchForm.template_type,"onUpdate:modelValue":t[1]||(t[1]=s=>o.searchForm.template_type=s),placeholder:"请选择模板类型",clearable:"",class:"form-control"},{default:l(()=>[e(m,{label:"变更通知",value:"change_notification"}),e(m,{label:"事件通知",value:"event_notification"}),e(m,{label:"自定义通知",value:"custom"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,{gutter:20},{default:l(()=>[e(g,{xs:24,sm:12,md:8,lg:24,class:"search-buttons-col"},{default:l(()=>[e(_,null,{default:l(()=>[p("div",rt,[e(D,{type:"primary",onClick:i.handleSearch,icon:i.searchIcon},{default:l(()=>[r("搜索")]),_:1},8,["onClick","icon"]),e(D,{onClick:i.handleReset,icon:i.refreshIcon},{default:l(()=>[r("重置")]),_:1},8,["onClick","icon"]),e(D,{type:"success",onClick:i.handleAdd,icon:i.plusIcon},{default:l(()=>[r("新增模板")]),_:1},8,["onClick","icon"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(T,{class:"table-card",shadow:"never"},{default:l(()=>[Q((h(),S(E,{data:o.tableData,border:"",stripe:"",height:"600",style:{width:"100%"}},{default:l(()=>[e(v,{prop:"template_name",label:"模板名称",width:"180","show-overflow-tooltip":""}),e(v,{prop:"template_type",label:"模板类型",width:"120",align:"center"},{default:l(({row:s})=>[e(C,{type:i.getTemplateTypeTagType(s.template_type)},{default:l(()=>[r(w(i.getTemplateTypeName(s.template_type)),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"message_format",label:"消息格式",width:"100",align:"center"},{default:l(({row:s})=>[e(C,{size:"small",type:i.getFormatTagType(s.message_format)},{default:l(()=>[r(w(i.getFormatName(s.message_format)),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"title_template",label:"标题模板",width:"200","show-overflow-tooltip":""}),e(v,{prop:"is_default",label:"默认模板",width:"100",align:"center"},{default:l(({row:s})=>[s.is_default?(h(),S(C,{key:0,type:"success",size:"small"},{default:l(()=>[r(" 默认 ")]),_:1})):(h(),k("span",dt,"-"))]),_:1}),e(v,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""}),e(v,{prop:"created_at",label:"创建时间",width:"160",align:"center"},{default:l(({row:s})=>[r(w(i.formatDateTime(s.created_at)),1)]),_:1}),e(v,{label:"操作",width:"280",align:"center",fixed:"right"},{default:l(({row:s})=>[p("div",ut,[e(D,{type:"info",size:"small",onClick:F=>i.handlePreview(s),icon:i.viewIcon},{default:l(()=>[r(" 预览 ")]),_:2},1032,["onClick","icon"]),e(D,{type:"warning",size:"small",onClick:F=>i.handleEdit(s),icon:i.editIcon},{default:l(()=>[r(" 编辑 ")]),_:2},1032,["onClick","icon"]),e(D,{type:"danger",size:"small",onClick:F=>i.handleDelete(s),icon:i.deleteIcon,disabled:s.is_default},{default:l(()=>[r(" 删除 ")]),_:2},1032,["onClick","icon","disabled"])])]),_:1})]),_:1},8,["data"])),[[z,o.loading]]),p("div",mt,[e(P,{"current-page":o.pagination.page,"onUpdate:currentPage":t[2]||(t[2]=s=>o.pagination.page=s),"page-size":o.pagination.pageSize,"onUpdate:pageSize":t[3]||(t[3]=s=>o.pagination.pageSize=s),total:o.pagination.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:i.handleSizeChange,onCurrentChange:i.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),e(H,{title:o.dialogTitle,modelValue:o.dialogVisible,"onUpdate:modelValue":t[13]||(t[13]=s=>o.dialogVisible=s),width:"900px",onClose:i.handleDialogClose},{footer:l(()=>[p("span",ft,[e(D,{onClick:t[12]||(t[12]=s=>o.dialogVisible=!1)},{default:l(()=>[r("取消")]),_:1}),e(D,{onClick:i.handlePreviewForm,icon:i.viewIcon},{default:l(()=>[r("预览")]),_:1},8,["onClick","icon"]),e(D,{type:"primary",onClick:i.handleSubmit,loading:o.submitting},{default:l(()=>[r(" 确定 ")]),_:1},8,["onClick","loading"])])]),default:l(()=>[e(I,{model:o.formData,rules:o.formRules,ref:"formRef","label-width":"120px"},{default:l(()=>[e(y,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"模板名称",prop:"template_name"},{default:l(()=>[e(b,{modelValue:o.formData.template_name,"onUpdate:modelValue":t[4]||(t[4]=s=>o.formData.template_name=s),placeholder:"请输入模板名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"模板类型",prop:"template_type"},{default:l(()=>[e(V,{modelValue:o.formData.template_type,"onUpdate:modelValue":t[5]||(t[5]=s=>o.formData.template_type=s),placeholder:"请选择模板类型"},{default:l(()=>[e(m,{label:"变更通知",value:"change_notification"}),e(m,{label:"事件通知",value:"event_notification"}),e(m,{label:"自定义通知",value:"custom"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(y,{gutter:20},{default:l(()=>[e(g,{span:12},{default:l(()=>[e(_,{label:"消息格式",prop:"message_format"},{default:l(()=>[e(V,{modelValue:o.formData.message_format,"onUpdate:modelValue":t[6]||(t[6]=s=>o.formData.message_format=s),placeholder:"请选择消息格式"},{default:l(()=>[e(m,{label:"纯文本",value:"text"}),e(m,{label:"HTML",value:"html"}),e(m,{label:"Markdown",value:"markdown"}),e(m,{label:"JSON",value:"json"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:l(()=>[e(_,{label:"默认模板"},{default:l(()=>[e(M,{modelValue:o.formData.is_default,"onUpdate:modelValue":t[7]||(t[7]=s=>o.formData.is_default=s)},null,8,["modelValue"]),ct]),_:1})]),_:1})]),_:1}),e(_,{label:"标题模板",prop:"title_template"},{default:l(()=>[e(b,{modelValue:o.formData.title_template,"onUpdate:modelValue":t[8]||(t[8]=s=>o.formData.title_template=s),placeholder:"请输入标题模板，支持变量：${变量名}"},null,8,["modelValue"]),pt]),_:1}),e(_,{label:"内容模板",prop:"content_template"},{default:l(()=>[e(b,{modelValue:o.formData.content_template,"onUpdate:modelValue":t[9]||(t[9]=s=>o.formData.content_template=s),type:"textarea",rows:10,placeholder:"请输入内容模板，支持变量替换"},null,8,["modelValue"]),_t]),_:1}),e(_,{label:"变量定义"},{default:l(()=>[e(D,{onClick:t[10]||(t[10]=s=>o.showVariablesDialog=!0),size:"small"},{default:l(()=>[r(" 配置模板变量 ")]),_:1}),i.hasVariables?(h(),k("span",ht," 已配置 "+w(Object.keys(o.formData.variables||{}).length)+" 个变量 ",1)):R("",!0)]),_:1}),e(_,{label:"描述"},{default:l(()=>[e(b,{modelValue:o.formData.description,"onUpdate:modelValue":t[11]||(t[11]=s=>o.formData.description=s),type:"textarea",rows:3,placeholder:"请输入模板描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue","onClose"]),e(H,{title:"配置模板变量",modelValue:o.showVariablesDialog,"onUpdate:modelValue":t[15]||(t[15]=s=>o.showVariablesDialog=s),width:"600px"},{footer:l(()=>[p("span",bt,[e(D,{onClick:t[14]||(t[14]=s=>o.showVariablesDialog=!1)},{default:l(()=>[r("取消")]),_:1}),e(D,{type:"primary",onClick:i.saveVariables},{default:l(()=>[r("确定")]),_:1},8,["onClick"])])]),default:l(()=>[p("div",gt,[e(D,{type:"primary",size:"small",onClick:i.addVariable,style:{"margin-bottom":"15px"}},{default:l(()=>[r(" 添加变量 ")]),_:1},8,["onClick"]),(h(!0),k(U,null,L(o.variables,(s,F)=>(h(),k("div",{key:F,class:"variable-item"},[e(b,{modelValue:s.key,"onUpdate:modelValue":K=>s.key=K,placeholder:"变量名(如: change_id)",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),e(b,{modelValue:s.description,"onUpdate:modelValue":K=>s.description=K,placeholder:"变量描述(如: 变更编号)",style:{width:"200px","margin-right":"10px"}},null,8,["modelValue","onUpdate:modelValue"]),e(D,{type:"danger",size:"small",onClick:K=>i.removeVariable(F),icon:i.deleteIcon},null,8,["onClick","icon"])]))),128))])]),_:1},8,["modelValue"]),e(H,{title:"模板预览",modelValue:o.previewVisible,"onUpdate:modelValue":t[17]||(t[17]=s=>o.previewVisible=s),width:"700px"},{footer:l(()=>[p("span",Mt,[e(D,{onClick:t[16]||(t[16]=s=>o.previewVisible=!1)},{default:l(()=>[r("关闭")]),_:1})])]),default:l(()=>[o.previewData?(h(),k("div",yt,[e(j,{column:1,border:""},{default:l(()=>[e(x,{label:"模板名称"},{default:l(()=>[r(w(o.previewData.template_name),1)]),_:1}),e(x,{label:"模板类型"},{default:l(()=>[e(C,{type:i.getTemplateTypeTagType(o.previewData.template_type)},{default:l(()=>[r(w(i.getTemplateTypeName(o.previewData.template_type)),1)]),_:1},8,["type"])]),_:1}),e(x,{label:"消息格式"},{default:l(()=>[e(C,{type:i.getFormatTagType(o.previewData.message_format)},{default:l(()=>[r(w(i.getFormatName(o.previewData.message_format)),1)]),_:1},8,["type"])]),_:1})]),_:1}),e(u,null,{default:l(()=>[r("模板内容")]),_:1}),vt,p("div",Dt,w(o.previewData.title_template),1),wt,p("div",kt,[p("pre",null,w(o.previewData.content_template),1)]),e(u,null,{default:l(()=>[r("效果预览")]),_:1}),Vt,p("div",Ct,w(i.previewTitle),1),Tt,p("div",xt,[o.previewData.message_format==="text"||o.previewData.message_format==="markdown"?(h(),k("pre",St,w(i.previewContent),1)):o.previewData.message_format==="html"?(h(),k("div",{key:1,innerHTML:i.previewContent},null,8,Ut)):(h(),k("pre",zt,w(i.previewContent),1))]),o.previewData.variables?(h(),k(U,{key:0},[e(u,null,{default:l(()=>[r("可用变量")]),_:1}),e(E,{data:Object.entries(o.previewData.variables),size:"small"},{default:l(()=>[e(v,{prop:"0",label:"变量名",width:"150"}),e(v,{prop:"1",label:"变量描述"})]),_:1},8,["data"])],64)):R("",!0)])):R("",!0)]),_:1},8,["modelValue"])])}const It=J(it,[["render",Ft],["__scopeId","data-v-73a1c737"]]),Rt={name:"MsgPushManagement",components:{ConfigManagement:fe,TaskManagement:Ue,HistoryManagement:st,TemplateManagement:It},data(){return{activeTab:"config"}},methods:{handleTabClick(a){var c,o;const n={config:"configRef",task:"taskRef",history:"historyRef",template:"templateRef"}[a.name];n&&this.$refs[n]&&((o=(c=this.$refs[n]).refreshData)==null||o.call(c))}}},Et={class:"msg-push-management"};function Ht(a,t,n,c,o,i){const b=d("ConfigManagement"),_=d("el-tab-pane"),g=d("TaskManagement"),m=d("HistoryManagement"),V=d("TemplateManagement"),y=d("el-tabs");return h(),k("div",Et,[e(y,{modelValue:o.activeTab,"onUpdate:modelValue":t[0]||(t[0]=D=>o.activeTab=D),type:"card",onTabClick:i.handleTabClick},{default:l(()=>[e(_,{label:"推送配置",name:"config"},{default:l(()=>[e(b,{ref:"configRef"},null,512)]),_:1}),e(_,{label:"推送任务",name:"task"},{default:l(()=>[e(g,{ref:"taskRef"},null,512)]),_:1}),e(_,{label:"推送历史",name:"history"},{default:l(()=>[e(m,{ref:"historyRef"},null,512)]),_:1}),e(_,{label:"消息模板",name:"template"},{default:l(()=>[e(V,{ref:"templateRef"},null,512)]),_:1})]),_:1},8,["modelValue","onTabClick"])])}const Nt=J(Rt,[["render",Ht],["__scopeId","data-v-c78a0d16"]]);export{Nt as default};
