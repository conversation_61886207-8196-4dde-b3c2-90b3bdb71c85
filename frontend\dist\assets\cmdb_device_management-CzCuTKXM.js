import{_ as B,A as N,v as G,B as O,c as _,a as l,f as v,w as o,b as h,F as b,l as g,h as V,C as j,D as H,m as f,x as J,t as x,e as s}from"./index-MGgq8mV5.js";import{u as I,w as K}from"./xlsx-DH6WiNtO.js";import{F as Q}from"./FileSaver.min-Cr9SGvul.js";const W={components:{Plus:O,Search:G,Download:N},data(){var n,a,c;return{userArr:[],loading:!1,devicetypes:[],productionattributes:[],datacenters:[],operationstatus:[],importances:[],usersList:[],hasDeletePermission:(n=localStorage.getItem("role_code"))==null?void 0:n.includes("D"),hasUpdatePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("U"),hasInsertPermission:(c=localStorage.getItem("role_code"))==null?void 0:c.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{management_ip:"",out_of_band_management:null,hostname:null,admin1:null,admin2:null,device_type:null,production_attributes:null,data_center:[],operation_status:null,importance:null,is_innovative_tech:null,is_monitored:null,online_status:null,year_category:null,serial_number:null,total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,management_ip:null,out_of_band_management:null,hostname:null,function_purpose:null,admin1:null,admin2:null,device_type:null,production_attributes:null,data_center:null,operation_status:null,importance:"general",asset_number:null,purchase_date:null,maintenance_years:null,maintenance_end_date:null,serial_number:null,model:null,version:null,cpu_model:null,is_innovative_tech:null,is_monitored:null,monitoring_ip:null,architecture_mode:null,is_single_point:null,managed_addresses:null,remarks:null,year_category:null,in_monitoring_list:null,pre_monitoring_verified:null,inspection:null,monitoring_requirement:!0,monitoring_requirement_description:null},rules:{management_ip:[{required:!0,message:"请输入管理IP",trigger:"blur"},{pattern:/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"}],hostname:[{required:!0,message:"请输入主机名",trigger:"blur"},{min:2,max:50,message:"主机名长度应在2-50个字符之间",trigger:"blur"}],function_purpose:[{required:!0,message:"请输入功能用途",trigger:"blur"}],admin1:[{required:!0,message:"请选择管理员1",trigger:"change"}],device_type:[{required:!0,message:"请选择设备类型",trigger:"change"}],production_attributes:[{required:!0,message:"请选择生产属性",trigger:"change"}],data_center:[{required:!0,message:"请选择所属机房",trigger:"change"}],operation_status:[{required:!0,message:"请选择生命周期",trigger:"change"}],importance:[{required:!0,message:"请选择重要性",trigger:"change"}],is_innovative_tech:[{required:!0,message:"请选择是否信创",trigger:"change"}],monitoring_requirement:[{required:!0,message:"请选择需要监控",trigger:"change"}],monitoring_requirement_description:[{required:!1,validator:(U,t,p)=>{this.formData.monitoring_requirement===!1&&(!t||t.trim()==="")?p(new Error('当需要监控为"否"时，不监控原因为必填项')):p()},trigger:"blur"}],managed_addresses:[{required:!1,validator:(U,t,p)=>{if(this.formData.is_single_point==="否")if(!t)p(new Error("当设备不是单点时，互备主机IP为必填项"));else{const d=t.split(","),r=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;d.filter(u=>!r.test(u.trim())).length>0?p(new Error("请输入正确的IP地址格式，多个IP请用英文逗号分隔")):p()}else p()},trigger:"blur"}]}}},mounted(){this.$route.query.search_ip&&(this.search.management_ip=this.$route.query.search_ip),this.loadData(),this.getDatadict("B","devicetypes"),this.getDatadict("C","productionattributes"),this.getDatadict("A","datacenters"),this.getDatadict("D","operationstatus"),this.getDatadict("importance","importances"),this.loadUsersList(),this.$route.query.from_discovery==="true"&&this.$nextTick(()=>{this.handleAddFromDiscovery()})},methods:{getLifecycleTagType(n){switch(n){case"正常":return"success";case"故障":return"danger";case"闲置":return"info";case"报废":return"info";case"预报废":return"warning";default:return n&&n.includes("正常")?"success":n&&n.includes("故障")?"danger":n&&n.includes("闲置")||n&&n.includes("报废")&&!n.includes("预")?"info":n&&n.includes("预报废")?"warning":"info"}},getImportanceTagType(n){switch(n){case"重要":return"danger";case"一般":return"info";default:return"info"}},handlePageChange(n){this.search.currentPage=n,this.loadData()},handlePageSizeChange(n){this.search.pageSize=parseInt(n),this.search.currentPage=1,this.loadData()},handleSortChange({prop:n,order:a}){this.search.sortProp=n,this.search.sortOrder=a==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const n=await this.$axios.post("/api/get_cmdb_device_management",this.search);this.userArr=n.data.msg,this.search.total=n.data.total}catch(n){console.error("数据加载失败:",n),this.$message.error("数据加载失败")}finally{this.loading=!1}},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){var n,a;try{const c={...this.formData,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/add_cmdb_device_management",c),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(c){console.error("添加失败:",c),this.$message.error(((a=(n=c.response)==null?void 0:n.data)==null?void 0:a.msg)||"添加失败")}},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){var n,a;try{const c={...this.formData,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/update_cmdb_device_management",c),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(c){console.error("更新失败:",c),this.$message.error(((a=(n=c.response)==null?void 0:n.data)==null?void 0:a.msg)||"更新失败")}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_device_management",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(n){console.error("删除失败:",n),this.$message.error("删除失败")}},resetSearch(){this.search={management_ip:"",out_of_band_management:null,hostname:null,admin1:null,admin2:null,device_type:null,production_attributes:null,data_center:[],operation_status:null,is_innovative_tech:null,is_monitored:null,online_status:null,year_category:null,serial_number:null,monitoring_requirement:null,total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async getDatadict(n,a){var c,U,t,p,d;try{console.log("=== 前端发送数据字典请求 ==="),console.log("字典代码:",n),console.log("目标数组:",a);const r={withCredentials:!0},i=n.length<=2?{dict_code:n}:{dict_type:n};console.log("请求配置:",r),console.log("请求数据:",i);const u=await this.$axios.post("/api/get_cmdb_data_dictionary",i,r);console.log("响应数据:",u.data),this[a]=u.data.msg}catch(r){console.error("数据字典加载失败详细信息:",{error:r,message:r.message,response:r.response,status:(c=r.response)==null?void 0:c.status,statusText:(U=r.response)==null?void 0:U.statusText,data:(t=r.response)==null?void 0:t.data}),this.$message.error(`数据字典加载失败: ${((d=(p=r.response)==null?void 0:p.data)==null?void 0:d.msg)||r.message}`)}},async loadUsersList(){try{const n=await this.$axios.post("/api/get_all_users_real_name");this.usersList=n.data.msg,console.log("用户列表加载成功:",this.usersList)}catch(n){console.error("用户列表加载失败:",n),this.$message.error("用户列表加载失败")}},handleMonitoringRequirementChange(n){n===!0&&(this.formData.monitoring_requirement_description=null),this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.validateField("monitoring_requirement_description")}),this.$refs.editFormRef&&this.$nextTick(()=>{this.$refs.editFormRef.validateField("monitoring_requirement_description")})},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={is_single_point:"否",is_innovative_tech:"否",is_monitored:"否",in_monitoring_list:"否",inspection:"否",managed_addresses:"",monitoring_requirement:!0,monitoring_requirement_description:null,importance:"general"},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.validateManagedAddresses()})},handleEdit(n,a){this.dialogVisible.edit=!0,this.formData.id=a.id,this.formData.management_ip=a.management_ip,this.formData.out_of_band_management=a.out_of_band_management,this.formData.hostname=a.hostname,this.formData.function_purpose=a.function_purpose,this.formData.admin1=a.admin1,this.formData.admin2=a.admin2,this.formData.device_type=a.device_type_code||a.device_type,this.formData.production_attributes=a.production_attributes_code||a.production_attributes,this.formData.data_center=a.data_center_code||a.data_center,this.formData.operation_status=a.operation_status_code||a.operation_status,this.formData.importance=a.importance_code||a.importance,this.formData.asset_number=a.asset_number,this.formData.purchase_date=a.purchase_date,this.formData.maintenance_years=a.maintenance_years,this.formData.maintenance_end_date=a.maintenance_end_date,this.formData.serial_number=a.serial_number,this.formData.model=a.model,this.formData.version=a.version,this.formData.cpu_model=a.cpu_model,this.formData.is_innovative_tech=a.is_innovative_tech,this.formData.monitoring_requirement=a.monitoring_requirement==="是",this.formData.monitoring_requirement_description=a.monitoring_requirement_description,this.formData.is_monitored=a.is_monitored,this.formData.monitoring_ip=a.monitoring_ip,this.formData.architecture_mode=a.architecture_mode,this.formData.is_single_point=a.is_single_point,this.formData.managed_addresses=a.managed_addresses,this.formData.remarks=a.remarks,this.formData.year_category=a.year_category,this.formData.in_monitoring_list=a.in_monitoring_list,this.formData.pre_monitoring_verified=a.pre_monitoring_verified,this.formData.inspection=a.inspection,this.$refs.editFormRef&&this.$nextTick(()=>{this.$refs.editFormRef.clearValidate()})},handleDelete(n,a){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=a.id,this.formData.management_ip=a.management_ip},exportData(){const a=this.$refs.table.columns,c=a.map(u=>u.label),U=this.userArr.map(u=>a.map(k=>u[k.property])),t=[c,...U],p=I.aoa_to_sheet(t),d=I.book_new();I.book_append_sheet(d,p,"Sheet1");const r=K(d,{bookType:"xlsx",type:"array"}),i=new Blob([r],{type:"application/octet-stream"});Q.saveAs(i,"网络设备.xlsx")},handleAddFromDiscovery(){this.dialogVisible.add=!0;const{ip_address:n,hostname:a,open_ports:c}=this.$route.query;this.formData={management_ip:n||"",hostname:a||"",function_purpose:"",admin1:"",admin2:"",device_type:"",production_attributes:"",data_center:"",operation_status:"",importance:"general",is_innovative_tech:"否",is_monitored:"否",remarks:c?`开放端口: ${c}`:"",is_single_point:"否",in_monitoring_list:"否",inspection:"否"},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.$message.info("请完善资产信息并提交")}),this.$router.replace({path:this.$route.path})},validateManagedAddresses(){this.$refs.addFormRef&&this.$refs.addFormRef.validateField("managed_addresses"),this.$refs.editFormRef&&this.$refs.editFormRef.validateField("managed_addresses"),this.formData.is_single_point==="是"&&(this.formData.managed_addresses="")}}},X={class:"user-manage"},Z={class:"dialogdiv"},$={class:"dialog-footer"},ee={class:"dialogdiv"},le={class:"dialog-footer"},ae={class:"button-container"},te={class:"action-bar unified-action-bar"},oe={class:"action-bar-left"},re={class:"action-bar-right"},ne={key:0,style:{color:"#909399","font-size":"12px"}},ie={key:1},se={style:{display:"flex","white-space":"nowrap"}},de={class:"pagination"};function ue(n,a,c,U,t,p){const d=h("el-input"),r=h("el-form-item"),i=h("el-option"),u=h("el-select"),k=h("el-date-picker"),q=h("el-form"),y=h("el-button"),P=h("el-dialog"),S=h("el-alert"),D=h("el-col"),A=h("Search"),C=h("el-icon"),R=h("el-row"),F=h("el-card"),T=h("Plus"),Y=h("Download"),m=h("el-table-column"),w=h("el-tag"),z=h("el-table"),M=h("el-pagination"),L=H("loading");return s(),_("div",X,[l(P,{modelValue:t.dialogVisible.add,"onUpdate:modelValue":a[26]||(a[26]=e=>t.dialogVisible.add=e),title:"新增网络设备信息",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:o(()=>[v("div",$,[l(y,{onClick:a[25]||(a[25]=e=>t.dialogVisible.add=!1)},{default:o(()=>[V("返回")]),_:1}),l(y,{type:"primary",onClick:p.validateAndSubmitAdd},{default:o(()=>[V("确定")]),_:1},8,["onClick"])])]),default:o(()=>[v("div",Z,[l(q,{model:t.formData,rules:t.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:o(()=>[l(r,{prop:"management_ip",label:"管理IP:"},{default:o(()=>[l(d,{modelValue:t.formData.management_ip,"onUpdate:modelValue":a[0]||(a[0]=e=>t.formData.management_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),l(r,{prop:"out_of_band_management",label:"带外管理IP:"},{default:o(()=>[l(d,{modelValue:t.formData.out_of_band_management,"onUpdate:modelValue":a[1]||(a[1]=e=>t.formData.out_of_band_management=e),style:{width:"240px"},clearable:"",placeholder:"请输入带外管理IP"},null,8,["modelValue"])]),_:1}),l(r,{prop:"hostname",label:"主机名:"},{default:o(()=>[l(d,{modelValue:t.formData.hostname,"onUpdate:modelValue":a[2]||(a[2]=e=>t.formData.hostname=e),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),l(r,{prop:"function_purpose",label:"功能用途:"},{default:o(()=>[l(d,{modelValue:t.formData.function_purpose,"onUpdate:modelValue":a[3]||(a[3]=e=>t.formData.function_purpose=e),style:{width:"240px"},clearable:"",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),l(r,{prop:"admin1",label:"管理员1:"},{default:o(()=>[l(u,{modelValue:t.formData.admin1,"onUpdate:modelValue":a[4]||(a[4]=e=>t.formData.admin1=e),style:{width:"240px"},placeholder:"请选择管理员1",clearable:"",filterable:""},{default:o(()=>[(s(!0),_(b,null,g(t.usersList,e=>(s(),f(i,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"admin2",label:"管理员2:"},{default:o(()=>[l(u,{modelValue:t.formData.admin2,"onUpdate:modelValue":a[5]||(a[5]=e=>t.formData.admin2=e),style:{width:"240px"},placeholder:"请选择管理员2",clearable:"",filterable:""},{default:o(()=>[(s(!0),_(b,null,g(t.usersList,e=>(s(),f(i,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"device_type",label:"设备类型:"},{default:o(()=>[l(u,{modelValue:t.formData.device_type,"onUpdate:modelValue":a[6]||(a[6]=e=>t.formData.device_type=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择设备类型"},{default:o(()=>[(s(!0),_(b,null,g(t.devicetypes,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"production_attributes",label:"生产属性:"},{default:o(()=>[l(u,{modelValue:t.formData.production_attributes,"onUpdate:modelValue":a[7]||(a[7]=e=>t.formData.production_attributes=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择生产属性"},{default:o(()=>[(s(!0),_(b,null,g(t.productionattributes,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"data_center",label:"数据中心:"},{default:o(()=>[l(u,{modelValue:t.formData.data_center,"onUpdate:modelValue":a[8]||(a[8]=e=>t.formData.data_center=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择数据中心"},{default:o(()=>[(s(!0),_(b,null,g(t.datacenters,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"operation_status",label:"生命周期:"},{default:o(()=>[l(u,{modelValue:t.formData.operation_status,"onUpdate:modelValue":a[9]||(a[9]=e=>t.formData.operation_status=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择生命周期"},{default:o(()=>[(s(!0),_(b,null,g(t.operationstatus,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"importance",label:"重要性:"},{default:o(()=>[l(u,{modelValue:t.formData.importance,"onUpdate:modelValue":a[10]||(a[10]=e=>t.formData.importance=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择重要性"},{default:o(()=>[(s(!0),_(b,null,g(t.importances,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"asset_number",label:"资产编号:"},{default:o(()=>[l(d,{modelValue:t.formData.asset_number,"onUpdate:modelValue":a[11]||(a[11]=e=>t.formData.asset_number=e),style:{width:"240px"},clearable:"",placeholder:"请输入资产编号"},null,8,["modelValue"])]),_:1}),l(r,{prop:"purchase_date",label:"购买日期:"},{default:o(()=>[l(k,{modelValue:t.formData.purchase_date,"onUpdate:modelValue":a[12]||(a[12]=e=>t.formData.purchase_date=e),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"240px"},clearable:"",placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),l(r,{prop:"maintenance_years",label:"维保年限:"},{default:o(()=>[l(d,{modelValue:t.formData.maintenance_years,"onUpdate:modelValue":a[13]||(a[13]=e=>t.formData.maintenance_years=e),style:{width:"240px"},clearable:"",placeholder:"请输入维保年限"},null,8,["modelValue"])]),_:1}),l(r,{prop:"serial_number",label:"序列号:"},{default:o(()=>[l(d,{modelValue:t.formData.serial_number,"onUpdate:modelValue":a[14]||(a[14]=e=>t.formData.serial_number=e),style:{width:"240px"},clearable:"",placeholder:"请输入序列号"},null,8,["modelValue"])]),_:1}),l(r,{prop:"model",label:"设备型号:"},{default:o(()=>[l(d,{modelValue:t.formData.model,"onUpdate:modelValue":a[15]||(a[15]=e=>t.formData.model=e),style:{width:"240px"},clearable:"",placeholder:"请输入设备型号"},null,8,["modelValue"])]),_:1}),l(r,{prop:"version",label:"版本:"},{default:o(()=>[l(d,{modelValue:t.formData.version,"onUpdate:modelValue":a[16]||(a[16]=e=>t.formData.version=e),style:{width:"240px"},clearable:"",placeholder:"请输入版本"},null,8,["modelValue"])]),_:1}),l(r,{prop:"cpu_model",label:"CPU型号:"},{default:o(()=>[l(d,{modelValue:t.formData.cpu_model,"onUpdate:modelValue":a[17]||(a[17]=e=>t.formData.cpu_model=e),style:{width:"240px"},clearable:"",placeholder:"请输入CPU型号"},null,8,["modelValue"])]),_:1}),l(r,{prop:"is_innovative_tech",label:"是否信创:"},{default:o(()=>[l(u,{modelValue:t.formData.is_innovative_tech,"onUpdate:modelValue":a[18]||(a[18]=e=>t.formData.is_innovative_tech=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择是否信创"},{default:o(()=>[l(i,{label:"是",value:"是"}),l(i,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"monitoring_requirement",label:"需要监控:",required:!0},{default:o(()=>[l(u,{modelValue:t.formData.monitoring_requirement,"onUpdate:modelValue":a[19]||(a[19]=e=>t.formData.monitoring_requirement=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择需要监控",onChange:p.handleMonitoringRequirementChange},{default:o(()=>[l(i,{label:"是",value:!0}),l(i,{label:"否",value:!1})]),_:1},8,["modelValue","onChange"])]),_:1}),l(r,{prop:"monitoring_requirement_description",label:"不监控原因:",required:t.formData.monitoring_requirement===!1},{default:o(()=>[l(d,{modelValue:t.formData.monitoring_requirement_description,"onUpdate:modelValue":a[20]||(a[20]=e=>t.formData.monitoring_requirement_description=e),type:"textarea",rows:3,style:{width:"240px"},clearable:"",placeholder:"当需要监控为否时，请说明原因",disabled:t.formData.monitoring_requirement===!0},null,8,["modelValue","disabled"])]),_:1},8,["required"]),l(r,{prop:"monitoring_ip",label:"监控IP:"},{default:o(()=>[l(d,{modelValue:t.formData.monitoring_ip,"onUpdate:modelValue":a[21]||(a[21]=e=>t.formData.monitoring_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入监控IP"},null,8,["modelValue"])]),_:1}),l(r,{prop:"is_single_point",label:"是否单点:"},{default:o(()=>[l(u,{modelValue:t.formData.is_single_point,"onUpdate:modelValue":a[22]||(a[22]=e=>t.formData.is_single_point=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择是否单点",onChange:p.validateManagedAddresses},{default:o(()=>[l(i,{label:"是",value:"是"}),l(i,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),l(r,{prop:"managed_addresses",label:"互备主机IP:",required:t.formData.is_single_point==="否"},{default:o(()=>[l(d,{modelValue:t.formData.managed_addresses,"onUpdate:modelValue":a[23]||(a[23]=e=>t.formData.managed_addresses=e),style:{width:"240px"},clearable:"",placeholder:"请输入互备主机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),l(r,{prop:"remarks",label:"备注:"},{default:o(()=>[l(d,{modelValue:t.formData.remarks,"onUpdate:modelValue":a[24]||(a[24]=e=>t.formData.remarks=e),style:{width:"240px"},clearable:"",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),l(P,{modelValue:t.dialogVisible.edit,"onUpdate:modelValue":a[53]||(a[53]=e=>t.dialogVisible.edit=e),title:"编辑网络设备信息",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:o(()=>[v("div",le,[l(y,{onClick:a[52]||(a[52]=e=>t.dialogVisible.edit=!1)},{default:o(()=>[V("取消")]),_:1}),l(y,{type:"primary",onClick:p.validateAndSubmitEdit},{default:o(()=>[V("更新")]),_:1},8,["onClick"])])]),default:o(()=>[v("div",ee,[l(q,{model:t.formData,rules:t.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:o(()=>[l(r,{prop:"management_ip",label:"管理IP:"},{default:o(()=>[l(d,{modelValue:t.formData.management_ip,"onUpdate:modelValue":a[27]||(a[27]=e=>t.formData.management_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),l(r,{prop:"out_of_band_management",label:"带外管理IP:"},{default:o(()=>[l(d,{modelValue:t.formData.out_of_band_management,"onUpdate:modelValue":a[28]||(a[28]=e=>t.formData.out_of_band_management=e),style:{width:"240px"},clearable:"",placeholder:"请输入带外管理IP"},null,8,["modelValue"])]),_:1}),l(r,{prop:"hostname",label:"主机名:"},{default:o(()=>[l(d,{modelValue:t.formData.hostname,"onUpdate:modelValue":a[29]||(a[29]=e=>t.formData.hostname=e),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),l(r,{prop:"function_purpose",label:"功能用途:"},{default:o(()=>[l(d,{modelValue:t.formData.function_purpose,"onUpdate:modelValue":a[30]||(a[30]=e=>t.formData.function_purpose=e),style:{width:"240px"},clearable:"",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),l(r,{prop:"admin1",label:"管理员1:"},{default:o(()=>[l(u,{modelValue:t.formData.admin1,"onUpdate:modelValue":a[31]||(a[31]=e=>t.formData.admin1=e),style:{width:"240px"},placeholder:"请选择管理员1",clearable:"",filterable:""},{default:o(()=>[(s(!0),_(b,null,g(t.usersList,e=>(s(),f(i,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"admin2",label:"管理员2:"},{default:o(()=>[l(u,{modelValue:t.formData.admin2,"onUpdate:modelValue":a[32]||(a[32]=e=>t.formData.admin2=e),style:{width:"240px"},placeholder:"请选择管理员2",clearable:"",filterable:""},{default:o(()=>[(s(!0),_(b,null,g(t.usersList,e=>(s(),f(i,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"device_type",label:"设备类型:"},{default:o(()=>[l(u,{modelValue:t.formData.device_type,"onUpdate:modelValue":a[33]||(a[33]=e=>t.formData.device_type=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择设备类型"},{default:o(()=>[(s(!0),_(b,null,g(t.devicetypes,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"production_attributes",label:"生产属性:"},{default:o(()=>[l(u,{modelValue:t.formData.production_attributes,"onUpdate:modelValue":a[34]||(a[34]=e=>t.formData.production_attributes=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择生产属性"},{default:o(()=>[(s(!0),_(b,null,g(t.productionattributes,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"data_center",label:"数据中心:"},{default:o(()=>[l(u,{modelValue:t.formData.data_center,"onUpdate:modelValue":a[35]||(a[35]=e=>t.formData.data_center=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择数据中心"},{default:o(()=>[(s(!0),_(b,null,g(t.datacenters,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"operation_status",label:"生命周期:"},{default:o(()=>[l(u,{modelValue:t.formData.operation_status,"onUpdate:modelValue":a[36]||(a[36]=e=>t.formData.operation_status=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择生命周期"},{default:o(()=>[(s(!0),_(b,null,g(t.operationstatus,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"importance",label:"重要性:"},{default:o(()=>[l(u,{modelValue:t.formData.importance,"onUpdate:modelValue":a[37]||(a[37]=e=>t.formData.importance=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择重要性"},{default:o(()=>[(s(!0),_(b,null,g(t.importances,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"asset_number",label:"资产编号:"},{default:o(()=>[l(d,{modelValue:t.formData.asset_number,"onUpdate:modelValue":a[38]||(a[38]=e=>t.formData.asset_number=e),style:{width:"240px"},clearable:"",placeholder:"请输入资产编号"},null,8,["modelValue"])]),_:1}),l(r,{prop:"purchase_date",label:"购买日期:"},{default:o(()=>[l(k,{modelValue:t.formData.purchase_date,"onUpdate:modelValue":a[39]||(a[39]=e=>t.formData.purchase_date=e),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"240px"},clearable:"",placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),l(r,{prop:"maintenance_years",label:"维保年限:"},{default:o(()=>[l(d,{modelValue:t.formData.maintenance_years,"onUpdate:modelValue":a[40]||(a[40]=e=>t.formData.maintenance_years=e),style:{width:"240px"},clearable:"",placeholder:"请输入维保年限"},null,8,["modelValue"])]),_:1}),l(r,{prop:"serial_number",label:"序列号:"},{default:o(()=>[l(d,{modelValue:t.formData.serial_number,"onUpdate:modelValue":a[41]||(a[41]=e=>t.formData.serial_number=e),style:{width:"240px"},clearable:"",placeholder:"请输入序列号"},null,8,["modelValue"])]),_:1}),l(r,{prop:"model",label:"设备型号:"},{default:o(()=>[l(d,{modelValue:t.formData.model,"onUpdate:modelValue":a[42]||(a[42]=e=>t.formData.model=e),style:{width:"240px"},clearable:"",placeholder:"请输入设备型号"},null,8,["modelValue"])]),_:1}),l(r,{prop:"version",label:"版本:"},{default:o(()=>[l(d,{modelValue:t.formData.version,"onUpdate:modelValue":a[43]||(a[43]=e=>t.formData.version=e),style:{width:"240px"},clearable:"",placeholder:"请输入版本"},null,8,["modelValue"])]),_:1}),l(r,{prop:"cpu_model",label:"CPU型号:"},{default:o(()=>[l(d,{modelValue:t.formData.cpu_model,"onUpdate:modelValue":a[44]||(a[44]=e=>t.formData.cpu_model=e),style:{width:"240px"},clearable:"",placeholder:"请输入CPU型号"},null,8,["modelValue"])]),_:1}),l(r,{prop:"is_innovative_tech",label:"是否信创:"},{default:o(()=>[l(u,{modelValue:t.formData.is_innovative_tech,"onUpdate:modelValue":a[45]||(a[45]=e=>t.formData.is_innovative_tech=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择是否信创"},{default:o(()=>[l(i,{label:"是",value:"是"}),l(i,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),l(r,{prop:"monitoring_requirement",label:"需要监控:",required:!0},{default:o(()=>[l(u,{modelValue:t.formData.monitoring_requirement,"onUpdate:modelValue":a[46]||(a[46]=e=>t.formData.monitoring_requirement=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择需要监控",onChange:p.handleMonitoringRequirementChange},{default:o(()=>[l(i,{label:"是",value:!0}),l(i,{label:"否",value:!1})]),_:1},8,["modelValue","onChange"])]),_:1}),l(r,{prop:"monitoring_requirement_description",label:"不监控原因:",required:t.formData.monitoring_requirement===!1},{default:o(()=>[l(d,{modelValue:t.formData.monitoring_requirement_description,"onUpdate:modelValue":a[47]||(a[47]=e=>t.formData.monitoring_requirement_description=e),type:"textarea",rows:3,style:{width:"240px"},clearable:"",placeholder:"当需要监控为否时，请说明原因",disabled:t.formData.monitoring_requirement===!0},null,8,["modelValue","disabled"])]),_:1},8,["required"]),l(r,{prop:"monitoring_ip",label:"监控IP:"},{default:o(()=>[l(d,{modelValue:t.formData.monitoring_ip,"onUpdate:modelValue":a[48]||(a[48]=e=>t.formData.monitoring_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入监控IP"},null,8,["modelValue"])]),_:1}),l(r,{prop:"is_single_point",label:"是否单点:"},{default:o(()=>[l(u,{modelValue:t.formData.is_single_point,"onUpdate:modelValue":a[49]||(a[49]=e=>t.formData.is_single_point=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择是否单点",onChange:p.validateManagedAddresses},{default:o(()=>[l(i,{label:"是",value:"是"}),l(i,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),l(r,{prop:"managed_addresses",label:"互备主机IP:",required:t.formData.is_single_point==="否"},{default:o(()=>[l(d,{modelValue:t.formData.managed_addresses,"onUpdate:modelValue":a[50]||(a[50]=e=>t.formData.managed_addresses=e),style:{width:"240px"},clearable:"",placeholder:"请输入互备主机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),l(r,{prop:"remarks",label:"备注:"},{default:o(()=>[l(d,{modelValue:t.formData.remarks,"onUpdate:modelValue":a[51]||(a[51]=e=>t.formData.remarks=e),style:{width:"240px"},clearable:"",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),l(P,{modelValue:t.dialogVisible.delete,"onUpdate:modelValue":a[55]||(a[55]=e=>t.dialogVisible.delete=e),title:"删除管理IP",width:"500","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:o(()=>[v("div",null,[l(y,{onClick:a[54]||(a[54]=e=>t.dialogVisible.delete=!1)},{default:o(()=>[V("取消")]),_:1}),l(y,{type:"danger",onClick:p.submitDelete},{default:o(()=>[V("确认删除")]),_:1},8,["onClick"])])]),default:o(()=>[l(S,{type:"warning",title:`确定要删除 IP 为 ${t.formData.management_ip} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),l(F,{class:"search-card"},{default:o(()=>[l(q,{inline:!0},{default:o(()=>[l(R,{gutter:10},{default:o(()=>[l(D,{span:6},{default:o(()=>[l(r,{label:"管理IP"},{default:o(()=>[l(d,{modelValue:t.search.management_ip,"onUpdate:modelValue":a[56]||(a[56]=e=>t.search.management_ip=e),placeholder:"请输入管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"带外管理IP"},{default:o(()=>[l(d,{modelValue:t.search.out_of_band_management,"onUpdate:modelValue":a[57]||(a[57]=e=>t.search.out_of_band_management=e),placeholder:"请输入带外管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"主机名"},{default:o(()=>[l(d,{modelValue:t.search.hostname,"onUpdate:modelValue":a[58]||(a[58]=e=>t.search.hostname=e),placeholder:"请输入主机名",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"管理员1"},{default:o(()=>[l(u,{modelValue:t.search.admin1,"onUpdate:modelValue":a[59]||(a[59]=e=>t.search.admin1=e),placeholder:"请选择管理员1",clearable:"",filterable:"",class:"form-control"},{default:o(()=>[(s(!0),_(b,null,g(t.usersList,e=>(s(),f(i,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"管理员2"},{default:o(()=>[l(u,{modelValue:t.search.admin2,"onUpdate:modelValue":a[60]||(a[60]=e=>t.search.admin2=e),placeholder:"请选择管理员2",clearable:"",filterable:"",class:"form-control"},{default:o(()=>[(s(!0),_(b,null,g(t.usersList,e=>(s(),f(i,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"生命周期"},{default:o(()=>[l(u,{modelValue:t.search.operation_status,"onUpdate:modelValue":a[61]||(a[61]=e=>t.search.operation_status=e),placeholder:"请选择生命周期",clearable:"",filterable:"",class:"form-control"},{default:o(()=>[(s(!0),_(b,null,g(t.operationstatus,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"设备类型"},{default:o(()=>[l(u,{modelValue:t.search.device_type,"onUpdate:modelValue":a[62]||(a[62]=e=>t.search.device_type=e),placeholder:"请选择设备类型",clearable:"",filterable:"",class:"form-control"},{default:o(()=>[(s(!0),_(b,null,g(t.devicetypes,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"生产属性"},{default:o(()=>[l(u,{modelValue:t.search.production_attributes,"onUpdate:modelValue":a[63]||(a[63]=e=>t.search.production_attributes=e),placeholder:"请选择生产属性",clearable:"",filterable:"",class:"form-control"},{default:o(()=>[(s(!0),_(b,null,g(t.productionattributes,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"所属机房"},{default:o(()=>[l(u,{modelValue:t.search.data_center,"onUpdate:modelValue":a[64]||(a[64]=e=>t.search.data_center=e),placeholder:"请选择所属机房",clearable:"",filterable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",class:"form-control"},{default:o(()=>[(s(!0),_(b,null,g(t.datacenters,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"重要性"},{default:o(()=>[l(u,{modelValue:t.search.importance,"onUpdate:modelValue":a[65]||(a[65]=e=>t.search.importance=e),placeholder:"请选择重要性",clearable:"",filterable:"",class:"form-control"},{default:o(()=>[(s(!0),_(b,null,g(t.importances,e=>(s(),f(i,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"序列号"},{default:o(()=>[l(d,{modelValue:t.search.serial_number,"onUpdate:modelValue":a[66]||(a[66]=e=>t.search.serial_number=e),placeholder:"请输入序列号",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"是否信创"},{default:o(()=>[l(u,{modelValue:t.search.is_innovative_tech,"onUpdate:modelValue":a[67]||(a[67]=e=>t.search.is_innovative_tech=e),placeholder:"请选择是否信创",clearable:"",filterable:"",class:"form-control"},{default:o(()=>[l(i,{label:"是",value:"是"}),l(i,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"需要监控"},{default:o(()=>[l(u,{modelValue:t.search.monitoring_requirement,"onUpdate:modelValue":a[68]||(a[68]=e=>t.search.monitoring_requirement=e),placeholder:"请选择需要监控",clearable:"",filterable:"",class:"form-control"},{default:o(()=>[l(i,{label:"是",value:"是"}),l(i,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"监控状态"},{default:o(()=>[l(u,{modelValue:t.search.is_monitored,"onUpdate:modelValue":a[69]||(a[69]=e=>t.search.is_monitored=e),placeholder:"请选择监控状态",clearable:"",filterable:"",class:"form-control"},{default:o(()=>[l(i,{label:"是",value:"是"}),l(i,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:6},{default:o(()=>[l(r,{label:"PING状态"},{default:o(()=>[l(u,{modelValue:t.search.online_status,"onUpdate:modelValue":a[70]||(a[70]=e=>t.search.online_status=e),placeholder:"请选择PING状态",clearable:"",filterable:"",class:"form-control"},{default:o(()=>[l(i,{label:"在线",value:"在线"}),l(i,{label:"离线",value:"离线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(D,{span:24,class:"search-buttons-col"},{default:o(()=>[l(r,{label:" ",class:"form-item-with-label search-buttons"},{default:o(()=>[v("div",ae,[l(y,{type:"primary",onClick:p.loadData},{default:o(()=>[l(C,null,{default:o(()=>[l(A)]),_:1}),V("查询 ")]),_:1},8,["onClick"]),l(y,{onClick:p.resetSearch},{default:o(()=>[V("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),v("div",te,[v("div",oe,[l(y,{type:"success",disabled:!t.hasInsertPermission,onClick:p.handleAdd},{default:o(()=>[l(C,null,{default:o(()=>[l(T)]),_:1}),V("新增资产 ")]),_:1},8,["disabled","onClick"])]),v("div",re,[l(y,{type:"info",onClick:p.exportData},{default:o(()=>[l(C,null,{default:o(()=>[l(Y)]),_:1}),V(" 导出数据 ")]),_:1},8,["onClick"])])]),l(F,{class:"table-card"},{default:o(()=>[j((s(),f(z,{data:t.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:p.handleSortChange},{default:o(()=>[J("",!0),l(m,{prop:"management_ip",label:"管理IP",sortable:""}),l(m,{prop:"out_of_band_management",label:"带外管理IP",sortable:""}),l(m,{prop:"hostname",label:"主机名",sortable:""}),l(m,{prop:"function_purpose",label:"功能用途",sortable:""}),l(m,{prop:"admin1",label:"管理员1",sortable:""}),l(m,{prop:"admin2",label:"管理员2",sortable:""}),l(m,{prop:"operation_status",label:"生命周期",sortable:""},{default:o(e=>[l(w,{type:p.getLifecycleTagType(e.row.operation_status)},{default:o(()=>[V(x(e.row.operation_status),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"importance",label:"重要性",sortable:""},{default:o(e=>[l(w,{type:p.getImportanceTagType(e.row.importance)},{default:o(()=>[V(x(e.row.importance),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"device_type",label:"设备类型",sortable:""}),l(m,{prop:"production_attributes",label:"生产属性",sortable:""}),l(m,{prop:"data_center",label:"所属机房",sortable:""}),l(m,{prop:"asset_number",label:"财务资产编号",sortable:""}),l(m,{prop:"purchase_date",label:"采购时间",sortable:""}),l(m,{prop:"maintenance_years",label:"维保年限",sortable:""}),l(m,{prop:"maintenance_end_date",label:"维保截止日期",sortable:""}),l(m,{prop:"serial_number",label:"序列号",sortable:""}),l(m,{prop:"model",label:"设备型号",sortable:""}),l(m,{prop:"version",label:"版本",sortable:""}),l(m,{prop:"cpu_model",label:"CPU型号",sortable:""}),l(m,{prop:"is_innovative_tech",label:"是否信创",sortable:""},{default:o(e=>[l(w,{type:e.row.is_innovative_tech==="是"?"success":"danger"},{default:o(()=>[V(x(e.row.is_innovative_tech),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"monitoring_requirement",label:"需要监控",sortable:""},{default:o(e=>[l(w,{type:e.row.monitoring_requirement==="是"?"success":"danger"},{default:o(()=>[V(x(e.row.monitoring_requirement),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"monitoring_requirement_description",label:"不监控原因",sortable:"","min-width":"150"},{default:o(e=>[e.row.monitoring_requirement_description?(s(),_("span",ie,x(e.row.monitoring_requirement_description),1)):(s(),_("span",ne," - "))]),_:1}),l(m,{prop:"is_monitored",label:"监控状态",sortable:""},{default:o(e=>[l(w,{type:e.row.is_monitored==="是"?"success":"danger"},{default:o(()=>[V(x(e.row.is_monitored),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"online_status",label:"PING状态",sortable:""},{default:o(e=>[l(w,{type:e.row.online_status==="在线"?"success":"danger"},{default:o(()=>[V(x(e.row.online_status),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"monitoring_ip",label:"配置的监控IP",sortable:""}),l(m,{prop:"is_single_point",label:"是否单点",sortable:""},{default:o(e=>[l(w,{type:e.row.is_single_point==="是"?"success":"danger"},{default:o(()=>[V(x(e.row.is_single_point),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"managed_addresses",label:"互备主机IP",sortable:""}),l(m,{prop:"remarks",label:"备注",sortable:""}),l(m,{prop:"created_at",label:"创建时间",sortable:""}),l(m,{prop:"created_by",label:"创建人",sortable:""}),l(m,{prop:"updated_at",label:"更新时间",sortable:""}),l(m,{prop:"updated_by",label:"更新人",sortable:""}),l(m,{label:"操作",fixed:"right"},{default:o(e=>[v("div",se,[l(y,{size:"small",type:"warning",disabled:!t.hasUpdatePermission,onClick:E=>p.handleEdit(e.$index,e.row)},{default:o(()=>[V("编辑")]),_:2},1032,["disabled","onClick"]),l(y,{size:"small",type:"danger",disabled:!t.hasDeletePermission,onClick:E=>p.handleDelete(e.$index,e.row)},{default:o(()=>[V("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[L,t.loading]]),v("div",de,[l(M,{background:"","current-page":t.search.currentPage,"page-size":t.search.pageSize,total:t.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:p.handlePageSizeChange,onCurrentChange:p.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const fe=B(W,[["render",ue],["__scopeId","data-v-192c5ae1"]]);export{fe as default};
