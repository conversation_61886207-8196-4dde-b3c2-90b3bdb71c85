import{_ as R,A as G,v as O,B as T,c as V,a as e,f as u,w as l,b as n,F as D,l as k,h as d,t as w,C as j,D as H,m as v,p as J,q as K,e as h}from"./index-MGgq8mV5.js";import{u as S,w as M}from"./xlsx-DH6WiNtO.js";import{F as Q}from"./FileSaver.min-Cr9SGvul.js";const W={components:{Plus:T,Search:O,Download:G},data(){var o,t,r;return{userArr:[],loading:!1,hasDeletePermission:(o=localStorage.getItem("role_code"))==null?void 0:o.includes("D"),hasUpdatePermission:(t=localStorage.getItem("role_code"))==null?void 0:t.includes("U"),hasInsertPermission:(r=localStorage.getItem("role_code"))==null?void 0:r.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{management_ip:"",function_purpose:"",admin1:"",admin2:"",designated_admin:"",management_status:"",is_virtual_machine:"",online_status:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,management_ip:"",designated_admin:"",remarks:""},userList:[],rules:{management_ip:[{required:!0,message:"请输入IP地址",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"请输入有效的IP地址",trigger:"blur"}],designated_admin:[{max:100,message:"指定管理员长度不能超过100个字符",trigger:"blur"}],remarks:[{max:500,message:"备注长度不能超过500个字符",trigger:"blur"}]}}},mounted(){this.$route.query.search_ip&&(this.search.management_ip=this.$route.query.search_ip),this.loadData(),this.loadUserList()},methods:{handlePageChange(o){this.search.currentPage=o,this.loadData()},handlePageSizeChange(o){this.search.pageSize=parseInt(o),this.search.currentPage=1,this.loadData()},handleSortChange({prop:o,order:t}){this.search.sortProp=o,this.search.sortOrder=t==="ascending"?"asc":"desc",this.loadData()},resetSearch(){this.search={management_ip:"",function_purpose:"",admin1:"",admin2:"",designated_admin:"",management_status:"",is_virtual_machine:"",online_status:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async loadData(){try{this.loading=!0;const o=await this.$axios.post("/api/get_cmdb_host_scan_results",this.search);this.userArr=o.data.msg,this.search.total=o.data.total}catch(o){console.error("数据加载失败:",o),this.$message.error("数据加载失败")}finally{this.loading=!1}},exportData(){const t=this.$refs.table.columns,r=t.map(p=>p.label),x=this.userArr.map(p=>t.map(y=>p[y.property])),s=[r,...x],c=S.aoa_to_sheet(s),f=S.book_new();S.book_append_sheet(f,c,"Sheet1");const i=M(f,{bookType:"xlsx",type:"array"}),_=new Blob([i],{type:"application/octet-stream"});Q.saveAs(_,"IP资产管理.xlsx")},handleEdit(o,t){this.dialogVisible.edit=!0,this.formData.id=t.id,this.formData.management_ip=t.management_ip,this.formData.designated_admin=t.designated_admin,this.formData.remarks=t.remarks,this.userList.length===0&&this.loadUserList()},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){var o,t;try{const r={...this.formData,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/update_cmdb_host_scan_results",r),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(r){console.error("更新失败:",r),this.$message.error(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.msg)||"更新失败")}},async loadUserList(){try{const o=await this.$axios.post("/api/get_all_users_real_name");o.data.code===0?this.userList=o.data.msg||[]:(console.error("获取用户列表失败:",o.data.msg),this.$message.error("获取用户列表失败"))}catch(o){console.error("获取用户列表失败:",o),this.$message.error("获取用户列表失败")}},handleAdd(){this.dialogVisible.add=!0,this.formData={management_ip:"",designated_admin:"",remarks:""},this.userList.length===0&&this.loadUserList(),this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields()})},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){var o,t;try{const r={...this.formData,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/add_cmdb_host_scan_results",r),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(r){console.error("添加失败:",r),this.$message.error(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.msg)||"添加失败")}},handleDelete(o,t){this.dialogVisible.delete=!0,this.formData.id=t.id,this.formData.management_ip=t.management_ip},async submitDelete(){var o,t;try{const r={id:this.formData.id,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/delete_cmdb_host_scan_results",r),this.$message.success("删除成功"),this.dialogVisible.delete=!1,this.loadData()}catch(r){console.error("删除失败:",r),this.$message.error(((t=(o=r.response)==null?void 0:o.data)==null?void 0:t.msg)||"删除失败")}}}},X=o=>(J("data-v-030c9213"),o=o(),K(),o),Y={class:"user-manage"},Z={class:"dialogdiv"},$={class:"dialog-footer"},ee={class:"dialogdiv"},ae=X(()=>u("p",null,"此操作不可恢复！",-1)),le={class:"dialog-footer"},te={class:"dialogdiv"},se={class:"dialog-footer"},oe={class:"button-container"},re={class:"action-bar unified-action-bar"},ne={class:"action-bar-left"},ie={class:"action-bar-right"},de={style:{display:"flex","white-space":"nowrap"}},ue={class:"pagination"};function me(o,t,r,x,s,c){const f=n("el-input"),i=n("el-form-item"),_=n("el-option"),p=n("el-select"),y=n("el-form"),g=n("el-button"),C=n("el-dialog"),b=n("el-col"),A=n("Search"),P=n("el-icon"),z=n("el-row"),I=n("el-card"),L=n("Plus"),F=n("Download"),m=n("el-table-column"),U=n("el-tag"),E=n("el-table"),q=n("el-pagination"),B=H("loading");return h(),V("div",Y,[e(C,{modelValue:s.dialogVisible.add,"onUpdate:modelValue":t[4]||(t[4]=a=>s.dialogVisible.add=a),title:"新增IP资产",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[u("div",$,[e(g,{onClick:t[3]||(t[3]=a=>s.dialogVisible.add=!1)},{default:l(()=>[d("返回")]),_:1}),e(g,{type:"primary",onClick:c.validateAndSubmitAdd},{default:l(()=>[d("确定")]),_:1},8,["onClick"])])]),default:l(()=>[u("div",Z,[e(y,{model:s.formData,rules:s.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:l(()=>[e(i,{prop:"management_ip",label:"IP地址:"},{default:l(()=>[e(f,{modelValue:s.formData.management_ip,"onUpdate:modelValue":t[0]||(t[0]=a=>s.formData.management_ip=a),style:{width:"240px"},clearable:"",placeholder:"请输入IP地址"},null,8,["modelValue"])]),_:1}),e(i,{prop:"designated_admin",label:"指定管理员:"},{default:l(()=>[e(p,{modelValue:s.formData.designated_admin,"onUpdate:modelValue":t[1]||(t[1]=a=>s.formData.designated_admin=a),style:{width:"240px"},filterable:"",clearable:"",placeholder:"请选择指定管理员"},{default:l(()=>[(h(!0),V(D,null,k(s.userList,a=>(h(),v(_,{key:a.id,label:a.real_name||a.username,value:a.real_name||a.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{prop:"remarks",label:"备注:"},{default:l(()=>[e(f,{modelValue:s.formData.remarks,"onUpdate:modelValue":t[2]||(t[2]=a=>s.formData.remarks=a),style:{width:"240px"},type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(C,{modelValue:s.dialogVisible.delete,"onUpdate:modelValue":t[6]||(t[6]=a=>s.dialogVisible.delete=a),title:"确认删除",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[u("div",le,[e(g,{onClick:t[5]||(t[5]=a=>s.dialogVisible.delete=!1)},{default:l(()=>[d("取消")]),_:1}),e(g,{type:"danger",onClick:c.submitDelete},{default:l(()=>[d("确定删除")]),_:1},8,["onClick"])])]),default:l(()=>[u("div",ee,[u("p",null,[d("确定要删除IP地址为 "),u("strong",null,w(s.formData.management_ip),1),d(" 的记录吗？")]),ae])]),_:1},8,["modelValue"]),e(C,{modelValue:s.dialogVisible.edit,"onUpdate:modelValue":t[11]||(t[11]=a=>s.dialogVisible.edit=a),title:"编辑IP资产",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[u("div",se,[e(g,{onClick:t[10]||(t[10]=a=>s.dialogVisible.edit=!1)},{default:l(()=>[d("返回")]),_:1}),e(g,{type:"primary",onClick:c.validateAndSubmitEdit},{default:l(()=>[d("确定")]),_:1},8,["onClick"])])]),default:l(()=>[u("div",te,[e(y,{model:s.formData,rules:s.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:l(()=>[e(i,{prop:"management_ip",label:"IP地址:"},{default:l(()=>[e(f,{modelValue:s.formData.management_ip,"onUpdate:modelValue":t[7]||(t[7]=a=>s.formData.management_ip=a),style:{width:"240px"},clearable:"",placeholder:"请输入IP地址",disabled:""},null,8,["modelValue"])]),_:1}),e(i,{prop:"designated_admin",label:"指定管理员:"},{default:l(()=>[e(p,{modelValue:s.formData.designated_admin,"onUpdate:modelValue":t[8]||(t[8]=a=>s.formData.designated_admin=a),style:{width:"240px"},filterable:"",clearable:"",placeholder:"请选择指定管理员"},{default:l(()=>[(h(!0),V(D,null,k(s.userList,a=>(h(),v(_,{key:a.id,label:a.real_name||a.username,value:a.real_name||a.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{prop:"remarks",label:"备注:"},{default:l(()=>[e(f,{modelValue:s.formData.remarks,"onUpdate:modelValue":t[9]||(t[9]=a=>s.formData.remarks=a),style:{width:"240px"},type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(I,{class:"search-card"},{default:l(()=>[e(y,{inline:!0},{default:l(()=>[e(z,{gutter:10},{default:l(()=>[e(b,{span:6},{default:l(()=>[e(i,{label:"IP地址"},{default:l(()=>[e(f,{modelValue:s.search.management_ip,"onUpdate:modelValue":t[12]||(t[12]=a=>s.search.management_ip=a),placeholder:"请输入IP地址",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:l(()=>[e(i,{label:"功能用途"},{default:l(()=>[e(f,{modelValue:s.search.function_purpose,"onUpdate:modelValue":t[13]||(t[13]=a=>s.search.function_purpose=a),placeholder:"请输入功能用途",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:l(()=>[e(i,{label:"管理员1"},{default:l(()=>[e(p,{modelValue:s.search.admin1,"onUpdate:modelValue":t[14]||(t[14]=a=>s.search.admin1=a),placeholder:"请选择管理员1",clearable:"",filterable:"",class:"form-control"},{default:l(()=>[(h(!0),V(D,null,k(s.userList,a=>(h(),v(_,{key:a.id,label:a.real_name,value:a.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:l(()=>[e(i,{label:"管理员2"},{default:l(()=>[e(p,{modelValue:s.search.admin2,"onUpdate:modelValue":t[15]||(t[15]=a=>s.search.admin2=a),placeholder:"请选择管理员2",clearable:"",filterable:"",class:"form-control"},{default:l(()=>[(h(!0),V(D,null,k(s.userList,a=>(h(),v(_,{key:a.id,label:a.real_name,value:a.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:l(()=>[e(i,{label:"指定管理员"},{default:l(()=>[e(p,{modelValue:s.search.designated_admin,"onUpdate:modelValue":t[16]||(t[16]=a=>s.search.designated_admin=a),placeholder:"请选择指定管理员",clearable:"",filterable:"",class:"form-control"},{default:l(()=>[(h(!0),V(D,null,k(s.userList,a=>(h(),v(_,{key:a.id,label:a.real_name,value:a.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:l(()=>[e(i,{label:"管理状态"},{default:l(()=>[e(p,{modelValue:s.search.management_status,"onUpdate:modelValue":t[17]||(t[17]=a=>s.search.management_status=a),placeholder:"请选择管理状态",clearable:"",class:"form-control"},{default:l(()=>[e(_,{label:"有人管",value:"有人管"}),e(_,{label:"无人管",value:"无人管"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:l(()=>[e(i,{label:"是否虚拟机"},{default:l(()=>[e(p,{modelValue:s.search.is_virtual_machine,"onUpdate:modelValue":t[18]||(t[18]=a=>s.search.is_virtual_machine=a),placeholder:"请选择是否虚拟机",clearable:"",class:"form-control"},{default:l(()=>[e(_,{label:"是",value:"是"}),e(_,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:6},{default:l(()=>[e(i,{label:"PING状态"},{default:l(()=>[e(p,{modelValue:s.search.online_status,"onUpdate:modelValue":t[19]||(t[19]=a=>s.search.online_status=a),placeholder:"请选择PING状态",clearable:"",class:"form-control"},{default:l(()=>[e(_,{label:"在线",value:"在线"}),e(_,{label:"离线",value:"离线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(b,{span:24,class:"search-buttons-col"},{default:l(()=>[e(i,{label:" ",class:"form-item-with-label search-buttons"},{default:l(()=>[u("div",oe,[e(g,{type:"primary",onClick:c.loadData},{default:l(()=>[e(P,null,{default:l(()=>[e(A)]),_:1}),d("查询 ")]),_:1},8,["onClick"]),e(g,{onClick:c.resetSearch},{default:l(()=>[d("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),u("div",re,[u("div",ne,[e(g,{type:"success",disabled:!s.hasInsertPermission,onClick:c.handleAdd},{default:l(()=>[e(P,null,{default:l(()=>[e(L)]),_:1}),d("新增资产 ")]),_:1},8,["disabled","onClick"])]),u("div",ie,[e(g,{type:"info",onClick:c.exportData},{default:l(()=>[e(P,null,{default:l(()=>[e(F)]),_:1}),d(" 导出数据 ")]),_:1},8,["onClick"])])]),e(I,{class:"table-card"},{default:l(()=>[j((h(),v(E,{data:s.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",class:"host-scan-table",onSortChange:c.handleSortChange},{default:l(()=>[e(m,{prop:"management_ip",label:"IP地址",sortable:""}),e(m,{prop:"function_purpose",label:"功能用途",sortable:""}),e(m,{prop:"admin1",label:"管理员1",sortable:""}),e(m,{prop:"admin2",label:"管理员2",sortable:""}),e(m,{prop:"designated_admin",label:"指定管理员",sortable:""}),e(m,{prop:"datacenter",label:"所属机房",sortable:""}),e(m,{prop:"management_status",label:"管理状态",sortable:""}),e(m,{prop:"is_virtual_machine",label:"是否虚拟机",sortable:""},{default:l(a=>[e(U,{type:a.row.is_virtual_machine==="是"?"success":"danger"},{default:l(()=>[d(w(a.row.is_virtual_machine),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"online_status",label:"PING状态",sortable:""},{default:l(a=>[e(U,{type:a.row.online_status==="在线"?"success":"danger"},{default:l(()=>[d(w(a.row.online_status),1)]),_:2},1032,["type"])]),_:1}),e(m,{prop:"remarks",label:"备注",sortable:""},{default:l(a=>[u("span",null,w(a.row.remarks),1)]),_:1}),e(m,{prop:"created_at",label:"创建时间",sortable:""}),e(m,{prop:"created_by",label:"创建人",sortable:""}),e(m,{prop:"updated_at",label:"更新时间",sortable:""}),e(m,{prop:"updated_by",label:"更新人",sortable:""}),e(m,{label:"操作",fixed:"right"},{default:l(a=>[u("div",de,[e(g,{size:"small",type:"warning",disabled:!s.hasUpdatePermission,onClick:N=>c.handleEdit(a.$index,a.row)},{default:l(()=>[d("特例编辑")]),_:2},1032,["disabled","onClick"]),e(g,{size:"small",type:"danger",disabled:!s.hasDeletePermission,onClick:N=>c.handleDelete(a.$index,a.row)},{default:l(()=>[d("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[B,s.loading]]),u("div",ue,[e(q,{background:"","current-page":s.search.currentPage,"page-size":s.search.pageSize,total:s.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:c.handlePageSizeChange,onCurrentChange:c.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const ge=R(W,[["render",me],["__scopeId","data-v-030c9213"]]);export{ge as default};
