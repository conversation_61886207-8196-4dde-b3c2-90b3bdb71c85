import{_ as M,A as Z,v as G,B as H,c as I,a as t,f as A,w as i,b as m,F as x,l as U,h as s,C as J,D as K,m as P,t as C,E as g,r as w,R as T,e as c}from"./index-MGgq8mV5.js";const Q={name:"InternetLineManagement",components:{Plus:H,Search:G,Download:Z},setup(){const r=w(!1),l=w(!1),d=w(!1),a=w([]),D=w(0),f=w(1),u=w(10),o=T({add:!1,edit:!1,delete:!1}),y=w(null),V=w(null),k=T({line_name:"",provider:""}),p=T({id:null,line_name:"",provider:"",line_type:"",bandwidth:"",ip_range:"",gateway_ip:"",firewall_ip:"",datacenter:""}),L=w([]),z=w([]),E=w([]);return{loading:r,submitLoading:l,exportLoading:d,tableData:a,total:D,currentPage:f,pageSize:u,dialogVisible:o,addFormRef:y,editFormRef:V,search:k,formData:p,providers:L,lineTypes:z,datacenters:E,rules:{line_name:[{required:!0,message:"请输入线路名称",trigger:"blur"}],provider:[{required:!0,message:"请选择运营商",trigger:"change"}],line_type:[{required:!0,message:"请选择线路类型",trigger:"change"}],bandwidth:[{required:!0,message:"请输入带宽",trigger:"blur"}],ip_range:[{validator:(h,v,n)=>{if(!v){n();return}if(!v.includes("/")){n(new Error("IP地址段格式不正确，应为 ***********/24 或 2001:db8::/32 格式"));return}const[_,e]=v.split("/"),b=parseInt(e);if(/^(\d{1,3}\.){3}\d{1,3}$/.test(_)){const F=_.split(".");for(const O of F){const q=parseInt(O);if(q<0||q>255){n(new Error("IPv4地址格式不正确"));return}}if(b<0||b>32){n(new Error("IPv4掩码应在0-32之间"));return}}else{if(!/^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|^:((:[0-9a-fA-F]{1,4}){1,7}|:)$|^fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}$|^::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$|^([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$/.test(_)){n(new Error("IPv6地址格式不正确"));return}if(b<0||b>128){n(new Error("IPv6掩码应在0-128之间"));return}}n()},trigger:"blur"}],gateway_ip:[{validator:(h,v,n)=>{if(!v){n();return}const _=v.trim();if(/^(\d{1,3}\.){3}\d{1,3}$/.test(_)){const b=_.split(".");for(const S of b){const F=parseInt(S);if(F<0||F>255){n(new Error("网关IPv4地址格式不正确，每段应在0-255之间"));return}}}else if(!/^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|^:((:[0-9a-fA-F]{1,4}){1,7}|:)$|^fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}$|^::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$|^([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$/.test(_)){n(new Error("网关IP地址格式不正确，请输入有效的IPv4或IPv6地址"));return}n()},trigger:"blur"}],firewall_ip:[{validator:(h,v,n)=>{if(!v){n();return}const _=v.trim();if(/^(\d{1,3}\.){3}\d{1,3}$/.test(_)){const b=_.split(".");for(const S of b){const F=parseInt(S);if(F<0||F>255){n(new Error("防火墙IPv4地址格式不正确，每段应在0-255之间"));return}}}else if(!/^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$|^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|^:((:[0-9a-fA-F]{1,4}){1,7}|:)$|^fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}$|^::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$|^([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])$/.test(_)){n(new Error("防火墙IP地址格式不正确，请输入有效的IPv4或IPv6地址"));return}n()},trigger:"blur"}],datacenter:[{required:!0,message:"请选择所属机房",trigger:"change"}]}}},methods:{async getData(){this.loading=!0;try{const r=await this.$axios.post("/api/get_internet_lines",{pageSize:this.pageSize,currentPage:this.currentPage,lineName:this.search.line_name,provider:this.search.provider});r.data.code===0?(this.tableData=r.data.msg,this.total=r.data.total||0):g.error(r.data.msg||"获取数据失败")}catch(r){console.error("获取数据失败:",r),g.error("获取数据失败")}finally{this.loading=!1}},async getDictData(){try{const r=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_type:"internet_provider"});r.data.code===0&&(this.providers=r.data.msg);const l=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_type:"internet_line_type"});l.data.code===0&&(this.lineTypes=l.data.msg);const d=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_type:"A"});d.data.code===0&&(this.datacenters=d.data.msg)}catch(r){console.error("获取字典数据失败:",r)}},resetSearch(){this.search.line_name="",this.search.provider="",this.currentPage=1,this.getData()},showAddDialog(){this.resetFormData(),this.dialogVisible.add=!0},editLine(r){this.resetFormData(),Object.assign(this.formData,r),this.dialogVisible.edit=!0},deleteLine(r){Object.assign(this.formData,r),this.dialogVisible.delete=!0},resetFormData(){this.formData.id=null,this.formData.line_name="",this.formData.provider="",this.formData.line_type="",this.formData.bandwidth="",this.formData.ip_range="",this.formData.gateway_ip="",this.formData.firewall_ip="",this.formData.datacenter=""},async validateAndSubmitAdd(){if(!this.submitLoading)try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch(r){console.error("表单验证失败:",r)}},async submitAdd(){this.submitLoading=!0;try{const r=await this.$axios.post("/api/add_internet_line",{lineName:this.formData.line_name,provider:this.getProviderName(this.formData.provider),lineType:this.getLineTypeName(this.formData.line_type),bandwidth:this.formData.bandwidth,ipRange:this.formData.ip_range,gatewayIp:this.formData.gateway_ip,firewallIp:this.formData.firewall_ip,datacenter:this.formData.datacenter,loginUsername:"admin"});r.data.code===0?(g.success("新增线路成功"),this.dialogVisible.add=!1,this.resetFormData(),this.getData()):g.error(r.data.msg||"新增失败")}catch(r){console.error("新增失败:",r),g.error("新增失败")}finally{this.submitLoading=!1}},async validateAndSubmitEdit(){if(!this.submitLoading)try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch(r){console.error("表单验证失败:",r)}},async submitEdit(){this.submitLoading=!0;try{const r=await this.$axios.post("/api/update_internet_line",{id:this.formData.id,lineName:this.formData.line_name,provider:this.getProviderName(this.formData.provider),lineType:this.getLineTypeName(this.formData.line_type),bandwidth:this.formData.bandwidth,ipRange:this.formData.ip_range,gatewayIp:this.formData.gateway_ip,firewallIp:this.formData.firewall_ip,datacenter:this.formData.datacenter,loginUsername:"admin"});r.data.code===0?(g.success("更新线路成功"),this.dialogVisible.edit=!1,this.getData()):g.error(r.data.msg||"更新失败")}catch(r){console.error("更新失败:",r),g.error("更新失败")}finally{this.submitLoading=!1}},async submitDelete(){try{const r=await this.$axios.post("/api/delete_internet_line",{id:this.formData.id,loginUsername:"admin"});r.data.code===0?(g.success("删除线路成功"),this.dialogVisible.delete=!1,this.getData()):g.error(r.data.msg||"删除失败")}catch(r){console.error("删除失败:",r),g.error("删除失败")}},async exportData(){this.exportLoading=!0;try{const r=await this.$axios.post("/api/export_internet_line_data",{exportType:"filtered",filters:{lineName:this.search.line_name,provider:this.search.provider},loginUsername:"admin"},{responseType:"blob"}),l=new Blob([r.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),d=window.URL.createObjectURL(l),a=document.createElement("a");a.href=d,a.download=`互联网线路数据_${new Date().toISOString().slice(0,10)}.xlsx`,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(d),g.success("导出成功")}catch(r){console.error("导出失败:",r),g.error("导出失败")}finally{this.exportLoading=!1}},handleSizeChange(r){this.pageSize=r,this.currentPage=1,this.getData()},handleCurrentChange(r){this.currentPage=r,this.getData()},getProviderName(r){if(!r||!this.providers.length)return r||"-";const l=this.providers.find(d=>d.dict_code===r);return l?l.dict_name:r},getDatacenterName(r){if(!r||!this.datacenters.length)return r||"-";const l=this.datacenters.find(d=>d.dict_code===r);return l?l.dict_name:r},getLineTypeName(r){if(!r||!this.lineTypes.length)return r||"-";const l=this.lineTypes.find(d=>d.dict_code===r);return l?l.dict_name:r},formatIpRange(r){if(!r)return"-";if(r.length>18){if(r.includes(":")){const l=r.split("/");if(l.length===2){const d=l[0],a=l[1];if(d.length>15){const D=d.split(":");if(D.length>=4)return d.includes("::")?`${D[0]}:${D[1]}::/${a}`:`${D[0]}:${D[1]}::${D[D.length-1]}/${a}`}}}return r.length>20?r.substring(0,17)+"...":r}return r}},mounted(){this.getDictData(),this.getData()}},W={class:"internet-line-management"},X={class:"dialog-footer"},Y={class:"dialog-footer"},$={class:"dialog-footer"},ee={class:"button-container"},ae={class:"action-bar unified-action-bar"},te={class:"action-bar-left"},le={class:"action-bar-right"},re={class:"table-actions"},ie={class:"pagination"};function oe(r,l,d,a,D,f){const u=m("el-input"),o=m("el-form-item"),y=m("el-option"),V=m("el-select"),k=m("el-form"),p=m("el-button"),L=m("el-dialog"),z=m("el-alert"),E=m("Search"),R=m("el-icon"),N=m("el-card"),B=m("Plus"),j=m("Download"),h=m("el-table-column"),v=m("el-table"),n=m("el-pagination"),_=K("loading");return c(),I("div",W,[t(L,{modelValue:a.dialogVisible.add,"onUpdate:modelValue":l[9]||(l[9]=e=>a.dialogVisible.add=e),title:"新增互联网线路",width:"500","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:i(()=>[A("div",X,[t(p,{onClick:l[8]||(l[8]=e=>a.dialogVisible.add=!1)},{default:i(()=>[s("取消")]),_:1}),t(p,{type:"primary",onClick:f.validateAndSubmitAdd,loading:a.submitLoading},{default:i(()=>[s("确定")]),_:1},8,["onClick","loading"])])]),default:i(()=>[t(k,{model:a.formData,rules:a.rules,ref:"addFormRef","label-position":"right","label-width":"120px"},{default:i(()=>[t(o,{prop:"line_name",label:"线路名称:"},{default:i(()=>[t(u,{modelValue:a.formData.line_name,"onUpdate:modelValue":l[0]||(l[0]=e=>a.formData.line_name=e),placeholder:"请输入线路名称",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{prop:"provider",label:"运营商:"},{default:i(()=>[t(V,{modelValue:a.formData.provider,"onUpdate:modelValue":l[1]||(l[1]=e=>a.formData.provider=e),placeholder:"请选择运营商",clearable:""},{default:i(()=>[(c(!0),I(x,null,U(a.providers,e=>(c(),P(y,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(o,{prop:"line_type",label:"线路类型:"},{default:i(()=>[t(V,{modelValue:a.formData.line_type,"onUpdate:modelValue":l[2]||(l[2]=e=>a.formData.line_type=e),placeholder:"请选择线路类型",clearable:""},{default:i(()=>[(c(!0),I(x,null,U(a.lineTypes,e=>(c(),P(y,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(o,{prop:"bandwidth",label:"带宽:"},{default:i(()=>[t(u,{modelValue:a.formData.bandwidth,"onUpdate:modelValue":l[3]||(l[3]=e=>a.formData.bandwidth=e),placeholder:"请输入带宽，如：100M",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{prop:"ip_range",label:"IP地址段:"},{default:i(()=>[t(u,{modelValue:a.formData.ip_range,"onUpdate:modelValue":l[4]||(l[4]=e=>a.formData.ip_range=e),placeholder:"请输入IP地址段，如：***********/24",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{prop:"gateway_ip",label:"网关IP:"},{default:i(()=>[t(u,{modelValue:a.formData.gateway_ip,"onUpdate:modelValue":l[5]||(l[5]=e=>a.formData.gateway_ip=e),placeholder:"请输入网关IP，如：***********",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{prop:"firewall_ip",label:"防火墙IP:"},{default:i(()=>[t(u,{modelValue:a.formData.firewall_ip,"onUpdate:modelValue":l[6]||(l[6]=e=>a.formData.firewall_ip=e),placeholder:"请输入防火墙IP，如：*************",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{prop:"datacenter",label:"所属机房:"},{default:i(()=>[t(V,{modelValue:a.formData.datacenter,"onUpdate:modelValue":l[7]||(l[7]=e=>a.formData.datacenter=e),placeholder:"请选择所属机房",clearable:""},{default:i(()=>[(c(!0),I(x,null,U(a.datacenters,e=>(c(),P(y,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),t(L,{modelValue:a.dialogVisible.edit,"onUpdate:modelValue":l[19]||(l[19]=e=>a.dialogVisible.edit=e),title:"编辑互联网线路",width:"500","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:i(()=>[A("div",Y,[t(p,{onClick:l[18]||(l[18]=e=>a.dialogVisible.edit=!1)},{default:i(()=>[s("取消")]),_:1}),t(p,{type:"primary",onClick:f.validateAndSubmitEdit,loading:a.submitLoading},{default:i(()=>[s("更新")]),_:1},8,["onClick","loading"])])]),default:i(()=>[t(k,{model:a.formData,rules:a.rules,ref:"editFormRef","label-position":"right","label-width":"120px"},{default:i(()=>[t(o,{prop:"line_name",label:"线路名称:"},{default:i(()=>[t(u,{modelValue:a.formData.line_name,"onUpdate:modelValue":l[10]||(l[10]=e=>a.formData.line_name=e),placeholder:"请输入线路名称",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{prop:"provider",label:"运营商:"},{default:i(()=>[t(V,{modelValue:a.formData.provider,"onUpdate:modelValue":l[11]||(l[11]=e=>a.formData.provider=e),placeholder:"请选择运营商",clearable:""},{default:i(()=>[(c(!0),I(x,null,U(a.providers,e=>(c(),P(y,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(o,{prop:"line_type",label:"线路类型:"},{default:i(()=>[t(V,{modelValue:a.formData.line_type,"onUpdate:modelValue":l[12]||(l[12]=e=>a.formData.line_type=e),placeholder:"请选择线路类型",clearable:""},{default:i(()=>[(c(!0),I(x,null,U(a.lineTypes,e=>(c(),P(y,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(o,{prop:"bandwidth",label:"带宽:"},{default:i(()=>[t(u,{modelValue:a.formData.bandwidth,"onUpdate:modelValue":l[13]||(l[13]=e=>a.formData.bandwidth=e),placeholder:"请输入带宽，如：100M",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{prop:"ip_range",label:"IP地址段:"},{default:i(()=>[t(u,{modelValue:a.formData.ip_range,"onUpdate:modelValue":l[14]||(l[14]=e=>a.formData.ip_range=e),placeholder:"请输入IP地址段，如：***********/24",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{prop:"gateway_ip",label:"网关IP:"},{default:i(()=>[t(u,{modelValue:a.formData.gateway_ip,"onUpdate:modelValue":l[15]||(l[15]=e=>a.formData.gateway_ip=e),placeholder:"请输入网关IP，如：***********",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{prop:"firewall_ip",label:"防火墙IP:"},{default:i(()=>[t(u,{modelValue:a.formData.firewall_ip,"onUpdate:modelValue":l[16]||(l[16]=e=>a.formData.firewall_ip=e),placeholder:"请输入防火墙IP，如：*************",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{prop:"datacenter",label:"所属机房:"},{default:i(()=>[t(V,{modelValue:a.formData.datacenter,"onUpdate:modelValue":l[17]||(l[17]=e=>a.formData.datacenter=e),placeholder:"请选择所属机房",clearable:""},{default:i(()=>[(c(!0),I(x,null,U(a.datacenters,e=>(c(),P(y,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),t(L,{modelValue:a.dialogVisible.delete,"onUpdate:modelValue":l[21]||(l[21]=e=>a.dialogVisible.delete=e),title:"删除线路",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:i(()=>[A("div",$,[t(p,{onClick:l[20]||(l[20]=e=>a.dialogVisible.delete=!1)},{default:i(()=>[s("取消")]),_:1}),t(p,{type:"danger",onClick:f.submitDelete},{default:i(()=>[s("确认删除")]),_:1},8,["onClick"])])]),default:i(()=>[t(z,{type:"warning",title:`确定要删除线路 '${a.formData.line_name}' 吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),t(N,{class:"search-card"},{default:i(()=>[t(k,{inline:!0},{default:i(()=>[t(o,{label:"线路名称"},{default:i(()=>[t(u,{modelValue:a.search.line_name,"onUpdate:modelValue":l[22]||(l[22]=e=>a.search.line_name=e),placeholder:"请输入线路名称",clearable:""},null,8,["modelValue"])]),_:1}),t(o,{label:"运营商"},{default:i(()=>[t(V,{modelValue:a.search.provider,"onUpdate:modelValue":l[23]||(l[23]=e=>a.search.provider=e),placeholder:"请选择运营商",clearable:""},{default:i(()=>[(c(!0),I(x,null,U(a.providers,e=>(c(),P(y,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(o,{label:" ",class:"form-item-with-label search-buttons"},{default:i(()=>[A("div",ee,[t(p,{type:"primary",onClick:f.getData},{default:i(()=>[t(R,null,{default:i(()=>[t(E)]),_:1}),s("查询 ")]),_:1},8,["onClick"]),t(p,{onClick:f.resetSearch},{default:i(()=>[s("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1}),A("div",ae,[A("div",te,[t(p,{type:"success",onClick:f.showAddDialog},{default:i(()=>[t(R,null,{default:i(()=>[t(B)]),_:1}),s("新增线路 ")]),_:1},8,["onClick"])]),A("div",le,[t(p,{type:"info",onClick:f.exportData,loading:a.exportLoading},{default:i(()=>[t(R,null,{default:i(()=>[t(j)]),_:1}),s("导出Excel ")]),_:1},8,["onClick","loading"])])]),t(N,{class:"table-card"},{default:i(()=>[J((c(),P(v,{data:a.tableData,stripe:"",border:""},{default:i(()=>[t(h,{prop:"line_name",label:"线路名称","min-width":"120"}),t(h,{prop:"provider",label:"运营商",width:"100"},{default:i(e=>[s(C(f.getProviderName(e.row.provider)),1)]),_:1}),t(h,{prop:"line_type",label:"线路类型",width:"100"},{default:i(e=>[s(C(f.getLineTypeName(e.row.line_type)),1)]),_:1}),t(h,{prop:"bandwidth",label:"带宽",width:"80"}),t(h,{prop:"ip_range",label:"IP地址段",width:"140"},{default:i(e=>[s(C(f.formatIpRange(e.row.ip_range)),1)]),_:1}),t(h,{prop:"gateway_ip",label:"网关IP",width:"120"},{default:i(e=>[s(C(e.row.gateway_ip||"-"),1)]),_:1}),t(h,{prop:"firewall_ip",label:"防火墙IP",width:"120"},{default:i(e=>[s(C(e.row.firewall_ip||"-"),1)]),_:1}),t(h,{prop:"datacenter",label:"所属机房",width:"120"},{default:i(e=>[s(C(f.getDatacenterName(e.row.datacenter)),1)]),_:1}),t(h,{label:"操作",align:"center",fixed:"right",width:"150"},{default:i(e=>[A("div",re,[t(p,{size:"small",type:"warning",onClick:b=>f.editLine(e.row)},{default:i(()=>[s("编辑")]),_:2},1032,["onClick"]),t(p,{size:"small",type:"danger",onClick:b=>f.deleteLine(e.row)},{default:i(()=>[s("删除")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[_,a.loading]]),A("div",ie,[t(n,{"current-page":a.currentPage,"onUpdate:currentPage":l[24]||(l[24]=e=>a.currentPage=e),"page-size":a.pageSize,"onUpdate:pageSize":l[25]||(l[25]=e=>a.pageSize=e),"page-sizes":[10,20,50,100],total:a.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:f.handleSizeChange,onCurrentChange:f.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const de=M(Q,[["render",oe],["__scopeId","data-v-2ad49fe8"]]);export{de as default};
