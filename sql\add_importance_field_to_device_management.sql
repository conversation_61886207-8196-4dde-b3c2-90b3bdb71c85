-- 为设备管理添加重要性字段
-- 创建时间: 2025-01-19
-- 描述: 为cmdb_device_management表添加importance字段，使用数据字典存储

-- 1. 首先添加重要性字典项到数据字典表
INSERT INTO public.cmdb_data_dictionary (dict_type, dict_type_name, dict_code, dict_name, created_by, updated_by) 
VALUES 
('importance', '重要性', 'general', '一般', 'admin', 'admin'),
('importance', '重要性', 'important', '重要', 'admin', 'admin')
ON CONFLICT DO NOTHING;

-- 2. 为cmdb_device_management表添加importance字段
ALTER TABLE public.cmdb_device_management 
ADD COLUMN IF NOT EXISTS importance character varying(50);

-- 3. 为新字段添加注释
COMMENT ON COLUMN public.cmdb_device_management.importance IS '重要性，存储数据字典dict_code';

-- 4. 删除现有的v_cmdb_device_management视图（使用CASCADE删除依赖的视图）
DROP VIEW IF EXISTS public.v_cmdb_device_management CASCADE;

-- 5. 重新创建v_cmdb_device_management视图，包含重要性字段的LEFT JOIN
CREATE VIEW public.v_cmdb_device_management AS
SELECT 
    t.id,
    t.management_ip,
    t.out_of_band_management,
    t.hostname,
    t.function_purpose,
    t.admin1,
    t.admin2,
    COALESCE(t2.dict_name, t.device_type) AS device_type,
    COALESCE(t3.dict_name, t.production_attributes) AS production_attributes,
    COALESCE(t4.dict_name, t.data_center) AS data_center,
    COALESCE(t5.dict_name, t.operation_status) AS operation_status,
    COALESCE(t6.dict_name, t.importance) AS importance,
    t.asset_number,
    to_char(t.purchase_date::timestamp with time zone, 'yyyy-mm-dd'::text) AS purchase_date,
    t.maintenance_years,
    to_char(t.maintenance_end_date::timestamp with time zone, 'yyyy-mm-dd'::text) AS maintenance_end_date,
    t.serial_number,
    t.model,
    t.version,
    t.cpu_model,
    t.is_innovative_tech,
    t.is_monitored,
    t.monitoring_ip,
    t.architecture_mode,
    t.is_single_point,
    t.managed_addresses,
    t.remarks,
    t.year_category,
    t.in_monitoring_list,
    t.pre_monitoring_verified,
    t.inspection,
    t.monitoring_requirement,
    t.monitoring_requirement_description,
    to_char(t.created_at::timestamp with time zone, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at::timestamp with time zone, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by,
    CASE 
        WHEN t.online_status = '1' THEN '在线'
        WHEN t.online_status = '0' THEN '离线'
        ELSE '未知'
    END AS online_status
FROM cmdb_device_management t
LEFT JOIN cmdb_data_dictionary t2 ON t.device_type = t2.dict_code AND t2.dict_type = 'B' AND t2.del_flag = '0'
LEFT JOIN cmdb_data_dictionary t3 ON t.production_attributes = t3.dict_code AND t3.dict_type = 'C' AND t3.del_flag = '0'
LEFT JOIN cmdb_data_dictionary t4 ON t.data_center = t4.dict_code AND t4.dict_type = 'A' AND t4.del_flag = '0'
LEFT JOIN cmdb_data_dictionary t5 ON t.operation_status = t5.dict_code AND t5.dict_type = 'D' AND t5.del_flag = '0'
LEFT JOIN cmdb_data_dictionary t6 ON t.importance = t6.dict_code AND t6.dict_type = 'importance' AND t6.del_flag = '0'
WHERE t.del_flag = '0';

-- 6. 为视图添加注释
COMMENT ON VIEW public.v_cmdb_device_management IS '设备管理视图，包含数据字典关联的显示名称';

-- 7. 重新创建依赖的v_cmdb_host_scan_results视图
CREATE VIEW v_cmdb_host_scan_results AS
SELECT
    t.management_ip,
    CASE
        WHEN (replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text) ~~ '171.17.19.%'::text) THEN '客户'::text
        ELSE COALESCE(t2.admin1, t3.admin1, t4.admin1, ''::text)
    END AS admin1,
    COALESCE(t2.admin2, t3.admin2, t4.admin2, ''::text) AS admin2,
    COALESCE(t.designated_admin, ''::character varying) AS designated_admin,
    COALESCE(t2.data_center, t3.data_center, t4.data_center) AS datacenter,
    CASE
        WHEN ((
        CASE
            WHEN (replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text) ~~ '171.17.19.%'::text) THEN '客户'::text
            ELSE COALESCE(t2.admin1, t3.admin1, t4.admin1, ''::text)
        END <> ''::text) OR (COALESCE(t2.admin2, t3.admin2, t4.admin2, ''::text) <> ''::text) OR ((t.designated_admin)::text <> ''::text)) THEN '有人管'::text
        ELSE '无人管'::text
    END AS management_status,
    CASE
        WHEN ((t.is_virtual_machine IS NOT NULL) AND ((t.is_virtual_machine)::text <> ''::text)) THEN t.is_virtual_machine
        WHEN (EXISTS ( SELECT 1
           FROM cmdb_vm_registry vm
          WHERE (((vm.management_ip)::text = (t.management_ip)::text) AND ((vm.del_flag)::text = '0'::text)))) THEN '是'::character varying
        ELSE '否'::character varying
    END AS is_virtual_machine,
    CASE
        WHEN (EXISTS ( SELECT 1
           FROM v_cmdb_discovery_results dr
          WHERE ((dr.ip_address)::text = (t.management_ip)::text))) THEN '在线'::text
        ELSE '离线'::text
    END AS online_status,
    t.cmdb_registration_status,
    t.remarks,
    t.scan_date,
    t.id,
    COALESCE(t2.function_purpose, t3.function_purpose, t5.function_purpose, ''::text) AS function_purpose,
    to_char(t.created_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS created_at,
    t.created_by,
    to_char(t.updated_at, 'yyyy-mm-dd hh24:mi:ss'::text) AS updated_at,
    t.updated_by
FROM cmdb_host_scan_results t
    LEFT JOIN ( SELECT t2_1.management_ip,
        max((t2_1.admin1)::text) AS admin1,
        max((t2_1.admin2)::text) AS admin2,
        max((t2_1.data_center)::text) AS data_center,
        max((t2_1.function_purpose)::text) AS function_purpose
       FROM v_cmdb_device_management t2_1
      GROUP BY t2_1.management_ip) t2 ON (((t2.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text)))
    LEFT JOIN ( SELECT t3_1.management_ip,
        max((t3_1.admin1)::text) AS admin1,
        max((t3_1.admin2)::text) AS admin2,
        max((t3_1.data_center)::text) AS data_center,
        max((t3_1.function_purpose)::text) AS function_purpose
       FROM v_cmdb_server_management t3_1
      GROUP BY t3_1.management_ip) t3 ON (((t3.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text)))
    LEFT JOIN ( SELECT t4_1.management_ip,
        max(t4_1.server_admin1) AS admin1,
        max(t4_1.server_admin2) AS admin2,
        max(t4_1.data_center) AS data_center
       FROM v_cmdb_application_system_info t4_1
      GROUP BY t4_1.management_ip) t4 ON (((t4.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text)))
    LEFT JOIN ( SELECT t5_1.management_ip,
        max((t5_1.function_purpose)::text) AS function_purpose
       FROM v_cmdb_vm_registry t5_1
      GROUP BY t5_1.management_ip) t5 ON (((t5.management_ip)::text = replace((t.management_ip)::text, '171.17.4.'::text, '171.17.3.'::text)))
WHERE ((t.del_flag)::text = '0'::text);

-- 8. 为importance字段设置默认值（可选，根据业务需求）
-- UPDATE public.cmdb_device_management SET importance = 'general' WHERE importance IS NULL;

COMMIT;
