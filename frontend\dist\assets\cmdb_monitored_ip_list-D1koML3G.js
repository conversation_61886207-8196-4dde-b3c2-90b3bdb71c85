import{_ as T,A as j,v as q,B as G,c as y,a as e,f as r,w as l,x as k,b as i,n as I,t as w,h as c,C as J,D as K,m as L,p as Q,q as R,e as b}from"./index-MGgq8mV5.js";import{u as A,w as W}from"./xlsx-DH6WiNtO.js";import{F as X}from"./FileSaver.min-Cr9SGvul.js";const Z={components:{Plus:G,Search:q,Download:j},data(){var s,t,f;return{userArr:[],loading:!1,hasDeletePermission:(s=localStorage.getItem("role_code"))==null?void 0:s.includes("D"),hasUpdatePermission:(t=localStorage.getItem("role_code"))==null?void 0:t.includes("U"),hasInsertPermission:(f=localStorage.getItem("role_code"))==null?void 0:f.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{ip_address:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,ip_address:"",description:"",status:"正常",last_checked:""},ipAddressError:!1,ipAddressErrorMessage:""}},mounted(){this.loadData()},methods:{handlePageChange(s){this.search.currentPage=s,this.loadData()},handlePageSizeChange(s){this.search.pageSize=s,this.search.currentPage=1,this.loadData()},handleSortChange({prop:s,order:t}){this.search.sortProp=s,this.search.sortOrder=t==="ascending"?"asc":"desc",this.loadData()},resetSearch(){this.search={ip_address:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async loadData(){try{this.loading=!0;const s=await this.$axios.post("/api/get_cmdb_monitored_ip_list",this.search);this.userArr=s.data.msg,this.search.total=s.data.total}catch(s){console.error("数据加载失败:",s),this.$message.error("数据加载失败")}finally{this.loading=!1}},async checkIpAddressExists(){if(!this.formData.ip_address||this.formData.ip_address.trim()===""){this.ipAddressError=!1,this.ipAddressErrorMessage="";return}try{const s=await this.$axios.post("/api/check_ip_address_exists",{ip_address:this.formData.ip_address.trim(),id:this.formData.id});s.data.code===0&&s.data.exists?(this.ipAddressError=!0,this.ipAddressErrorMessage="此IP地址已存在，请使用其他IP地址"):(this.ipAddressError=!1,this.ipAddressErrorMessage="")}catch(s){console.error("检查IP地址失败:",s),this.ipAddressError=!1,this.ipAddressErrorMessage=""}},async submitAdd(){if(await this.checkIpAddressExists(),this.ipAddressError){this.$message.error("请修正IP地址重复问题后再提交");return}if(!this.formData.ip_address||this.formData.ip_address.trim()===""){this.$message.error("IP地址不能为空");return}try{const s={...this.formData,username:localStorage.getItem("loginUsername")||"admin"};await this.$axios.post("/api/add_cmdb_monitored_ip_list",s),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(s){console.error("添加失败:",s),s.response&&s.response.data&&s.response.data.msg?this.$message.error(s.response.data.msg):this.$message.error("添加失败")}},async submitEdit(){if(await this.checkIpAddressExists(),this.ipAddressError){this.$message.error("请修正IP地址重复问题后再提交");return}if(!this.formData.ip_address||this.formData.ip_address.trim()===""){this.$message.error("IP地址不能为空");return}try{const s={...this.formData,username:localStorage.getItem("loginUsername")||"admin"};await this.$axios.post("/api/update_cmdb_monitored_ip_list",s),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(s){console.error("更新失败:",s),s.response&&s.response.data&&s.response.data.msg?this.$message.error(s.response.data.msg):this.$message.error("更新失败")}},async submitDelete(){try{const s={...this.formData,username:localStorage.getItem("loginUsername")||"admin"};await this.$axios.post("/api/del_cmdb_monitored_ip_list",s),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(s){console.error("删除失败:",s),this.$message.error("删除失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={id:null,ip_address:"",description:"",status:"正常",last_checked:""},this.ipAddressError=!1,this.ipAddressErrorMessage=""},handleEdit(s,t){this.dialogVisible.edit=!0,this.formData.id=t.id,this.formData.ip_address=t.ip_address,this.formData.description=t.description,this.formData.last_checked=t.last_checked,this.formData.status=t.status,this.ipAddressError=!1,this.ipAddressErrorMessage=""},handleDelete(s,t){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=t.id,this.formData.ip_address=t.ip_address},exportData(){try{const s=this.$refs.table;if(!s){this.$message.error("表格实例不存在");return}const t=s.columns.filter(u=>u.visible!==!1);if(t.length===0){this.$message.error("没有可导出的列");return}const f=t.map(u=>u.label);if(!this.userArr||!Array.isArray(this.userArr)){this.$message.error("没有可导出的数据");return}const x=this.userArr.map(u=>t.map(n=>u[n.property]||"")),a=[f,...x],d=A.aoa_to_sheet(a),m=A.book_new();A.book_append_sheet(m,d,"Sheet1");const h=W(m,{bookType:"xlsx",type:"array"}),g=new Blob([h],{type:"application/octet-stream"});X.saveAs(g,"监控IP列表.xlsx")}catch(s){console.error("导出数据时发生错误:",s),this.$message.error("导出数据失败: "+s.message)}}}},_=s=>(Q("data-v-025620ee"),s=s(),R(),s),$={class:"user-manage"},ee={class:"dialogdiv"},se=_(()=>r("span",{class:"label"},"IP地址:",-1)),te={key:0,class:"error-message"},ae=_(()=>r("span",{class:"label"},"描述:",-1)),le=_(()=>r("span",{class:"label"},"状态:",-1)),oe=_(()=>r("span",{class:"label"},"最后检查时间:",-1)),re={class:"dialog-footer"},ie={class:"dialogdiv"},de=_(()=>r("span",{class:"label"},"IP地址:",-1)),ne={key:0,class:"error-message"},ce=_(()=>r("span",{class:"label"},"描述:",-1)),pe=_(()=>r("span",{class:"label"},"状态:",-1)),me=_(()=>r("span",{class:"label"},"最后检查时间:",-1)),ue={class:"dialog-footer"},_e={class:"button-container"},he={class:"action-bar unified-action-bar"},fe={class:"action-bar-left"},ge={class:"action-bar-right"},be={style:{display:"flex","white-space":"nowrap"}},De={class:"pagination"};function Ve(s,t,f,x,a,d){const m=i("el-input"),h=i("el-option"),g=i("el-select"),u=i("el-date-picker"),n=i("el-button"),D=i("el-dialog"),E=i("el-alert"),C=i("el-form-item"),P=i("el-col"),S=i("Search"),V=i("el-icon"),U=i("el-row"),M=i("el-form"),v=i("el-card"),Y=i("Plus"),z=i("Download"),p=i("el-table-column"),B=i("el-tag"),H=i("el-table"),N=i("el-pagination"),O=K("loading");return b(),y("div",$,[e(D,{modelValue:a.dialogVisible.add,"onUpdate:modelValue":t[5]||(t[5]=o=>a.dialogVisible.add=o),title:"添加监控IP",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[r("div",re,[e(n,{onClick:t[4]||(t[4]=o=>a.dialogVisible.add=!1)},{default:l(()=>[c("返回")]),_:1}),e(n,{type:"primary",onClick:d.submitAdd},{default:l(()=>[c("确定")]),_:1},8,["onClick"])])]),default:l(()=>[r("div",ee,[r("p",null,[se,e(m,{modelValue:a.formData.ip_address,"onUpdate:modelValue":t[0]||(t[0]=o=>a.formData.ip_address=o),style:{width:"240px"},clearable:"",onBlur:d.checkIpAddressExists,class:I({"error-input":a.ipAddressError})},null,8,["modelValue","onBlur","class"])]),a.ipAddressError?(b(),y("p",te,w(a.ipAddressErrorMessage),1)):k("",!0),r("p",null,[ae,e(m,{modelValue:a.formData.description,"onUpdate:modelValue":t[1]||(t[1]=o=>a.formData.description=o),style:{width:"240px"}},null,8,["modelValue"])]),r("p",null,[le,e(g,{modelValue:a.formData.status,"onUpdate:modelValue":t[2]||(t[2]=o=>a.formData.status=o),style:{width:"240px"},placeholder:"请选择状态"},{default:l(()=>[e(h,{label:"正常",value:"正常"}),e(h,{label:"异常",value:"异常"})]),_:1},8,["modelValue"])]),r("p",null,[oe,e(u,{modelValue:a.formData.last_checked,"onUpdate:modelValue":t[3]||(t[3]=o=>a.formData.last_checked=o),type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD HH:mm:ss",style:{width:"240px"},placeholder:"选择日期时间"},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),e(D,{modelValue:a.dialogVisible.edit,"onUpdate:modelValue":t[11]||(t[11]=o=>a.dialogVisible.edit=o),title:"编辑监控IP",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[r("div",ue,[e(n,{onClick:t[10]||(t[10]=o=>a.dialogVisible.edit=!1)},{default:l(()=>[c("取消")]),_:1}),e(n,{type:"primary",onClick:d.submitEdit},{default:l(()=>[c("更新")]),_:1},8,["onClick"])])]),default:l(()=>[r("div",ie,[r("p",null,[de,e(m,{modelValue:a.formData.ip_address,"onUpdate:modelValue":t[6]||(t[6]=o=>a.formData.ip_address=o),style:{width:"240px"},clearable:"",onBlur:d.checkIpAddressExists,class:I({"error-input":a.ipAddressError})},null,8,["modelValue","onBlur","class"])]),a.ipAddressError?(b(),y("p",ne,w(a.ipAddressErrorMessage),1)):k("",!0),r("p",null,[ce,e(m,{modelValue:a.formData.description,"onUpdate:modelValue":t[7]||(t[7]=o=>a.formData.description=o),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),r("p",null,[pe,e(g,{modelValue:a.formData.status,"onUpdate:modelValue":t[8]||(t[8]=o=>a.formData.status=o),style:{width:"240px"}},{default:l(()=>[e(h,{label:"正常",value:"正常"}),e(h,{label:"异常",value:"异常"})]),_:1},8,["modelValue"])]),r("p",null,[me,e(u,{modelValue:a.formData.last_checked,"onUpdate:modelValue":t[9]||(t[9]=o=>a.formData.last_checked=o),type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD HH:mm:ss",style:{width:"240px"},placeholder:"选择日期时间"},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),e(D,{modelValue:a.dialogVisible.delete,"onUpdate:modelValue":t[13]||(t[13]=o=>a.dialogVisible.delete=o),title:"删除监控IP",width:"500","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[r("div",null,[e(n,{onClick:t[12]||(t[12]=o=>a.dialogVisible.delete=!1)},{default:l(()=>[c("取消")]),_:1}),e(n,{type:"danger",onClick:d.submitDelete},{default:l(()=>[c("确认删除")]),_:1},8,["onClick"])])]),default:l(()=>[e(E,{type:"warning",title:`确定要删除 IP 为 ${a.formData.ip_address} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),e(v,{class:"search-card"},{default:l(()=>[e(M,{inline:!0},{default:l(()=>[e(U,{gutter:10},{default:l(()=>[e(P,{xs:24,sm:12,md:6,lg:6},{default:l(()=>[e(C,{label:"IP地址",class:"form-item-with-label"},{default:l(()=>[e(m,{modelValue:a.search.ip_address,"onUpdate:modelValue":t[14]||(t[14]=o=>a.search.ip_address=o),placeholder:"请输入IP地址",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(P,{xs:24,sm:12,md:18,lg:18,class:"search-buttons-col"},{default:l(()=>[e(C,{label:" ",class:"form-item-with-label search-buttons"},{default:l(()=>[r("div",_e,[e(n,{type:"primary",onClick:d.loadData},{default:l(()=>[e(V,null,{default:l(()=>[e(S)]),_:1}),c("查询 ")]),_:1},8,["onClick"]),e(n,{onClick:d.resetSearch},{default:l(()=>[c("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),r("div",he,[r("div",fe,[e(n,{type:"success",disabled:!a.hasInsertPermission,onClick:d.handleAdd},{default:l(()=>[e(V,null,{default:l(()=>[e(Y)]),_:1}),c("新增监控IP ")]),_:1},8,["disabled","onClick"])]),r("div",ge,[e(n,{type:"info",onClick:d.exportData},{default:l(()=>[e(V,null,{default:l(()=>[e(z)]),_:1}),c(" 导出数据 ")]),_:1},8,["onClick"])])]),e(v,{class:"table-card"},{default:l(()=>[J((b(),L(H,{data:a.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:d.handleSortChange},{default:l(()=>[k("",!0),e(p,{prop:"ip_address",label:"IP地址",sortable:""}),e(p,{prop:"description",label:"描述",sortable:""}),e(p,{prop:"status",label:"状态",sortable:""},{default:l(o=>[e(B,{type:o.row.status==="正常"?"success":"danger"},{default:l(()=>[c(w(o.row.status),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"last_checked",label:"最后检查时间",sortable:""}),e(p,{prop:"created_at",label:"创建时间",sortable:""}),e(p,{prop:"created_by",label:"创建人",sortable:""}),e(p,{prop:"updated_at",label:"更新时间",sortable:""}),e(p,{prop:"updated_by",label:"更新人",sortable:""}),e(p,{label:"操作",width:"140",align:"center",fixed:"right"},{default:l(o=>[r("div",be,[e(n,{size:"small",type:"warning",disabled:!a.hasUpdatePermission,onClick:F=>d.handleEdit(o.$index,o.row)},{default:l(()=>[c("编辑")]),_:2},1032,["disabled","onClick"]),e(n,{size:"small",type:"danger",disabled:!a.hasDeletePermission,onClick:F=>d.handleDelete(o.$index,o.row)},{default:l(()=>[c("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[O,a.loading]]),r("div",De,[e(N,{background:"","current-page":a.search.currentPage,"page-size":a.search.pageSize,total:a.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d.handlePageSizeChange,onCurrentChange:d.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const Ae=T(Z,[["render",Ve],["__scopeId","data-v-025620ee"]]);export{Ae as default};
