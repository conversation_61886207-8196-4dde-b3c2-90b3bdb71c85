import{_ as l,c as h,f as e,a as t,w as s,b as r,h as a,p,q as u,e as f}from"./index-MGgq8mV5.js";const m={name:"AIPlatform",data(){return{}},methods:{showDocumentEditor(){this.$router.push("/ai_document_editor")},showKnowledgeBase(){this.$router.push("/ai_knowledge_base")}}},o=c=>(p("data-v-a7ff0952"),c=c(),u(),c),v={class:"ai-platform"},w={class:"platform-container"},y=o(()=>e("h2",{class:"platform-title"},"AI平台",-1)),k={class:"card-container"},C={class:"card-header"},I=o(()=>e("h3",null,"文档智能修订",-1)),g={class:"card-content"},B=o(()=>e("p",{class:"card-description"}," 智能文档修订工具可以帮助您快速校对和优化文档内容，提高文档质量和专业性。 ",-1)),b={class:"card-header"},x=o(()=>e("h3",null,"知识海洋（知识库）",-1)),$={class:"card-content"},A=o(()=>e("p",{class:"card-description"}," 智能知识库系统，提供快速准确的信息检索和问答服务，助力团队知识共享与协作。 ",-1)),D={class:"card-header"},E=o(()=>e("h3",null,"智能数据分析",-1)),N={class:"card-content"},S=o(()=>e("p",{class:"card-description"}," 通过AI技术分析CMDB数据，自动发现潜在问题并提供优化建议。 ",-1));function V(c,K,q,M,P,i){const d=r("el-tag"),n=r("el-button"),_=r("el-card");return f(),h("div",v,[e("div",w,[y,e("div",k,[t(_,{class:"ai-card",shadow:"hover"},{header:s(()=>[e("div",C,[I,t(d,{type:"success"},{default:s(()=>[a("在线")]),_:1})])]),default:s(()=>[e("div",g,[B,t(n,{type:"primary",onClick:i.showDocumentEditor},{default:s(()=>[a("立即使用")]),_:1},8,["onClick"])])]),_:1}),t(_,{class:"ai-card",shadow:"hover"},{header:s(()=>[e("div",b,[x,t(d,{type:"success"},{default:s(()=>[a("在线")]),_:1})])]),default:s(()=>[e("div",$,[A,t(n,{type:"primary",onClick:i.showKnowledgeBase},{default:s(()=>[a("立即使用")]),_:1},8,["onClick"])])]),_:1}),t(_,{class:"ai-card coming-soon",shadow:"hover"},{header:s(()=>[e("div",D,[E,t(d,{type:"info"},{default:s(()=>[a("即将推出")]),_:1})])]),default:s(()=>[e("div",N,[S,t(n,{type:"info",disabled:""},{default:s(()=>[a("敬请期待")]),_:1})])]),_:1})])])])}const j=l(m,[["render",V],["__scopeId","data-v-a7ff0952"]]);export{j as default};
