import{_ as j,A as q,v as L,B as M,c as y,a,w as r,f as o,b as i,F as P,l as I,h as p,x as w,y as G,C as H,D as J,m as C,E as D,p as Q,q as W,e as f}from"./index-MGgq8mV5.js";import{u as v,w as X}from"./xlsx-DH6WiNtO.js";import{F as Y}from"./FileSaver.min-Cr9SGvul.js";const Z={components:{Plus:M,Search:L,Download:q},data(){var t,e,n;return{userArr:[],loading:!1,showRoleColumn:!1,hasDeletePermission:(t=localStorage.getItem("role_code"))==null?void 0:t.includes("D"),hasUpdatePermission:(e=localStorage.getItem("role_code"))==null?void 0:e.includes("U"),hasInsertPermission:(n=localStorage.getItem("role_code"))==null?void 0:n.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{username:"",total:0,pageSize:10,currentPage:1,loginUsername:"",sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,username:"",password:"",role_code:"",created_at:null,role_code_name:"",real_name:"",phone:"",email:"",wechat_id:"",calendar_duty_permission:""},selectedRoles:[],roleOptions:[{value:"I",label:"I:增"},{value:"D",label:"D:删"},{value:"U",label:"U:改"}]}},mounted(){this.search.loginUsername=localStorage.getItem("loginUsername")||"",this.loadAllData()},methods:{handlePageChange(t){this.search.currentPage=t,this.loadData()},handlePageSizeChange(t){this.search.pageSize=parseInt(t),this.search.currentPage=1,this.loadData()},updateRoleCode(t){this.formData.role_code=t.join(",")},handleSortChange({column:t,prop:e,order:n}){this.search.sortProp=e,this.search.sortOrder=n==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const t=await this.$axios.post("/api/get_cmdb_users",this.search);this.userArr=t.data.msg,this.search.total=t.data.total}catch(t){console.error("数据加载失败:",t),this.$message.error("数据加载失败")}finally{this.loading=!1}},async loadAllData(){this.search.username="",this.search.currentPage=1,await this.loadData()},resetSearch(){this.search.username="",this.search.currentPage=1,this.$refs.searchFormRef&&this.$refs.searchFormRef.resetFields(),this.loadData(),this.$message.success("搜索条件已重置")},async submitAdd(){if(this.formData.username=this.formData.username.trim(),!this.formData.username||!this.formData.password){this.$message.error("请输入用户名和密码！");return}const t={...this.formData};t.usernameby=this.search.loginUsername;try{await this.$axios.post("/api/add_cmdb_users",t),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(e){console.error("添加失败:",e),e.response&&e.response.data&&e.response.data.msg?this.$message.error(`添加失败: ${e.response.data.msg}`):this.$message.error("添加失败")}},async loadCalendarDutyPermission(t){try{const e=await this.$axios.post("/api/check_ops_calendar_duty_permission",{username:t});if(e.data.code===0){const n=e.data.msg;console.log("获取到的权限信息:",n),n.hasEditPermission?this.formData.calendar_duty_permission="edit":n.hasViewPermission?this.formData.calendar_duty_permission="view":this.formData.calendar_duty_permission="",console.log("设置的权限值:",this.formData.calendar_duty_permission)}else this.formData.calendar_duty_permission=""}catch(e){console.error("获取交易日历权限失败:",e),this.formData.calendar_duty_permission=""}},async saveCalendarDutyPermission(t,e){if(this.search.loginUsername==="admin")try{const n=await this.$axios.post("/api/update_ops_calendar_duty_permission",{username:t,permission_type:e,updated_by:this.search.loginUsername});n.data.code!==0&&console.error("保存交易日历权限失败:",n.data.msg)}catch(n){console.error("保存交易日历权限失败:",n)}},async submitEdit(){const t={...this.formData};t.usernameby=this.search.loginUsername,this.search.loginUsername!=="admin"&&(delete t.role_code,delete t.calendar_duty_permission);try{const e=await this.$axios.post("/api/update_cmdb_users",t);e.data.code===0?(this.search.loginUsername==="admin"&&await this.saveCalendarDutyPermission(this.formData.username,this.formData.calendar_duty_permission),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()):this.$message.error(`更新失败: ${e.data.msg}`)}catch(e){console.error("更新失败:",e),e.response&&e.response.data&&e.response.data.msg?this.$message.error(`更新失败: ${e.response.data.msg}`):this.$message.error("更新失败")}},async submitDelete(t){const e={id:this.formData.id,usernameby:this.search.loginUsername};try{const n=await this.$axios.post("/api/del_cmdb_users",e);n.data.code===0?D.success("删除成功"):D.error(`删除失败: ${n.data.msg}`),this.loadData(),this.dialogVisible.delete=!1}catch(n){console.error("删除失败:",n),n.response&&n.response.data&&n.response.data.msg?this.$message.error(`删除失败: ${n.response.data.msg}`):this.$message.error("删除失败")}},handleAdd(t,e){if(this.search.loginUsername!=="admin"){D.error("只有管理员可以添加用户");return}this.dialogVisible.add=!this.dialogVisible.add,this.formData={username:"",password:"",real_name:"",phone:"",email:"",wechat_id:"",role_code:""},this.selectedRoles=[]},async handleEdit(t,e){if(this.search.loginUsername!=="admin"&&e.username!==this.search.loginUsername){D.error("您只能编辑自己的信息");return}this.dialogVisible.edit=!0,this.formData.id=e.id,this.formData.username=e.username,this.formData.real_name=e.real_name||"",this.formData.phone=e.phone||"",this.formData.email=e.email||"",this.formData.wechat_id=e.wechat_id||"",this.formData.password="",this.formData.role_code=e.role_code,this.selectedRoles=e.role_code?e.role_code.replace(/^,|,$/g,"").split(","):[],this.search.loginUsername==="admin"&&await this.loadCalendarDutyPermission(e.username)},handleDelete(t,e){if(this.search.loginUsername!=="admin"){D.error("只有管理员可以删除用户");return}this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=e.id,this.formData.username=e.username},exportData(){const e=this.$refs.table.columns,n=e.map(u=>u.label),x=this.userArr.map(u=>e.map(g=>u[g.property])),l=[n,...x],d=v.aoa_to_sheet(l),m=v.book_new();v.book_append_sheet(m,d,"Sheet1");const _=X(m,{bookType:"xlsx",type:"array"}),b=new Blob([_],{type:"application/octet-stream"});Y.saveAs(b,"用户管理.xlsx")}}},c=t=>(Q("data-v-2ae4d084"),t=t(),W(),t),$={class:"user-manage"},ee={class:"dialogdiv"},ae=c(()=>o("span",{class:"label"},"用户名:",-1)),le=c(()=>o("span",{class:"label"},"用户姓名:",-1)),se=c(()=>o("span",{class:"label"},"联系电话:",-1)),oe=c(()=>o("span",{class:"label"},"电子邮箱:",-1)),te=c(()=>o("span",{class:"label"},"企业微信ID:",-1)),re=c(()=>o("span",{class:"label"},"初始密码:",-1)),ne=c(()=>o("span",{class:"label"},"权限代码:",-1)),ie={class:"dialog-footer"},de={class:"dialogdiv"},me=c(()=>o("span",{class:"label"},"用户名:",-1)),ue=c(()=>o("span",{class:"label"},"用户姓名:",-1)),ce=c(()=>o("span",{class:"label"},"联系电话:",-1)),pe=c(()=>o("span",{class:"label"},"电子邮箱:",-1)),he=c(()=>o("span",{class:"label"},"企业微信ID:",-1)),_e={key:0},fe=c(()=>o("span",{class:"label"},"交易日历权限:",-1)),be=c(()=>o("span",{class:"label"},"密码:",-1)),ge=c(()=>o("span",{class:"label"},"权限代码:",-1)),De={class:"dialog-footer"},ye={class:"button-container"},Ve={style:{display:"flex","white-space":"nowrap"}},we={class:"pagination"};function Ce(t,e,n,x,l,d){const m=i("el-input"),_=i("el-option"),b=i("el-select"),u=i("el-button"),g=i("el-dialog"),R=i("el-alert"),U=i("el-form-item"),k=i("el-col"),z=i("Search"),V=i("el-icon"),A=i("Plus"),B=i("Download"),E=i("el-row"),F=i("el-form"),S=i("el-card"),h=i("el-table-column"),O=i("el-table"),N=i("el-pagination"),K=J("loading");return f(),y("div",$,[a(g,{modelValue:l.dialogVisible.add,"onUpdate:modelValue":e[8]||(e[8]=s=>l.dialogVisible.add=s),title:"新增用户",width:"400","align-center":""},{footer:r(()=>[o("div",ie,[a(u,{onClick:e[7]||(e[7]=s=>l.dialogVisible.add=!1)},{default:r(()=>[p("返回")]),_:1}),a(u,{type:"primary",onClick:d.submitAdd},{default:r(()=>[p("确定")]),_:1},8,["onClick"])])]),default:r(()=>[o("div",ee,[o("p",null,[ae,a(m,{modelValue:l.formData.username,"onUpdate:modelValue":e[0]||(e[0]=s=>l.formData.username=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[le,a(m,{modelValue:l.formData.real_name,"onUpdate:modelValue":e[1]||(e[1]=s=>l.formData.real_name=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[se,a(m,{modelValue:l.formData.phone,"onUpdate:modelValue":e[2]||(e[2]=s=>l.formData.phone=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[oe,a(m,{modelValue:l.formData.email,"onUpdate:modelValue":e[3]||(e[3]=s=>l.formData.email=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[te,a(m,{modelValue:l.formData.wechat_id,"onUpdate:modelValue":e[4]||(e[4]=s=>l.formData.wechat_id=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[re,a(m,{modelValue:l.formData.password,"onUpdate:modelValue":e[5]||(e[5]=s=>l.formData.password=s),style:{width:"240px"},clearable:"",type:"password"},null,8,["modelValue"])]),o("p",null,[ne,a(b,{modelValue:l.selectedRoles,"onUpdate:modelValue":e[6]||(e[6]=s=>l.selectedRoles=s),multiple:"",style:{width:"240px"},placeholder:"请选择角色",onChange:d.updateRoleCode},{default:r(()=>[(f(!0),y(P,null,I(l.roleOptions,s=>(f(),C(_,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])])])]),_:1},8,["modelValue"]),a(g,{modelValue:l.dialogVisible.edit,"onUpdate:modelValue":e[18]||(e[18]=s=>l.dialogVisible.edit=s),title:"更新用户信息",width:"400","align-center":""},{footer:r(()=>[o("div",De,[a(u,{onClick:e[17]||(e[17]=s=>l.dialogVisible.edit=!1)},{default:r(()=>[p("取消")]),_:1}),a(u,{type:"primary",onClick:d.submitEdit},{default:r(()=>[p("更新")]),_:1},8,["onClick"])])]),default:r(()=>[o("div",de,[o("p",null,[me,a(m,{modelValue:l.formData.username,"onUpdate:modelValue":e[9]||(e[9]=s=>l.formData.username=s),style:{width:"240px"},clearable:"",disabled:""},null,8,["modelValue"])]),o("p",null,[ue,a(m,{modelValue:l.formData.real_name,"onUpdate:modelValue":e[10]||(e[10]=s=>l.formData.real_name=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[ce,a(m,{modelValue:l.formData.phone,"onUpdate:modelValue":e[11]||(e[11]=s=>l.formData.phone=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[pe,a(m,{modelValue:l.formData.email,"onUpdate:modelValue":e[12]||(e[12]=s=>l.formData.email=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),o("p",null,[he,a(m,{modelValue:l.formData.wechat_id,"onUpdate:modelValue":e[13]||(e[13]=s=>l.formData.wechat_id=s),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),l.search.loginUsername==="admin"?(f(),y("p",_e,[fe,a(b,{modelValue:l.formData.calendar_duty_permission,"onUpdate:modelValue":e[14]||(e[14]=s=>l.formData.calendar_duty_permission=s),style:{width:"240px"},placeholder:"请选择权限",clearable:""},{default:r(()=>[a(_,{label:"无权限",value:""}),a(_,{label:"查看权限（只能查看，无法编辑）",value:"view"}),a(_,{label:"编辑权限（可查看，可编辑）",value:"edit"})]),_:1},8,["modelValue"])])):w("",!0),o("p",null,[be,a(m,{modelValue:l.formData.password,"onUpdate:modelValue":e[15]||(e[15]=s=>l.formData.password=s),style:{width:"240px"},clearable:"",placeholder:"为空不会更新密码",type:"password"},null,8,["modelValue"])]),o("p",null,[ge,a(b,{modelValue:l.selectedRoles,"onUpdate:modelValue":e[16]||(e[16]=s=>l.selectedRoles=s),multiple:"",style:{width:"240px"},placeholder:"请选择角色",disabled:l.search.loginUsername!=="admin",onChange:d.updateRoleCode},{default:r(()=>[(f(!0),y(P,null,I(l.roleOptions,s=>(f(),C(_,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled","onChange"])])])]),_:1},8,["modelValue"]),a(g,{modelValue:l.dialogVisible.delete,"onUpdate:modelValue":e[20]||(e[20]=s=>l.dialogVisible.delete=s),title:"删除用户",width:"500","align-center":""},{footer:r(()=>[o("div",null,[a(u,{onClick:e[19]||(e[19]=s=>l.dialogVisible.delete=!1)},{default:r(()=>[p("取消")]),_:1}),a(u,{type:"danger",onClick:d.submitDelete},{default:r(()=>[p("确认删除")]),_:1},8,["onClick"])])]),default:r(()=>[a(R,{type:"warning",title:`确定要删除 IP 为 ${l.formData.username} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),a(S,{class:"search-card"},{default:r(()=>[a(F,{model:l.search,ref:"searchFormRef","label-width":"80px","label-position":"right"},{default:r(()=>[a(E,{gutter:20},{default:r(()=>[a(k,{xs:24,sm:12,md:8,lg:6},{default:r(()=>[a(U,{label:"用户名"},{default:r(()=>[a(m,{modelValue:l.search.username,"onUpdate:modelValue":e[21]||(e[21]=s=>l.search.username=s),placeholder:"请输入用户名",clearable:"",onKeyup:G(d.loadData,["enter"]),class:"form-control"},null,8,["modelValue","onKeyup"])]),_:1})]),_:1}),a(k,{xs:24,sm:12,md:8,lg:18,class:"search-buttons-col"},{default:r(()=>[a(U,null,{default:r(()=>[o("div",ye,[a(u,{type:"primary",onClick:d.loadData},{default:r(()=>[a(V,null,{default:r(()=>[a(z)]),_:1}),p("查询 ")]),_:1},8,["onClick"]),a(u,{onClick:d.resetSearch},{default:r(()=>[p("重置")]),_:1},8,["onClick"]),a(u,{type:"success",disabled:l.search.loginUsername!=="admin",onClick:d.handleAdd},{default:r(()=>[a(V,null,{default:r(()=>[a(A)]),_:1}),p("新增 ")]),_:1},8,["disabled","onClick"]),a(u,{type:"info",onClick:d.exportData},{default:r(()=>[a(V,null,{default:r(()=>[a(B)]),_:1}),p("导出 ")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(S,{class:"table-card"},{default:r(()=>[H((f(),C(O,{data:l.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:d.handleSortChange},{default:r(()=>[w("",!0),a(h,{prop:"username",label:"用户名",sortable:""}),a(h,{prop:"real_name",label:"用户姓名",sortable:""}),a(h,{prop:"phone",label:"联系电话",sortable:""}),a(h,{prop:"email",label:"电子邮箱",sortable:""}),a(h,{prop:"wechat_id",label:"企业微信ID",sortable:""}),a(h,{prop:"role_code_name",label:"权限",sortable:""}),w("",!0),a(h,{prop:"created_at",label:"创建时间",sortable:""}),a(h,{prop:"created_by",label:"创建人",sortable:""}),a(h,{prop:"updated_at",label:"更新时间",sortable:""}),a(h,{prop:"updated_by",label:"更新人",sortable:""}),a(h,{label:"操作",fixed:"right"},{default:r(s=>[o("div",Ve,[a(u,{size:"small",type:"warning",disabled:l.search.loginUsername!=="admin"&&s.row.username!==l.search.loginUsername,onClick:T=>d.handleEdit(s.$index,s.row)},{default:r(()=>[p("编辑")]),_:2},1032,["disabled","onClick"]),a(u,{size:"small",type:"danger",disabled:l.search.loginUsername!=="admin",onClick:T=>d.handleDelete(s.$index,s.row)},{default:r(()=>[p("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[K,l.loading]]),o("div",we,[a(N,{background:"","current-page":l.search.currentPage,"page-size":l.search.pageSize,total:l.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d.handlePageSizeChange,onCurrentChange:d.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const ke=j(Z,[["render",Ce],["__scopeId","data-v-2ae4d084"]]);export{ke as default};
