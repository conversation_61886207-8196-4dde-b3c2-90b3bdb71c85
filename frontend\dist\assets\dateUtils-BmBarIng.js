const D=t=>{const n=t.getFullYear(),r=String(t.getMonth()+1).padStart(2,"0"),o=String(t.getDate()).padStart(2,"0");return`${n}-${r}-${o}`},l=t=>{const n=t.getFullYear(),r=String(t.getMonth()+1).padStart(2,"0"),o=String(t.getDate()).padStart(2,"0");return`${n}${r}${o}`},i=t=>t.replace(/-/g,""),S=t=>{if(!t||t.length!==8)return"";const n=t.substring(0,4),r=t.substring(4,6),o=t.substring(6,8);return`${n}-${r}-${o}`},u=t=>{if(!t)return"";if(typeof t=="string"){if(t.includes("-"))return i(t);if(t.length===8)return t}else if(t instanceof Date)return l(t);return""},g=(t,n="YYYY-MM-DD")=>t?n==="YYYY-MM-DD"?/^\d{4}-\d{2}-\d{2}$/.test(t):n==="YYYYMMDD"?/^\d{8}$/.test(t):!1:!1,Y=(t,n)=>{const r=`${t}${n.toString().padStart(2,"0")}01`,o=`${t}${n.toString().padStart(2,"0")}31`;return{startDate:r,endDate:o}},$=t=>{if(!t)return"";try{let n;if(typeof t=="string")if(t.includes("T"))n=new Date(t);else{if(t.includes(" "))return t.split(" ")[0];if(t.length===10&&t.includes("-"))return t;n=new Date(t)}else if(t instanceof Date)n=t;else return String(t);if(isNaN(n.getTime()))return String(t);const r=n.getFullYear(),o=String(n.getMonth()+1).padStart(2,"0"),s=String(n.getDate()).padStart(2,"0");return`${r}-${o}-${s}`}catch(n){return console.error("日期转换错误:",n,"原始值:",t),String(t)}},p=t=>{if(!t)return"";try{const n=new Date(t);if(isNaN(n.getTime()))return String(t);const r=n.getFullYear(),o=String(n.getMonth()+1).padStart(2,"0"),s=String(n.getDate()).padStart(2,"0"),c=String(n.getHours()).padStart(2,"0"),a=String(n.getMinutes()).padStart(2,"0"),e=String(n.getSeconds()).padStart(2,"0");return`${r}-${o}-${s} ${c}:${a}:${e}`}catch(n){return console.error("日期时间转换错误:",n,"原始值:",t),String(t)}},d=()=>{console.log("=== 日期转换测试 ===");const t=new Date(2025,4,16);console.log("测试日期:",t);const n=D(t);console.log("前端格式:",n);const r=l(t);console.log("数据库格式:",r);const o=i(n);console.log("前端->数据库:",o);const s=S(r);console.log("数据库->前端:",s);const c=u(n);console.log("解析结果:",c),console.log("验证前端格式:",g(n,"YYYY-MM-DD")),console.log("验证数据库格式:",g(r,"YYYYMMDD"));const a=Y(2025,5);console.log("5月份范围:",a);const e="2025-05-16T16:30:00.000Z",f=$(e);console.log("时区安全转换:",e,"->",f),console.log("=== 测试完成 ===")};export{l as a,D as b,p as f,u as p,$ as s,d as t};
