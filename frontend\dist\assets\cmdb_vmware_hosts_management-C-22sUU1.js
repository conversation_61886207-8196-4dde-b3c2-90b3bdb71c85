import{_ as be,r as x,R as D,o as we,c as U,f as l,a as e,w as t,b as i,t as c,u as y,J as Se,a1 as xe,a2 as Pe,a3 as Ce,h as n,G as Y,v as Ve,a4 as ke,A as Ie,O as ze,x as Z,E as d,p as Ee,q as Te,Q as ee,e as B}from"./index-MGgq8mV5.js";import{s as P}from"./request-DGO27LS-.js";import{F as De}from"./FileSaver.min-Cr9SGvul.js";import{u as H,w as Ue}from"./xlsx-DH6WiNtO.js";const b=O=>(Ee("data-v-092d10ee"),O=O(),Te(),O),Be={class:"page-container"},Oe={class:"stats-cards"},Me={class:"stats-content"},$e={class:"stats-number"},je=b(()=>l("div",{class:"stats-label"},"虚拟机总数",-1)),Ne={class:"stats-content"},Re={class:"stats-number"},Xe=b(()=>l("div",{class:"stats-label"},"有IP虚拟机",-1)),Fe={class:"stats-content"},Le={class:"stats-number"},We=b(()=>l("div",{class:"stats-label"},"vCenter数量",-1)),Ae={class:"stats-content"},He={class:"stats-number"},qe=b(()=>l("div",{class:"stats-label"},"ESXi主机数",-1)),Ge={class:"card-header"},Je=b(()=>l("span",null,"同步状态",-1)),Qe={class:"header-actions"},Ke={class:"sync-status-content"},Ye={class:"sync-info-item"},Ze=b(()=>l("div",{class:"sync-info-label"},"服务状态",-1)),et={class:"sync-info-value"},tt={class:"sync-info-item"},st=b(()=>l("div",{class:"sync-info-label"},"最后同步时间",-1)),at={class:"sync-info-value"},lt={class:"sync-info-item"},ot=b(()=>l("div",{class:"sync-info-label"},"数据源地址",-1)),nt={class:"sync-info-value"},ct={class:"data-source-url"},rt={class:"card-header"},it=b(()=>l("span",null,"虚拟机列表",-1)),dt={class:"table-actions"},_t=["onClick","title"],ut={key:0},pt={key:1,class:"no-data"},mt={style:{display:"flex",gap:"8px","justify-content":"center"}},ft={class:"pagination-container"},vt={key:0,class:"detail-content"},ht={key:0,style:{"margin-top":"20px"}},gt=b(()=>l("h4",null,"同步错误信息：",-1)),yt={__name:"cmdb_vmware_hosts_management",setup(O){const q=x([]),N=x(!1),R=x(!1),X=x(!1),I=x([]),M=x(!1),p=x(null),F=x(!1),m=D({vm_name:"",vcenter_ip:"",esxi_ip:"",vm_ip:"",sync_status:""}),r=D({currentPage:1,pageSize:20,total:0}),z=D({sortProp:"last_sync_time",sortOrder:"desc"}),T=D({totalVms:0,vmsWithIp:0,vmsWithoutIp:0,syncSuccess:0,syncFailed:0,totalVcenters:0,totalEsxiHosts:0,lastSyncTime:null,lastDataSourceTime:null}),C=D({isRunning:!1,lastSyncTime:null,dataSourceUrl:"",syncInterval:""}),w=async()=>{try{N.value=!0;const s=await P.post("/api/get_vmware_hosts",{...m,currentPage:r.currentPage,pageSize:r.pageSize,sortProp:z.sortProp,sortOrder:z.sortOrder});s.code===0?(q.value=s.msg,r.total=s.total):d.error(s.msg||"获取数据失败")}catch(s){console.error("获取VMware虚拟机列表失败:",s),d.error("获取数据失败")}finally{N.value=!1}},$=async()=>{try{const s=await P.post("/api/get_vmware_statistics");s.code===0&&Object.assign(T,s.msg)}catch(s){console.error("获取统计信息失败:",s)}},L=async()=>{try{const s=await P.post("/api/get_vmware_sync_status");s.code===0&&Object.assign(C,s.msg)}catch(s){console.error("获取同步状态失败:",s)}},te=()=>{L()},se=async()=>{try{R.value=!0;const s=await P.post("/api/trigger_vmware_sync");s.code===0?(d.success("同步任务已启动"),setTimeout(()=>{w(),$(),L()},2e3)):d.error(s.msg||"同步失败")}catch(s){console.error("手动同步失败:",s),d.error("同步失败")}finally{R.value=!1}},ae=async()=>{try{X.value=!0;const s=await P.post("/api/get_vmware_hosts",{...m,currentPage:1,pageSize:999999,sortProp:z.sortProp,sortOrder:z.sortOrder});if(s.code===0){const a=s.msg.map(u=>({虚拟机名称:u.vm_name||"",虚拟机IP:u.vm_ip||"","vCenter IP":u.vcenter_ip||"",ESXi主机IP:u.esxi_ip||"",数据源时间:V(u.data_source_time)||"",同步状态:W(u.sync_status)||"",最后同步时间:V(u.last_sync_time)||"",创建时间:u.created_at_formatted||"",更新时间:u.updated_at_formatted||""})),f=H.json_to_sheet(a),h=H.book_new();H.book_append_sheet(h,f,"虚拟机列表");const _=[{wch:25},{wch:15},{wch:15},{wch:15},{wch:20},{wch:10},{wch:20},{wch:20},{wch:20}];f["!cols"]=_;const k=Ue(h,{bookType:"xlsx",type:"array"}),v=new Blob([k],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),E=`虚拟机列表_${new Date().toISOString().slice(0,10).replace(/-/g,"")}.xlsx`;De.saveAs(v,E),d.success(`导出成功，共导出 ${a.length} 条数据`)}else d.error(s.msg||"获取导出数据失败")}catch(s){console.error("导出失败:",s),d.error("导出失败")}finally{X.value=!1}},le=()=>{r.currentPage=1,w()},oe=()=>{Object.keys(m).forEach(s=>{m[s]=""}),r.currentPage=1,w()},ne=s=>{I.value=s},ce=({prop:s,order:a})=>{z.sortProp=s||"last_sync_time",z.sortOrder=a==="ascending"?"asc":"desc",w()},re=()=>{const s=[10,20,50,100];return r.total>100&&s.push(r.total),s},ie=s=>{s===r.total?(F.value=!0,r.pageSize=r.total,r.currentPage=1):(F.value=!1,r.pageSize=s,r.currentPage=1),w()},de=s=>{r.currentPage=s,w()},G=async s=>{try{const a=await P.post("/api/get_vmware_host_detail",{id:s.id});a.code===0?(p.value=a.msg,M.value=!0):d.error(a.msg||"获取详情失败")}catch(a){console.error("获取详情失败:",a),d.error("获取详情失败")}},_e=async s=>{try{await ee.confirm(`确定要删除虚拟机 "${s.vm_name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=await P.post("/api/delete_vmware_host",{id:s.id,username:"admin"});a.code===0?(d.success("删除成功"),w(),$()):d.error(a.msg||"删除失败")}catch(a){a!=="cancel"&&(console.error("删除失败:",a),d.error("删除失败"))}},ue=async()=>{if(I.value.length===0){d.warning("请选择要删除的记录");return}try{await ee.confirm(`确定要删除选中的 ${I.value.length} 条记录吗？`,"确认批量删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const s=I.value.map(f=>f.id),a=await P.post("/api/batch_delete_vmware_hosts",{ids:s,username:"admin"});a.code===0?(d.success("批量删除成功"),w(),$(),I.value=[]):d.error(a.msg||"批量删除失败")}catch(s){s!=="cancel"&&(console.error("批量删除失败:",s),d.error("批量删除失败"))}},J=s=>({success:"success",failed:"danger",pending:"warning"})[s]||"info",W=s=>({success:"成功",failed:"失败",pending:"待同步"})[s]||s,V=s=>s?new Date(s).toLocaleString("zh-CN"):"";return we(()=>{w(),$(),L()}),(s,a)=>{const f=i("el-icon"),h=i("el-card"),_=i("el-col"),k=i("el-row"),v=i("el-button"),j=i("el-tag"),Q=i("el-tooltip"),E=i("el-input"),u=i("el-form-item"),A=i("el-option"),pe=i("el-select"),me=i("el-form"),S=i("el-table-column"),fe=i("el-table"),ve=i("el-pagination"),g=i("el-descriptions-item"),he=i("el-descriptions"),ge=i("el-alert"),ye=i("el-dialog");return B(),U("div",Be,[l("div",Oe,[e(k,{gutter:20},{default:t(()=>[e(_,{span:6},{default:t(()=>[e(h,{class:"stats-card"},{default:t(()=>[l("div",Me,[l("div",$e,c(T.totalVms),1),je]),e(f,{class:"stats-icon"},{default:t(()=>[e(y(Se))]),_:1})]),_:1})]),_:1}),e(_,{span:6},{default:t(()=>[e(h,{class:"stats-card connected"},{default:t(()=>[l("div",Ne,[l("div",Re,c(T.vmsWithIp),1),Xe]),e(f,{class:"stats-icon"},{default:t(()=>[e(y(xe))]),_:1})]),_:1})]),_:1}),e(_,{span:6},{default:t(()=>[e(h,{class:"stats-card warning"},{default:t(()=>[l("div",Fe,[l("div",Le,c(T.totalVcenters),1),We]),e(f,{class:"stats-icon"},{default:t(()=>[e(y(Pe))]),_:1})]),_:1})]),_:1}),e(_,{span:6},{default:t(()=>[e(h,{class:"stats-card info"},{default:t(()=>[l("div",Ae,[l("div",He,c(T.totalEsxiHosts),1),qe]),e(f,{class:"stats-icon"},{default:t(()=>[e(y(Ce))]),_:1})]),_:1})]),_:1})]),_:1})]),e(h,{class:"sync-status-card"},{header:t(()=>[l("div",Ge,[Je,l("div",Qe,[e(v,{type:"primary",onClick:se,loading:R.value},{default:t(()=>[e(f,null,{default:t(()=>[e(y(Y))]),_:1}),n(" 手动同步 ")]),_:1},8,["loading"]),e(v,{type:"success",onClick:te},{default:t(()=>[e(f,null,{default:t(()=>[e(y(Y))]),_:1}),n(" 刷新状态 ")]),_:1})])])]),default:t(()=>[l("div",Ke,[e(k,{gutter:24},{default:t(()=>[e(_,{span:6},{default:t(()=>[l("div",Ye,[Ze,l("div",et,[e(j,{type:C.isRunning?"success":"danger",size:"small"},{default:t(()=>[n(c(C.isRunning?"运行中":"已停止"),1)]),_:1},8,["type"])])])]),_:1}),e(_,{span:6},{default:t(()=>[l("div",tt,[st,l("div",at,c(V(C.lastSyncTime)||"未同步"),1)])]),_:1}),e(_,{span:12},{default:t(()=>[l("div",lt,[ot,l("div",nt,[e(Q,{content:C.dataSourceUrl,placement:"top",disabled:!C.dataSourceUrl},{default:t(()=>[l("span",ct,c(C.dataSourceUrl||"未配置"),1)]),_:1},8,["content","disabled"])])])]),_:1})]),_:1})])]),_:1}),e(h,{class:"search-card"},{default:t(()=>[e(me,{model:m,class:"search-form"},{default:t(()=>[e(k,{gutter:16},{default:t(()=>[e(_,{span:6},{default:t(()=>[e(u,{label:"虚拟机IP"},{default:t(()=>[e(E,{modelValue:m.vm_ip,"onUpdate:modelValue":a[0]||(a[0]=o=>m.vm_ip=o),placeholder:"请输入虚拟机IP",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:6},{default:t(()=>[e(u,{label:"虚拟机名称"},{default:t(()=>[e(E,{modelValue:m.vm_name,"onUpdate:modelValue":a[1]||(a[1]=o=>m.vm_name=o),placeholder:"请输入虚拟机名称",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:6},{default:t(()=>[e(u,{label:"vCenter IP"},{default:t(()=>[e(E,{modelValue:m.vcenter_ip,"onUpdate:modelValue":a[2]||(a[2]=o=>m.vcenter_ip=o),placeholder:"请输入vCenter IP",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(_,{span:6},{default:t(()=>[e(u,{label:"ESXi主机IP"},{default:t(()=>[e(E,{modelValue:m.esxi_ip,"onUpdate:modelValue":a[3]||(a[3]=o=>m.esxi_ip=o),placeholder:"请输入ESXi主机IP",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(k,{gutter:16},{default:t(()=>[e(_,{span:6},{default:t(()=>[e(u,{label:"同步状态"},{default:t(()=>[e(pe,{modelValue:m.sync_status,"onUpdate:modelValue":a[4]||(a[4]=o=>m.sync_status=o),placeholder:"请选择同步状态",clearable:""},{default:t(()=>[e(A,{label:"成功",value:"success"}),e(A,{label:"失败",value:"failed"}),e(A,{label:"待同步",value:"pending"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(_,{span:18})]),_:1}),e(k,{gutter:16},{default:t(()=>[e(_,{span:24,class:"search-buttons"},{default:t(()=>[e(u,null,{default:t(()=>[e(v,{type:"primary",onClick:le},{default:t(()=>[e(f,null,{default:t(()=>[e(y(Ve))]),_:1}),n(" 搜索 ")]),_:1}),e(v,{onClick:oe},{default:t(()=>[e(f,null,{default:t(()=>[e(y(ke))]),_:1}),n(" 重置 ")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(h,{class:"table-card"},{header:t(()=>[l("div",rt,[it,l("div",dt,[e(v,{type:"info",onClick:ae,loading:X.value},{default:t(()=>[e(f,null,{default:t(()=>[e(y(Ie))]),_:1}),n(" 导出数据 ")]),_:1},8,["loading"]),e(v,{type:"danger",disabled:I.value.length===0,onClick:ue},{default:t(()=>[e(f,null,{default:t(()=>[e(y(ze))]),_:1}),n(" 批量删除 ")]),_:1},8,["disabled"])])])]),default:t(()=>[e(fe,{data:q.value,loading:N.value,onSelectionChange:ne,onSortChange:ce,stripe:"",border:"",style:{width:"100%"}},{default:t(()=>[e(S,{type:"selection",width:"55"}),e(S,{prop:"vm_name",label:"虚拟机名称","min-width":"200",sortable:"custom","show-overflow-tooltip":""},{default:t(({row:o})=>[l("span",{class:"vm-name-link",onClick:K=>G(o),title:o.vm_name},c(o.vm_name),9,_t)]),_:1}),e(S,{prop:"vcenter_ip",label:"vCenter IP",width:"140",sortable:"custom"}),e(S,{prop:"esxi_ip",label:"ESXi主机IP",width:"140",sortable:"custom"}),e(S,{prop:"vm_ip",label:"虚拟机IP",width:"140",sortable:"custom"},{default:t(({row:o})=>[o.vm_ip?(B(),U("span",ut,c(o.vm_ip),1)):(B(),U("span",pt,"-"))]),_:1}),e(S,{prop:"data_source_time",label:"数据源时间",width:"160",sortable:"custom"},{default:t(({row:o})=>[n(c(V(o.data_source_time)),1)]),_:1}),e(S,{prop:"sync_status",label:"同步状态",width:"100",sortable:"custom"},{default:t(({row:o})=>[e(j,{type:J(o.sync_status)},{default:t(()=>[n(c(W(o.sync_status)),1)]),_:2},1032,["type"])]),_:1}),e(S,{prop:"last_sync_time",label:"最后同步时间",width:"160",sortable:"custom"},{default:t(({row:o})=>[n(c(V(o.last_sync_time)),1)]),_:1}),e(S,{label:"操作",width:"140",fixed:"right",align:"center"},{default:t(({row:o})=>[l("div",mt,[e(v,{size:"small",type:"primary",onClick:K=>G(o)},{default:t(()=>[n(" 详情 ")]),_:2},1032,["onClick"]),e(v,{size:"small",type:"danger",onClick:K=>_e(o)},{default:t(()=>[n(" 删除 ")]),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data","loading"]),l("div",ft,[e(ve,{"current-page":r.currentPage,"onUpdate:currentPage":a[5]||(a[5]=o=>r.currentPage=o),"page-size":r.pageSize,"onUpdate:pageSize":a[6]||(a[6]=o=>r.pageSize=o),"page-sizes":re(),total:r.total,layout:F.value?"total":"total, sizes, prev, pager, next, jumper",onSizeChange:ie,onCurrentChange:de},null,8,["current-page","page-size","page-sizes","total","layout"])])]),_:1}),e(ye,{modelValue:M.value,"onUpdate:modelValue":a[8]||(a[8]=o=>M.value=o),title:"虚拟机详情",width:"60%","close-on-click-modal":!1},{footer:t(()=>[e(v,{onClick:a[7]||(a[7]=o=>M.value=!1)},{default:t(()=>[n("关闭")]),_:1})]),default:t(()=>[p.value?(B(),U("div",vt,[e(he,{column:2,border:""},{default:t(()=>[e(g,{label:"虚拟机名称"},{default:t(()=>[n(c(p.value.vm_name),1)]),_:1}),e(g,{label:"vCenter IP"},{default:t(()=>[n(c(p.value.vcenter_ip||"-"),1)]),_:1}),e(g,{label:"ESXi主机IP"},{default:t(()=>[n(c(p.value.esxi_ip||"-"),1)]),_:1}),e(g,{label:"虚拟机IP"},{default:t(()=>[n(c(p.value.vm_ip||"-"),1)]),_:1}),e(g,{label:"数据源时间"},{default:t(()=>[n(c(V(p.value.data_source_time)||"-"),1)]),_:1}),e(g,{label:"最后同步时间"},{default:t(()=>[n(c(V(p.value.last_sync_time)),1)]),_:1}),e(g,{label:"同步状态"},{default:t(()=>[e(j,{type:J(p.value.sync_status)},{default:t(()=>[n(c(W(p.value.sync_status)),1)]),_:1},8,["type"])]),_:1}),e(g,{label:"创建时间"},{default:t(()=>[n(c(p.value.created_at_formatted),1)]),_:1}),e(g,{label:"更新时间"},{default:t(()=>[n(c(p.value.updated_at_formatted),1)]),_:1}),e(g,{label:"版本号"},{default:t(()=>[n(c(p.value.version_num),1)]),_:1})]),_:1}),p.value.sync_error_message?(B(),U("div",ht,[gt,e(ge,{title:p.value.sync_error_message,type:"error",closable:!1,"show-icon":""},null,8,["title"])])):Z("",!0)])):Z("",!0)]),_:1},8,["modelValue"])])}}},Pt=be(yt,[["__scopeId","data-v-092d10ee"]]);export{Pt as default};
