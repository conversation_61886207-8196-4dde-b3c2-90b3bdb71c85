const fs = require('fs');
const path = require('path');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const { connPG } = require('../db/pg');

async function checkImportanceDict() {
    try {
        console.log('检查重要性数据字典...');
        
        // 查询重要性字典数据
        const dictQuery = `
            SELECT dict_type, dict_code, dict_name, del_flag
            FROM cmdb_data_dictionary 
            WHERE dict_type = 'importance'
            ORDER BY dict_code
        `;
        
        const dictResult = await connPG.query(dictQuery);
        console.log('重要性字典数据:', dictResult.rows);
        
        // 测试数据字典API
        console.log('\n测试数据字典API...');
        const apiQuery = `
            SELECT dict_code, dict_name
            FROM cmdb_data_dictionary
            WHERE del_flag = '0' AND dict_type = 'importance'
            ORDER BY dict_code ASC
        `;
        
        const apiResult = await connPG.query(apiQuery);
        console.log('API返回数据:', apiResult.rows);
        
        console.log('\n检查完成！');
        
    } catch (error) {
        console.error('检查失败:', error);
    } finally {
        process.exit(0);
    }
}

checkImportanceDict();
