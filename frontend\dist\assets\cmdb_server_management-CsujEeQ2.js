import{_ as B,A as N,v as G,B as O,c as f,a as l,f as v,w as r,b as h,F as b,l as g,h as V,C as H,D as j,m as c,x as K,t as k,e as i}from"./index-MGgq8mV5.js";import{u as I,w as J}from"./xlsx-DH6WiNtO.js";import{F as Q}from"./FileSaver.min-Cr9SGvul.js";const W={components:{Plus:O,Search:G,Download:N},data(){var n,a,_;return{userArr:[],loading:!1,servertypes:[],productionattributes:[],datacenters:[],operationstatus:[],operatingsystems:[],usersList:[],hasDeletePermission:(n=localStorage.getItem("role_code"))==null?void 0:n.includes("D"),hasUpdatePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("U"),hasInsertPermission:(_=localStorage.getItem("role_code"))==null?void 0:_.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{management_ip:"",hostname:"",admin1:"",admin2:"",server_type:"",production_attributes:"",data_center:[],out_of_band_management_ilo:"",operation_status:"",is_monitored:"",online_status:"",os_category:"",operating_system:"",year_category:"",monitoring_requirement:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:"",management_ip:"",hostname:"",function_purpose:"",admin1:"",admin2:"",server_type:"",production_attributes:"",data_center:"",out_of_band_management_ilo:"",operation_status:"",is_innovative_tech:"",asset_number:"",purchase_date:"",maintenance_years:"",maintenance_end_date:"",serial_number:"",server_model:"",is_monitored:"",cpu_model:"",memory:"",disk:"",network_card:"",operating_system:"",os_category:"",remarks:"",year_category:"",is_single_point:"",managed_addresses:"",monitoring_requirement:"",monitoring_requirement_description:""},rules:{management_ip:[{required:!0,message:"请输入管理IP",trigger:"blur"},{pattern:/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"},{validator:(x,t,p)=>{if(!t){p();return}this.checkManagementIpDuplicate(t,this.formData.id||null).then(d=>{d.exists?p(new Error(d.msg)):p()}).catch(d=>{console.error("检查管理IP失败:",d),p(new Error("检查管理IP失败，请稍后重试"))})},trigger:"blur"}],hostname:[{required:!0,message:"请输入主机名",trigger:"blur"},{min:2,max:50,message:"主机名长度应在2-50个字符之间",trigger:"blur"}],function_purpose:[{required:!0,message:"请输入功能用途",trigger:"blur"}],admin1:[{required:!0,message:"请选择管理员1",trigger:"change"}],server_type:[{required:!0,message:"请选择服务器类型",trigger:"change"}],production_attributes:[{required:!0,message:"请选择生产属性",trigger:"change"}],operation_status:[{required:!0,message:"请选择生命周期",trigger:"change"}],data_center:[{required:!0,message:"请选择所属机房",trigger:"change"}],is_innovative_tech:[{required:!0,message:"请选择是否信创",trigger:"change"}],managed_addresses:[{required:!1,validator:(x,t,p)=>{if(this.formData.is_single_point==="否")if(!t)p(new Error("当服务器不是单点时，互备主机IP为必填项"));else{const d=t.split(","),o=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;d.filter(u=>!o.test(u.trim())).length>0?p(new Error("请输入正确的IP地址格式，多个IP请用英文逗号分隔")):p()}else p()},trigger:"blur"}],monitoring_requirement:[{required:!0,message:"请选择需要监控",trigger:"change"}],monitoring_requirement_description:[{required:!1,validator:(x,t,p)=>{this.formData.monitoring_requirement==="否"&&(!t||t.trim()==="")?p(new Error('当需要监控为"否"时，不监控原因为必填项')):p()},trigger:"blur"}]}}},mounted(){this.$route.query.search_ip&&(this.search.management_ip=this.$route.query.search_ip),this.loadData(),this.getDatadict("E","servertypes"),this.getDatadict("C","productionattributes"),this.getDatadict("A","datacenters"),this.getDatadict("D","operationstatus"),this.getDatadict("K","operatingsystems"),this.loadUsersList(),this.$route.query.from_discovery==="true"&&this.$nextTick(()=>{this.handleAddFromDiscovery()})},methods:{getLifecycleTagType(n){switch(n){case"正常":return"success";case"故障":return"danger";case"闲置":return"info";case"报废":return"info";case"预报废":return"warning";default:return n&&n.includes("正常")?"success":n&&n.includes("故障")?"danger":n&&n.includes("闲置")||n&&n.includes("报废")&&!n.includes("预")?"info":n&&n.includes("预报废")?"warning":"info"}},handlePageChange(n){this.search.currentPage=n,this.loadData()},handlePageSizeChange(n){this.search.pageSize=parseInt(n),this.search.currentPage=1,this.loadData()},handleSortChange({column:n,prop:a,order:_}){this.search.sortProp=a,this.search.sortOrder=_==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const n=await this.$axios.post("/api/get_cmdb_server_management",this.search);this.userArr=n.data.msg,this.search.total=n.data.total}catch(n){console.error("数据加载失败:",n),this.$message.error("数据加载失败")}finally{this.loading=!1}},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){var n,a;try{const _={...this.formData,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/add_cmdb_server_management",_),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(_){console.error("添加失败:",_),this.$message.error(((a=(n=_.response)==null?void 0:n.data)==null?void 0:a.msg)||"添加失败")}},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){var n,a;try{const _={...this.formData,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/update_cmdb_server_management",_),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(_){console.error("更新失败:",_),this.$message.error(((a=(n=_.response)==null?void 0:n.data)==null?void 0:a.msg)||"更新失败")}},async submitDelete(n){try{const a={...this.formData,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/del_cmdb_server_management",a),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(a){console.error("删除失败:",a),this.$message.error("删除失败")}},resetSearch(){this.search={management_ip:"",hostname:"",admin1:"",admin2:"",server_type:"",production_attributes:"",data_center:[],out_of_band_management_ilo:"",operation_status:"",is_innovative_tech:"",is_monitored:"",online_status:"",os_category:"",operating_system:"",year_category:"",monitoring_requirement:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async getDatadict(n,a){try{const _=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:n});this[a]=_.data.msg}catch(_){console.error("数据加载失败:",_),this.$message.error("数据加载失败")}},async loadUsersList(){try{const n=await this.$axios.post("/api/get_all_users_real_name");this.usersList=n.data.msg,console.log("用户列表加载成功:",this.usersList)}catch(n){console.error("用户列表加载失败:",n),this.$message.error("用户列表加载失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={is_single_point:"否",is_innovative_tech:"否",is_monitored:"否",managed_addresses:"",monitoring_requirement:"是",monitoring_requirement_description:""},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.validateManagedAddresses()})},handleEdit(n,a){this.dialogVisible.edit=!0,this.formData.id=a.id,this.formData.management_ip=a.management_ip,this.formData.hostname=a.hostname,this.formData.function_purpose=a.function_purpose,this.formData.admin1=a.admin1,this.formData.admin2=a.admin2,this.formData.server_type=a.server_type_code||a.server_type,this.formData.production_attributes=a.production_attributes_code||a.production_attributes,this.formData.data_center=a.data_center_code||a.data_center,this.formData.out_of_band_management_ilo=a.out_of_band_management_ilo,this.formData.operation_status=a.operation_status_code||a.operation_status,this.formData.is_innovative_tech=a.is_innovative_tech,this.formData.asset_number=a.asset_number,this.formData.purchase_date=a.purchase_date,this.formData.maintenance_years=a.maintenance_years,this.formData.maintenance_end_date=a.maintenance_end_date,this.formData.serial_number=a.serial_number,this.formData.server_model=a.server_model,this.formData.is_monitored=a.is_monitored,this.formData.cpu_model=a.cpu_model,this.formData.memory=a.memory,this.formData.disk=a.disk,this.formData.network_card=a.network_card,this.formData.operating_system=a.operating_system_code||a.operating_system,this.formData.os_category=a.os_category_code||a.os_category,this.formData.remarks=a.remarks,this.formData.year_category=a.year_category,this.formData.is_single_point=a.is_single_point,this.formData.managed_addresses=a.managed_addresses,this.formData.monitoring_requirement=a.monitoring_requirement,this.formData.monitoring_requirement_description=a.monitoring_requirement_description,console.log("编辑表单数据:",{is_innovative_tech:this.formData.is_innovative_tech,operating_system:this.formData.operating_system}),this.$refs.editFormRef&&this.$nextTick(()=>{this.$refs.editFormRef.clearValidate()})},handleDelete(n,a){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=a.id,this.formData.management_ip=a.management_ip},exportData(){const a=this.$refs.table.columns,_=a.map(u=>u.label),x=this.userArr.map(u=>a.map(U=>u[U.property])),t=[_,...x],p=I.aoa_to_sheet(t),d=I.book_new();I.book_append_sheet(d,p,"Sheet1");const o=J(d,{bookType:"xlsx",type:"array"}),s=new Blob([o],{type:"application/octet-stream"});Q.saveAs(s,"实体服务器.xlsx")},handleAddFromDiscovery(){this.dialogVisible.add=!0;const{ip_address:n,hostname:a,open_ports:_}=this.$route.query;this.formData={management_ip:n||"",hostname:a||"",function_purpose:"",admin1:"",admin2:"",server_type:"",production_attributes:"",data_center:"",operation_status:"",is_innovative_tech:"否",remarks:_?`开放端口: ${_}`:"",is_single_point:"否",managed_addresses:"",is_monitored:"否"},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.$message.info("请完善资产信息并提交")}),this.$router.replace({path:this.$route.path})},validateManagedAddresses(){this.$refs.addFormRef&&this.$refs.addFormRef.validateField("managed_addresses"),this.$refs.editFormRef&&this.$refs.editFormRef.validateField("managed_addresses"),this.formData.is_single_point==="是"&&(this.formData.managed_addresses="")},handleMonitoringRequirementChange(){this.formData.monitoring_requirement==="是"&&(this.formData.monitoring_requirement_description=""),this.$refs.addFormRef&&this.$refs.addFormRef.validateField("monitoring_requirement_description"),this.$refs.editFormRef&&this.$refs.editFormRef.validateField("monitoring_requirement_description")},async checkManagementIpDuplicate(n,a=null){if(!n)return{exists:!1};try{return(await this.$axios.post("/api/check_server_management_ip",{management_ip:n,id:a,operation_status:this.formData.operation_status})).data}catch(_){throw console.error("检查管理IP失败:",_),new Error("检查管理IP失败，请稍后重试")}}}},X={class:"user-manage"},Z={class:"dialogdiv"},$={class:"dialog-footer"},ee={class:"dialogdiv"},le={class:"dialog-footer"},ae={class:"button-container"},te={class:"action-bar unified-action-bar"},re={class:"action-bar-left"},oe={class:"action-bar-right"},ne={style:{display:"flex","white-space":"nowrap"}},se={class:"pagination"};function ie(n,a,_,x,t,p){const d=h("el-input"),o=h("el-form-item"),s=h("el-option"),u=h("el-select"),U=h("el-date-picker"),q=h("el-form"),D=h("el-button"),P=h("el-dialog"),F=h("el-alert"),y=h("el-col"),A=h("Search"),C=h("el-icon"),R=h("el-row"),S=h("el-card"),Y=h("Plus"),M=h("Download"),m=h("el-table-column"),w=h("el-tag"),E=h("el-table"),z=h("el-pagination"),L=j("loading");return i(),f("div",X,[l(P,{modelValue:t.dialogVisible.add,"onUpdate:modelValue":a[27]||(a[27]=e=>t.dialogVisible.add=e),title:"新增服务器信息",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:r(()=>[v("div",$,[l(D,{onClick:a[26]||(a[26]=e=>t.dialogVisible.add=!1)},{default:r(()=>[V("返回")]),_:1}),l(D,{type:"primary",onClick:p.validateAndSubmitAdd},{default:r(()=>[V("确定")]),_:1},8,["onClick"])])]),default:r(()=>[v("div",Z,[l(q,{model:t.formData,rules:t.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:r(()=>[l(o,{prop:"management_ip",label:"管理IP:"},{default:r(()=>[l(d,{modelValue:t.formData.management_ip,"onUpdate:modelValue":a[0]||(a[0]=e=>t.formData.management_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),l(o,{prop:"hostname",label:"主机名:"},{default:r(()=>[l(d,{modelValue:t.formData.hostname,"onUpdate:modelValue":a[1]||(a[1]=e=>t.formData.hostname=e),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),l(o,{prop:"function_purpose",label:"功能用途:"},{default:r(()=>[l(d,{modelValue:t.formData.function_purpose,"onUpdate:modelValue":a[2]||(a[2]=e=>t.formData.function_purpose=e),style:{width:"240px"},clearable:"",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),l(o,{prop:"admin1",label:"管理员1:"},{default:r(()=>[l(u,{modelValue:t.formData.admin1,"onUpdate:modelValue":a[3]||(a[3]=e=>t.formData.admin1=e),style:{width:"240px"},placeholder:"请选择管理员1",clearable:"",filterable:""},{default:r(()=>[(i(!0),f(b,null,g(t.usersList,e=>(i(),c(s,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"admin2",label:"管理员2:"},{default:r(()=>[l(u,{modelValue:t.formData.admin2,"onUpdate:modelValue":a[4]||(a[4]=e=>t.formData.admin2=e),style:{width:"240px"},placeholder:"请选择管理员2",clearable:"",filterable:""},{default:r(()=>[(i(!0),f(b,null,g(t.usersList,e=>(i(),c(s,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"server_type",label:"服务器类型:"},{default:r(()=>[l(u,{modelValue:t.formData.server_type,"onUpdate:modelValue":a[5]||(a[5]=e=>t.formData.server_type=e),style:{width:"240px"},clearable:"",placeholder:"请选择服务器类型"},{default:r(()=>[(i(!0),f(b,null,g(t.servertypes,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"production_attributes",label:"生产属性:"},{default:r(()=>[l(u,{modelValue:t.formData.production_attributes,"onUpdate:modelValue":a[6]||(a[6]=e=>t.formData.production_attributes=e),style:{width:"240px"},clearable:"",placeholder:"请选择生产属性"},{default:r(()=>[(i(!0),f(b,null,g(t.productionattributes,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"data_center",label:"所属机房:"},{default:r(()=>[l(u,{modelValue:t.formData.data_center,"onUpdate:modelValue":a[7]||(a[7]=e=>t.formData.data_center=e),style:{width:"240px"},clearable:"",placeholder:"请选择所属机房"},{default:r(()=>[(i(!0),f(b,null,g(t.datacenters,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"out_of_band_management_ilo",label:"带外管理IP:"},{default:r(()=>[l(d,{modelValue:t.formData.out_of_band_management_ilo,"onUpdate:modelValue":a[8]||(a[8]=e=>t.formData.out_of_band_management_ilo=e),style:{width:"240px"},clearable:"",placeholder:"请输入带外管理IP"},null,8,["modelValue"])]),_:1}),l(o,{prop:"operation_status",label:"生命周期:"},{default:r(()=>[l(u,{modelValue:t.formData.operation_status,"onUpdate:modelValue":a[9]||(a[9]=e=>t.formData.operation_status=e),style:{width:"240px"},clearable:"",placeholder:"请选择生命周期"},{default:r(()=>[(i(!0),f(b,null,g(t.operationstatus,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"is_innovative_tech",label:"是否信创:"},{default:r(()=>[l(u,{modelValue:t.formData.is_innovative_tech,"onUpdate:modelValue":a[10]||(a[10]=e=>t.formData.is_innovative_tech=e),style:{width:"240px"},clearable:"",placeholder:"请选择是否信创"},{default:r(()=>[l(s,{label:"是",value:"是"}),l(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"monitoring_requirement",label:"需要监控:"},{default:r(()=>[l(u,{modelValue:t.formData.monitoring_requirement,"onUpdate:modelValue":a[11]||(a[11]=e=>t.formData.monitoring_requirement=e),style:{width:"240px"},clearable:"",placeholder:"请选择需要监控",onChange:p.handleMonitoringRequirementChange},{default:r(()=>[l(s,{label:"是",value:"是"}),l(s,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),l(o,{prop:"monitoring_requirement_description",label:"不监控原因:",required:t.formData.monitoring_requirement==="否"},{default:r(()=>[l(d,{modelValue:t.formData.monitoring_requirement_description,"onUpdate:modelValue":a[12]||(a[12]=e=>t.formData.monitoring_requirement_description=e),style:{width:"240px"},type:"textarea",rows:3,clearable:"",placeholder:"当需要监控为否时必填",disabled:t.formData.monitoring_requirement==="是"},null,8,["modelValue","disabled"])]),_:1},8,["required"]),l(o,{prop:"is_single_point",label:"是否单点:"},{default:r(()=>[l(u,{modelValue:t.formData.is_single_point,"onUpdate:modelValue":a[13]||(a[13]=e=>t.formData.is_single_point=e),style:{width:"240px"},clearable:"",placeholder:"请选择是否单点",onChange:p.validateManagedAddresses},{default:r(()=>[l(s,{label:"是",value:"是"}),l(s,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),l(o,{prop:"managed_addresses",label:"互备主机IP:",required:t.formData.is_single_point==="否"},{default:r(()=>[l(d,{modelValue:t.formData.managed_addresses,"onUpdate:modelValue":a[14]||(a[14]=e=>t.formData.managed_addresses=e),style:{width:"240px"},clearable:"",placeholder:"请输入互备主机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),l(o,{prop:"asset_number",label:"财务资产编号:"},{default:r(()=>[l(d,{modelValue:t.formData.asset_number,"onUpdate:modelValue":a[15]||(a[15]=e=>t.formData.asset_number=e),style:{width:"240px"},clearable:"",placeholder:"请输入财务资产编号"},null,8,["modelValue"])]),_:1}),l(o,{prop:"purchase_date",label:"采购时间:"},{default:r(()=>[l(U,{modelValue:t.formData.purchase_date,"onUpdate:modelValue":a[16]||(a[16]=e=>t.formData.purchase_date=e),type:"datetime","value-format":"YYYY-MM-DD","time-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY-MM-DD",style:{width:"240px"},clearable:"",placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),l(o,{prop:"maintenance_years",label:"维保年限:"},{default:r(()=>[l(d,{modelValue:t.formData.maintenance_years,"onUpdate:modelValue":a[17]||(a[17]=e=>t.formData.maintenance_years=e),style:{width:"240px"},clearable:"",placeholder:"请输入维保年限"},null,8,["modelValue"])]),_:1}),l(o,{prop:"serial_number",label:"序列号:"},{default:r(()=>[l(d,{modelValue:t.formData.serial_number,"onUpdate:modelValue":a[18]||(a[18]=e=>t.formData.serial_number=e),style:{width:"240px"},clearable:"",placeholder:"请输入序列号"},null,8,["modelValue"])]),_:1}),l(o,{prop:"server_model",label:"服务器型号:"},{default:r(()=>[l(d,{modelValue:t.formData.server_model,"onUpdate:modelValue":a[19]||(a[19]=e=>t.formData.server_model=e),style:{width:"240px"},clearable:"",placeholder:"请输入服务器型号"},null,8,["modelValue"])]),_:1}),l(o,{prop:"cpu_model",label:"CPU型号:"},{default:r(()=>[l(d,{modelValue:t.formData.cpu_model,"onUpdate:modelValue":a[20]||(a[20]=e=>t.formData.cpu_model=e),style:{width:"240px"},clearable:"",placeholder:"请输入CPU型号"},null,8,["modelValue"])]),_:1}),l(o,{prop:"memory",label:"内存:"},{default:r(()=>[l(d,{modelValue:t.formData.memory,"onUpdate:modelValue":a[21]||(a[21]=e=>t.formData.memory=e),style:{width:"240px"},clearable:"",placeholder:"请输入内存"},null,8,["modelValue"])]),_:1}),l(o,{prop:"disk",label:"硬盘:"},{default:r(()=>[l(d,{modelValue:t.formData.disk,"onUpdate:modelValue":a[22]||(a[22]=e=>t.formData.disk=e),style:{width:"240px"},clearable:"",placeholder:"请输入硬盘"},null,8,["modelValue"])]),_:1}),l(o,{prop:"network_card",label:"网卡:"},{default:r(()=>[l(d,{modelValue:t.formData.network_card,"onUpdate:modelValue":a[23]||(a[23]=e=>t.formData.network_card=e),style:{width:"240px"},clearable:"",placeholder:"请输入网卡"},null,8,["modelValue"])]),_:1}),l(o,{prop:"operating_system",label:"操作系统:"},{default:r(()=>[l(u,{modelValue:t.formData.operating_system,"onUpdate:modelValue":a[24]||(a[24]=e=>t.formData.operating_system=e),style:{width:"240px"},clearable:"",placeholder:"请选择操作系统"},{default:r(()=>[(i(!0),f(b,null,g(t.operatingsystems,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"remarks",label:"备注:"},{default:r(()=>[l(d,{modelValue:t.formData.remarks,"onUpdate:modelValue":a[25]||(a[25]=e=>t.formData.remarks=e),style:{width:"240px"},clearable:"",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),l(P,{modelValue:t.dialogVisible.edit,"onUpdate:modelValue":a[55]||(a[55]=e=>t.dialogVisible.edit=e),title:"编辑服务器信息",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:r(()=>[v("div",le,[l(D,{onClick:a[54]||(a[54]=e=>t.dialogVisible.edit=!1)},{default:r(()=>[V("取消")]),_:1}),l(D,{type:"primary",onClick:p.validateAndSubmitEdit},{default:r(()=>[V("更新")]),_:1},8,["onClick"])])]),default:r(()=>[v("div",ee,[l(q,{model:t.formData,rules:t.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:r(()=>[l(o,{prop:"management_ip",label:"管理IP:"},{default:r(()=>[l(d,{modelValue:t.formData.management_ip,"onUpdate:modelValue":a[28]||(a[28]=e=>t.formData.management_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),l(o,{prop:"hostname",label:"主机名:"},{default:r(()=>[l(d,{modelValue:t.formData.hostname,"onUpdate:modelValue":a[29]||(a[29]=e=>t.formData.hostname=e),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),l(o,{prop:"function_purpose",label:"功能用途:"},{default:r(()=>[l(d,{modelValue:t.formData.function_purpose,"onUpdate:modelValue":a[30]||(a[30]=e=>t.formData.function_purpose=e),style:{width:"240px"},clearable:"",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),l(o,{prop:"admin1",label:"管理员1:"},{default:r(()=>[l(u,{modelValue:t.formData.admin1,"onUpdate:modelValue":a[31]||(a[31]=e=>t.formData.admin1=e),style:{width:"240px"},placeholder:"请选择管理员1",clearable:"",filterable:""},{default:r(()=>[(i(!0),f(b,null,g(t.usersList,e=>(i(),c(s,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"admin2",label:"管理员2:"},{default:r(()=>[l(u,{modelValue:t.formData.admin2,"onUpdate:modelValue":a[32]||(a[32]=e=>t.formData.admin2=e),style:{width:"240px"},placeholder:"请选择管理员2",clearable:"",filterable:""},{default:r(()=>[(i(!0),f(b,null,g(t.usersList,e=>(i(),c(s,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"server_type",label:"服务器类型:"},{default:r(()=>[l(u,{modelValue:t.formData.server_type,"onUpdate:modelValue":a[33]||(a[33]=e=>t.formData.server_type=e),style:{width:"240px"},clearable:"",placeholder:"请选择服务器类型"},{default:r(()=>[(i(!0),f(b,null,g(t.servertypes,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"production_attributes",label:"生产属性:"},{default:r(()=>[l(u,{modelValue:t.formData.production_attributes,"onUpdate:modelValue":a[34]||(a[34]=e=>t.formData.production_attributes=e),style:{width:"240px"},clearable:"",placeholder:"请选择生产属性"},{default:r(()=>[(i(!0),f(b,null,g(t.productionattributes,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"data_center",label:"所属机房:"},{default:r(()=>[l(u,{modelValue:t.formData.data_center,"onUpdate:modelValue":a[35]||(a[35]=e=>t.formData.data_center=e),style:{width:"240px"},clearable:"",placeholder:"请选择所属机房"},{default:r(()=>[(i(!0),f(b,null,g(t.datacenters,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"out_of_band_management_ilo",label:"带外管理IP:"},{default:r(()=>[l(d,{modelValue:t.formData.out_of_band_management_ilo,"onUpdate:modelValue":a[36]||(a[36]=e=>t.formData.out_of_band_management_ilo=e),style:{width:"240px"},clearable:"",placeholder:"请输入带外管理IP"},null,8,["modelValue"])]),_:1}),l(o,{prop:"operation_status",label:"生命周期:"},{default:r(()=>[l(u,{modelValue:t.formData.operation_status,"onUpdate:modelValue":a[37]||(a[37]=e=>t.formData.operation_status=e),style:{width:"240px"},clearable:"",placeholder:"请选择生命周期"},{default:r(()=>[(i(!0),f(b,null,g(t.operationstatus,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"is_innovative_tech",label:"是否信创:"},{default:r(()=>[l(u,{modelValue:t.formData.is_innovative_tech,"onUpdate:modelValue":a[38]||(a[38]=e=>t.formData.is_innovative_tech=e),style:{width:"240px"},clearable:"",placeholder:"请选择是否信创"},{default:r(()=>[l(s,{label:"是",value:"是"}),l(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"monitoring_requirement",label:"需要监控:"},{default:r(()=>[l(u,{modelValue:t.formData.monitoring_requirement,"onUpdate:modelValue":a[39]||(a[39]=e=>t.formData.monitoring_requirement=e),style:{width:"240px"},clearable:"",placeholder:"请选择需要监控",onChange:p.handleMonitoringRequirementChange},{default:r(()=>[l(s,{label:"是",value:"是"}),l(s,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),l(o,{prop:"monitoring_requirement_description",label:"不监控原因:",required:t.formData.monitoring_requirement==="否"},{default:r(()=>[l(d,{modelValue:t.formData.monitoring_requirement_description,"onUpdate:modelValue":a[40]||(a[40]=e=>t.formData.monitoring_requirement_description=e),style:{width:"240px"},type:"textarea",rows:3,clearable:"",placeholder:"当需要监控为否时必填",disabled:t.formData.monitoring_requirement==="是"},null,8,["modelValue","disabled"])]),_:1},8,["required"]),l(o,{prop:"is_single_point",label:"是否单点:"},{default:r(()=>[l(u,{modelValue:t.formData.is_single_point,"onUpdate:modelValue":a[41]||(a[41]=e=>t.formData.is_single_point=e),style:{width:"240px"},clearable:"",placeholder:"请选择是否单点",onChange:p.validateManagedAddresses},{default:r(()=>[l(s,{label:"是",value:"是"}),l(s,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),l(o,{prop:"managed_addresses",label:"互备主机IP:",required:t.formData.is_single_point==="否"},{default:r(()=>[l(d,{modelValue:t.formData.managed_addresses,"onUpdate:modelValue":a[42]||(a[42]=e=>t.formData.managed_addresses=e),style:{width:"240px"},clearable:"",placeholder:"请输入互备主机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),l(o,{prop:"asset_number",label:"财务资产编号:"},{default:r(()=>[l(d,{modelValue:t.formData.asset_number,"onUpdate:modelValue":a[43]||(a[43]=e=>t.formData.asset_number=e),style:{width:"240px"},clearable:"",placeholder:"请输入财务资产编号"},null,8,["modelValue"])]),_:1}),l(o,{prop:"purchase_date",label:"采购时间:"},{default:r(()=>[l(U,{modelValue:t.formData.purchase_date,"onUpdate:modelValue":a[44]||(a[44]=e=>t.formData.purchase_date=e),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"240px"},clearable:"",placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),l(o,{prop:"maintenance_years",label:"维保年限:"},{default:r(()=>[l(d,{modelValue:t.formData.maintenance_years,"onUpdate:modelValue":a[45]||(a[45]=e=>t.formData.maintenance_years=e),style:{width:"240px"},clearable:"",placeholder:"请输入维保年限"},null,8,["modelValue"])]),_:1}),l(o,{prop:"serial_number",label:"序列号:"},{default:r(()=>[l(d,{modelValue:t.formData.serial_number,"onUpdate:modelValue":a[46]||(a[46]=e=>t.formData.serial_number=e),style:{width:"240px"},clearable:"",placeholder:"请输入序列号"},null,8,["modelValue"])]),_:1}),l(o,{prop:"server_model",label:"服务器型号:"},{default:r(()=>[l(d,{modelValue:t.formData.server_model,"onUpdate:modelValue":a[47]||(a[47]=e=>t.formData.server_model=e),style:{width:"240px"},clearable:"",placeholder:"请输入服务器型号"},null,8,["modelValue"])]),_:1}),l(o,{prop:"cpu_model",label:"CPU型号:"},{default:r(()=>[l(d,{modelValue:t.formData.cpu_model,"onUpdate:modelValue":a[48]||(a[48]=e=>t.formData.cpu_model=e),style:{width:"240px"},clearable:"",placeholder:"请输入CPU型号"},null,8,["modelValue"])]),_:1}),l(o,{prop:"memory",label:"内存:"},{default:r(()=>[l(d,{modelValue:t.formData.memory,"onUpdate:modelValue":a[49]||(a[49]=e=>t.formData.memory=e),style:{width:"240px"},clearable:"",placeholder:"请输入内存"},null,8,["modelValue"])]),_:1}),l(o,{prop:"disk",label:"硬盘:"},{default:r(()=>[l(d,{modelValue:t.formData.disk,"onUpdate:modelValue":a[50]||(a[50]=e=>t.formData.disk=e),style:{width:"240px"},clearable:"",placeholder:"请输入硬盘"},null,8,["modelValue"])]),_:1}),l(o,{prop:"network_card",label:"网卡:"},{default:r(()=>[l(d,{modelValue:t.formData.network_card,"onUpdate:modelValue":a[51]||(a[51]=e=>t.formData.network_card=e),style:{width:"240px"},clearable:"",placeholder:"请输入网卡"},null,8,["modelValue"])]),_:1}),l(o,{prop:"operating_system",label:"操作系统:"},{default:r(()=>[l(u,{modelValue:t.formData.operating_system,"onUpdate:modelValue":a[52]||(a[52]=e=>t.formData.operating_system=e),style:{width:"240px"},clearable:"",placeholder:"请选择操作系统"},{default:r(()=>[(i(!0),f(b,null,g(t.operatingsystems,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(o,{prop:"remarks",label:"备注:"},{default:r(()=>[l(d,{modelValue:t.formData.remarks,"onUpdate:modelValue":a[53]||(a[53]=e=>t.formData.remarks=e),style:{width:"240px"},clearable:"",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),l(P,{modelValue:t.dialogVisible.delete,"onUpdate:modelValue":a[57]||(a[57]=e=>t.dialogVisible.delete=e),title:"删除管理IP",width:"500","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:r(()=>[v("div",null,[l(D,{onClick:a[56]||(a[56]=e=>t.dialogVisible.delete=!1)},{default:r(()=>[V("取消")]),_:1}),l(D,{type:"danger",onClick:p.submitDelete},{default:r(()=>[V("确认删除")]),_:1},8,["onClick"])])]),default:r(()=>[l(F,{type:"warning",title:`确定要删除 IP 为 ${t.formData.management_ip} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),l(S,{class:"search-card"},{default:r(()=>[l(q,{inline:!0},{default:r(()=>[l(R,{gutter:10},{default:r(()=>[l(y,{span:6},{default:r(()=>[l(o,{label:"管理IP"},{default:r(()=>[l(d,{modelValue:t.search.management_ip,"onUpdate:modelValue":a[58]||(a[58]=e=>t.search.management_ip=e),placeholder:"请输入管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"主机名"},{default:r(()=>[l(d,{modelValue:t.search.hostname,"onUpdate:modelValue":a[59]||(a[59]=e=>t.search.hostname=e),placeholder:"请输入主机名",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"管理员1"},{default:r(()=>[l(u,{modelValue:t.search.admin1,"onUpdate:modelValue":a[60]||(a[60]=e=>t.search.admin1=e),placeholder:"请选择管理员1",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(i(!0),f(b,null,g(t.usersList,e=>(i(),c(s,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"管理员2"},{default:r(()=>[l(u,{modelValue:t.search.admin2,"onUpdate:modelValue":a[61]||(a[61]=e=>t.search.admin2=e),placeholder:"请选择管理员2",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(i(!0),f(b,null,g(t.usersList,e=>(i(),c(s,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"生命周期"},{default:r(()=>[l(u,{modelValue:t.search.operation_status,"onUpdate:modelValue":a[62]||(a[62]=e=>t.search.operation_status=e),placeholder:"请选择生命周期",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(i(!0),f(b,null,g(t.operationstatus,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"服务器类型"},{default:r(()=>[l(u,{modelValue:t.search.server_type,"onUpdate:modelValue":a[63]||(a[63]=e=>t.search.server_type=e),placeholder:"请选择服务器类型",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(i(!0),f(b,null,g(t.servertypes,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"生产属性"},{default:r(()=>[l(u,{modelValue:t.search.production_attributes,"onUpdate:modelValue":a[64]||(a[64]=e=>t.search.production_attributes=e),placeholder:"请选择生产属性",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(i(!0),f(b,null,g(t.productionattributes,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"所属机房"},{default:r(()=>[l(u,{modelValue:t.search.data_center,"onUpdate:modelValue":a[65]||(a[65]=e=>t.search.data_center=e),placeholder:"请选择所属机房",clearable:"",filterable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",class:"form-control"},{default:r(()=>[(i(!0),f(b,null,g(t.datacenters,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"带外管理IP"},{default:r(()=>[l(d,{modelValue:t.search.out_of_band_management_ilo,"onUpdate:modelValue":a[66]||(a[66]=e=>t.search.out_of_band_management_ilo=e),placeholder:"请输入带外管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"是否信创"},{default:r(()=>[l(u,{modelValue:t.search.is_innovative_tech,"onUpdate:modelValue":a[67]||(a[67]=e=>t.search.is_innovative_tech=e),placeholder:"请选择是否信创",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[l(s,{label:"是",value:"是"}),l(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"需要监控"},{default:r(()=>[l(u,{modelValue:t.search.monitoring_requirement,"onUpdate:modelValue":a[68]||(a[68]=e=>t.search.monitoring_requirement=e),placeholder:"请选择需要监控",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[l(s,{label:"是",value:"是"}),l(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"监控状态"},{default:r(()=>[l(u,{modelValue:t.search.is_monitored,"onUpdate:modelValue":a[69]||(a[69]=e=>t.search.is_monitored=e),placeholder:"请选择监控状态",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[l(s,{label:"是",value:"是"}),l(s,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"PING状态"},{default:r(()=>[l(u,{modelValue:t.search.online_status,"onUpdate:modelValue":a[70]||(a[70]=e=>t.search.online_status=e),placeholder:"请选择PING状态",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[l(s,{label:"在线",value:"在线"}),l(s,{label:"离线",value:"离线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:6},{default:r(()=>[l(o,{label:"操作系统"},{default:r(()=>[l(u,{modelValue:t.search.operating_system,"onUpdate:modelValue":a[71]||(a[71]=e=>t.search.operating_system=e),placeholder:"请选择操作系统",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(i(!0),f(b,null,g(t.operatingsystems,e=>(i(),c(s,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(y,{span:24,class:"search-buttons-col"},{default:r(()=>[l(o,{label:" ",class:"form-item-with-label search-buttons"},{default:r(()=>[v("div",ae,[l(D,{type:"primary",onClick:p.loadData},{default:r(()=>[l(C,null,{default:r(()=>[l(A)]),_:1}),V("查询 ")]),_:1},8,["onClick"]),l(D,{onClick:p.resetSearch},{default:r(()=>[V("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),v("div",te,[v("div",re,[l(D,{type:"success",disabled:!t.hasInsertPermission,onClick:p.handleAdd},{default:r(()=>[l(C,null,{default:r(()=>[l(Y)]),_:1}),V("新增资产 ")]),_:1},8,["disabled","onClick"])]),v("div",oe,[l(D,{type:"info",onClick:p.exportData},{default:r(()=>[l(C,null,{default:r(()=>[l(M)]),_:1}),V(" 导出数据 ")]),_:1},8,["onClick"])])]),l(S,{class:"table-card"},{default:r(()=>[H((i(),c(E,{data:t.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:p.handleSortChange},{default:r(()=>[K("",!0),l(m,{prop:"management_ip",label:"管理IP",sortable:""}),l(m,{prop:"hostname",label:"主机名",sortable:""}),l(m,{prop:"function_purpose",label:"功能用途",sortable:""}),l(m,{prop:"admin1",label:"管理员1",sortable:""}),l(m,{prop:"admin2",label:"管理员2",sortable:""}),l(m,{prop:"operation_status",label:"生命周期",sortable:""},{default:r(e=>[l(w,{type:p.getLifecycleTagType(e.row.operation_status)},{default:r(()=>[V(k(e.row.operation_status),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"server_type",label:"服务器类型",sortable:""}),l(m,{prop:"production_attributes",label:"生产属性",sortable:""}),l(m,{prop:"data_center",label:"所属机房",sortable:""}),l(m,{prop:"out_of_band_management_ilo",label:"带外管理IP",sortable:""}),l(m,{prop:"is_innovative_tech",label:"是否信创",sortable:""},{default:r(e=>[l(w,{type:e.row.is_innovative_tech==="是"?"success":"danger"},{default:r(()=>[V(k(e.row.is_innovative_tech),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"asset_number",label:"财务资产编号",sortable:""}),l(m,{prop:"purchase_date",label:"采购时间",sortable:""}),l(m,{prop:"maintenance_years",label:"维保年限",sortable:""}),l(m,{prop:"maintenance_end_date",label:"维保截止日期",sortable:""}),l(m,{prop:"serial_number",label:"序列号",sortable:""}),l(m,{prop:"server_model",label:"服务器型号",sortable:""}),l(m,{prop:"monitoring_requirement",label:"需要监控",sortable:""},{default:r(e=>[l(w,{type:e.row.monitoring_requirement==="是"?"success":"danger"},{default:r(()=>[V(k(e.row.monitoring_requirement),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"monitoring_requirement_description",label:"不监控原因",sortable:""}),l(m,{prop:"is_monitored",label:"监控状态",sortable:""},{default:r(e=>[l(w,{type:e.row.is_monitored==="是"?"success":"danger"},{default:r(()=>[V(k(e.row.is_monitored),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"online_status",label:"PING状态",sortable:""},{default:r(e=>[l(w,{type:e.row.online_status==="在线"?"success":"danger"},{default:r(()=>[V(k(e.row.online_status),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"is_single_point",label:"是否单点",sortable:""},{default:r(e=>[l(w,{type:e.row.is_single_point==="是"?"success":"danger"},{default:r(()=>[V(k(e.row.is_single_point),1)]),_:2},1032,["type"])]),_:1}),l(m,{prop:"managed_addresses",label:"互备主机IP",sortable:""}),l(m,{prop:"cpu_model",label:"CPU型号",sortable:""}),l(m,{prop:"memory",label:"内存",sortable:""}),l(m,{prop:"disk",label:"硬盘",sortable:""}),l(m,{prop:"network_card",label:"网卡",sortable:""}),l(m,{prop:"operating_system",label:"操作系统",sortable:""}),l(m,{prop:"remarks",label:"备注",sortable:""}),l(m,{prop:"created_at",label:"创建时间",sortable:""}),l(m,{prop:"created_by",label:"创建人",sortable:""}),l(m,{prop:"updated_at",label:"更新时间",sortable:""}),l(m,{prop:"updated_by",label:"更新人",sortable:""}),l(m,{label:"操作",fixed:"right"},{default:r(e=>[v("div",ne,[l(D,{size:"small",type:"warning",disabled:!t.hasUpdatePermission,onClick:T=>p.handleEdit(e.$index,e.row)},{default:r(()=>[V("编辑")]),_:2},1032,["disabled","onClick"]),l(D,{size:"small",type:"danger",disabled:!t.hasDeletePermission,onClick:T=>p.handleDelete(e.$index,e.row)},{default:r(()=>[V("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[L,t.loading]]),v("div",se,[l(z,{background:"","current-page":t.search.currentPage,"page-size":t.search.pageSize,total:t.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:p.handlePageSizeChange,onCurrentChange:p.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const pe=B(W,[["render",ie],["__scopeId","data-v-974d39c8"]]);export{pe as default};
