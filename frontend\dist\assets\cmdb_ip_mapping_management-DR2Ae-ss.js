import{_ as Z,c as v,a as e,w as l,b as w,F as k,l as x,f as U,x as B,y as N,h as b,t as L,C as q,D as K,m as S,V as z,E as i,r as P,R as W,p as J,q as G,e as f}from"./index-MGgq8mV5.js";const H={name:"IpMappingManagement",setup(){const o=P(!1),r=P(!1),c=P(!1),a=P(!1),y=P([]),n=P(0),u=P(1),s=P(10),d=W({add:!1,edit:!1,delete:!1,batchDelete:!1}),V=P([]),p=P(null),C=P(null),_=P(""),A=W({internetIp:"",protocol:"",datacenter:"",status:"",mappedPort:"",assignedUser:"",keyword:""}),F=W({id:null,internetIp:"",dmzIp:"",managementIp:"",sourcePort:"",mappedPort:"",protocol:"",description:"",datacenter:"",whitelist:[],lineId:null,assignedUser:"",status:"active"}),M=P([]),O=P([]);return{loading:o,submitLoading:r,exportLoading:c,deleteLoading:a,tableData:y,total:n,currentPage:u,pageSize:s,dialogVisible:d,selectedRows:V,addFormRef:p,editFormRef:C,whitelistInput:_,search:A,formData:F,datacenters:M,internetLines:O,rules:{internetIp:[{required:!0,message:"请输入互联网IP",trigger:"blur"},{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"IP地址格式不正确",trigger:"blur"},{validator:(I,D,g)=>{D?D.split(".").every(h=>{const m=parseInt(h);return m>=0&&m<=255})?g():g(new Error("IP地址范围应在0-255之间")):g()},trigger:"blur"}],dmzIp:[{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"DMZ-IP地址格式不正确",trigger:"blur"},{validator:(I,D,g)=>{D?D.split(".").every(h=>{const m=parseInt(h);return m>=0&&m<=255})?g():g(new Error("DMZ-IP地址范围应在0-255之间")):g()},trigger:"blur"}],managementIp:[{pattern:/^(\d{1,3}\.){3}\d{1,3}$/,message:"管理IP地址格式不正确",trigger:"blur"},{validator:(I,D,g)=>{D?D.split(".").every(h=>{const m=parseInt(h);return m>=0&&m<=255})?g():g(new Error("管理IP地址范围应在0-255之间")):g()},trigger:"blur"}],sourcePort:[{pattern:/^(\d+(-\d+)?)(,\d+(-\d+)?)*$/,message:"源端口格式不正确，如：80 或 80-90 或 80,81,82",trigger:"blur"},{validator:(I,D,g)=>{D?D.split(",").every(h=>{if(h.includes("-")){const[m,R]=h.split("-").map(j=>parseInt(j));return m>=1&&m<=65535&&R>=1&&R<=65535&&m<=R}else{const m=parseInt(h);return m>=1&&m<=65535}})?g():g(new Error("端口号必须在1-65535之间")):g()},trigger:"blur"}],mappedPort:[{pattern:/^(\d+(-\d+)?)(,\d+(-\d+)?)*$/,message:"映射端口格式不正确，如：8080 或 8080-8090",trigger:"blur"},{validator:(I,D,g)=>{D?D.split(",").every(h=>{if(h.includes("-")){const[m,R]=h.split("-").map(j=>parseInt(j));return m>=1&&m<=65535&&R>=1&&R<=65535&&m<=R}else{const m=parseInt(h);return m>=1&&m<=65535}})?g():g(new Error("端口号必须在1-65535之间")):g()},trigger:"blur"}],protocol:[{required:!0,message:"请选择协议",trigger:"change"}],datacenter:[{required:!0,message:"请选择机房",trigger:"change"}],lineId:[{required:!0,message:"请选择关联线路",trigger:"change"}]}}},methods:{async getData(){this.loading=!0;try{const o=await z.post("/api/get_ip_mappings",{pageSize:this.pageSize,currentPage:this.currentPage,internetIp:this.search.internetIp,protocol:this.search.protocol,datacenter:this.search.datacenter,status:this.search.status,mappedPort:this.search.mappedPort,assignedUser:this.search.assignedUser,keyword:this.search.keyword});o.data.code===0?(this.tableData=o.data.msg.map(r=>{if(r.whitelist&&typeof r.whitelist=="string")try{r.whitelist=JSON.parse(r.whitelist)}catch{r.whitelist=[]}return r}),this.total=o.data.total||0):i.error(o.data.msg||"获取数据失败")}catch(o){console.error("获取数据失败:",o),i.error("获取数据失败")}finally{this.loading=!1}},async getDictData(){try{const o=await z.post("/api/get_cmdb_data_dictionary",{dictType:"datacenter"});o.data.code===0&&(this.datacenters=o.data.msg)}catch(o){console.error("获取字典数据失败:",o)}},async getInternetLines(){try{const o=await z.post("/api/get_internet_lines",{pageSize:1e3,currentPage:1});o.data.code===0&&(this.internetLines=o.data.msg)}catch(o){console.error("获取线路列表失败:",o)}},resetSearch(){this.search.internetIp="",this.search.protocol="",this.search.datacenter="",this.search.status="",this.search.mappedPort="",this.search.assignedUser="",this.search.keyword="",this.currentPage=1,this.getData()},showAddDialog(){this.resetFormData(),this.dialogVisible.add=!0},editIpMapping(o){if(this.resetFormData(),Object.assign(this.formData,o),o.whitelist&&typeof o.whitelist=="string")try{this.formData.whitelist=JSON.parse(o.whitelist)}catch{this.formData.whitelist=[]}else Array.isArray(o.whitelist)?this.formData.whitelist=[...o.whitelist]:this.formData.whitelist=[];this.dialogVisible.edit=!0},deleteIpMapping(o){Object.assign(this.formData,o),this.dialogVisible.delete=!0},resetFormData(){this.formData.id=null,this.formData.internetIp="",this.formData.dmzIp="",this.formData.managementIp="",this.formData.sourcePort="",this.formData.mappedPort="",this.formData.protocol="",this.formData.description="",this.formData.datacenter="",this.formData.whitelist=[],this.formData.lineId=null,this.formData.assignedUser="",this.formData.status="active",this.whitelistInput=""},addWhitelistIp(){const o=this.whitelistInput.trim();if(!o){i.warning("请输入IP地址");return}if(!/^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/.test(o)){i.error("IP地址格式不正确，支持格式：*********** 或 ***********/24");return}if(!o.split("/")[0].split(".").every(y=>{const n=parseInt(y);return n>=0&&n<=255})){i.error("IP地址范围应在0-255之间");return}if(o.includes("/")){const y=parseInt(o.split("/")[1]);if(y<0||y>32){i.error("CIDR掩码应在0-32之间");return}}if(this.formData.whitelist||(this.formData.whitelist=[]),this.formData.whitelist.includes(o)){i.warning("该IP地址已存在于白名单中");return}this.formData.whitelist.push(o),this.whitelistInput="",i.success("白名单IP添加成功")},removeWhitelistIp(o){this.formData.whitelist.splice(o,1)},async validateAndSubmitAdd(){if(!this.submitLoading)try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch(o){if(o.fields){const r=Object.values(o.fields)[0][0];i.error(r.message)}else console.error("表单验证失败:",o),i.error("表单验证失败，请检查输入内容")}},async submitAdd(){var o,r;this.submitLoading=!0;try{const c=await z.post("/api/add_ip_mapping",{...this.formData,loginUsername:"admin"});c.data.code===0?(i.success("新增IP映射成功"),this.dialogVisible.add=!1,this.resetFormData(),this.getData()):i.error(c.data.msg||"新增失败，请稍后重试")}catch(c){console.error("新增失败:",c),((o=c.response)==null?void 0:o.status)===400?i.error(c.response.data.msg||"请求参数错误，请检查输入内容"):((r=c.response)==null?void 0:r.status)===500?i.error("服务器内部错误，请联系管理员"):i.error("网络连接失败，请检查网络后重试")}finally{this.submitLoading=!1}},async validateAndSubmitEdit(){if(!this.submitLoading)try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch(o){if(o.fields){const r=Object.values(o.fields)[0][0];i.error(r.message)}else console.error("表单验证失败:",o),i.error("表单验证失败，请检查输入内容")}},async submitEdit(){var o,r,c;this.submitLoading=!0;try{const a=await z.post("/api/update_ip_mapping",{...this.formData,loginUsername:"admin"});a.data.code===0?(i.success("更新IP映射成功"),this.dialogVisible.edit=!1,this.getData()):i.error(a.data.msg||"更新失败，请稍后重试")}catch(a){console.error("更新失败:",a),((o=a.response)==null?void 0:o.status)===400?i.error(a.response.data.msg||"请求参数错误，请检查输入内容"):((r=a.response)==null?void 0:r.status)===404?i.error("IP映射记录不存在，可能已被删除"):((c=a.response)==null?void 0:c.status)===500?i.error("服务器内部错误，请联系管理员"):i.error("网络连接失败，请检查网络后重试")}finally{this.submitLoading=!1}},async submitDelete(){var o,r,c;if(!this.deleteLoading){this.deleteLoading=!0;try{const a=await z.post("/api/delete_ip_mapping",{id:this.formData.id,loginUsername:"admin"});a.data.code===0?(i.success("删除IP映射成功"),this.dialogVisible.delete=!1,this.getData()):i.error(a.data.msg||"删除失败，请稍后重试")}catch(a){console.error("删除失败:",a),((o=a.response)==null?void 0:o.status)===400?i.error(a.response.data.msg||"删除失败"):((r=a.response)==null?void 0:r.status)===404?i.error("IP映射记录不存在，可能已被删除"):((c=a.response)==null?void 0:c.status)===500?i.error("服务器内部错误，请联系管理员"):i.error("网络连接失败，请检查网络后重试")}finally{this.deleteLoading=!1}}},async exportData(){var o;if(!this.exportLoading){this.exportLoading=!0;try{const r=await z.post("/api/export_ip_mapping_data",{exportType:"filtered",filters:{internetIp:this.search.internetIp,protocol:this.search.protocol,datacenter:this.search.datacenter,status:this.search.status,mappedPort:this.search.mappedPort,assignedUser:this.search.assignedUser,keyword:this.search.keyword},loginUsername:"admin"},{responseType:"blob"});if(r.data.type==="application/json"){const n=await r.data.text(),u=JSON.parse(n);i.error(u.msg||"导出失败");return}const c=new Blob([r.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),a=window.URL.createObjectURL(c),y=document.createElement("a");y.href=a,y.download=`IP映射数据_${new Date().toISOString().slice(0,10)}.xlsx`,document.body.appendChild(y),y.click(),document.body.removeChild(y),window.URL.revokeObjectURL(a),i.success("导出成功")}catch(r){console.error("导出失败:",r),((o=r.response)==null?void 0:o.status)===500?i.error("服务器内部错误，导出失败"):i.error("导出失败，请稍后重试")}finally{this.exportLoading=!1}}},handleSizeChange(o){this.pageSize=o,this.currentPage=1,this.getData()},handleCurrentChange(o){this.currentPage=o,this.getData()},handleSelectionChange(o){this.selectedRows=o},batchDelete(){if(this.selectedRows.length===0){i.warning("请选择要删除的IP映射");return}this.dialogVisible.batchDelete=!0},async submitBatchDelete(){try{const o=this.selectedRows.map(c=>c.id),r=await z.post("/api/batch_delete_ip_mappings",{ids:o});r.data.code===0?(i.success(`成功删除 ${o.length} 条IP映射记录`),this.dialogVisible.batchDelete=!1,this.selectedRows=[],this.getData()):i.error(r.data.msg||"批量删除失败")}catch(o){console.error("批量删除失败:",o),i.error("批量删除失败")}}},mounted(){this.getDictData(),this.getInternetLines(),this.getData()}},Q=o=>(J("data-v-8dc17e29"),o=o(),G(),o),X={class:"ip-mapping-management"},Y={class:"whitelist-editor"},$={key:0,class:"whitelist-tags"},ee={class:"dialog-footer"},ae={class:"whitelist-editor"},te={key:0,class:"whitelist-tags"},le={class:"dialog-footer"},re={class:"dialog-footer"},oe={class:"toolbar"},ie={style:{"margin-top":"15px","max-height":"200px","overflow-y":"auto"}},se=Q(()=>U("p",null,[U("strong",null,"将要删除的IP映射：")],-1)),de={class:"dialog-footer"},ne={key:0},pe={key:1},ue={class:"pagination"};function me(o,r,c,a,y,n){const u=w("el-input"),s=w("el-form-item"),d=w("el-col"),V=w("el-row"),p=w("el-option"),C=w("el-select"),_=w("el-button"),A=w("el-tag"),F=w("el-form"),M=w("el-dialog"),O=w("el-alert"),T=w("el-card"),I=w("el-table-column"),D=w("el-table"),g=w("el-pagination"),E=K("loading");return f(),v("div",X,[e(M,{modelValue:a.dialogVisible.add,"onUpdate:modelValue":r[13]||(r[13]=t=>a.dialogVisible.add=t),title:"新增IP映射",width:"600","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[U("div",ee,[e(_,{onClick:r[12]||(r[12]=t=>a.dialogVisible.add=!1),disabled:a.submitLoading},{default:l(()=>[b("取消")]),_:1},8,["disabled"]),e(_,{type:"primary",onClick:n.validateAndSubmitAdd,loading:a.submitLoading},{default:l(()=>[b("确定")]),_:1},8,["onClick","loading"])])]),default:l(()=>[e(F,{model:a.formData,rules:a.rules,ref:"addFormRef","label-position":"right","label-width":"120px"},{default:l(()=>[e(V,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(s,{prop:"internetIp",label:"互联网IP:"},{default:l(()=>[e(u,{modelValue:a.formData.internetIp,"onUpdate:modelValue":r[0]||(r[0]=t=>a.formData.internetIp=t),placeholder:"请输入互联网IP",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(s,{prop:"dmzIp",label:"DMZ-IP:"},{default:l(()=>[e(u,{modelValue:a.formData.dmzIp,"onUpdate:modelValue":r[1]||(r[1]=t=>a.formData.dmzIp=t),placeholder:"请输入DMZ-IP",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(s,{prop:"managementIp",label:"管理IP:"},{default:l(()=>[e(u,{modelValue:a.formData.managementIp,"onUpdate:modelValue":r[2]||(r[2]=t=>a.formData.managementIp=t),placeholder:"请输入管理IP",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(s,{prop:"protocol",label:"协议:"},{default:l(()=>[e(C,{modelValue:a.formData.protocol,"onUpdate:modelValue":r[3]||(r[3]=t=>a.formData.protocol=t),placeholder:"请选择协议",clearable:""},{default:l(()=>[e(p,{label:"TCP",value:"TCP"}),e(p,{label:"UDP",value:"UDP"}),e(p,{label:"ICMP",value:"ICMP"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(s,{prop:"sourcePort",label:"源端口:"},{default:l(()=>[e(u,{modelValue:a.formData.sourcePort,"onUpdate:modelValue":r[4]||(r[4]=t=>a.formData.sourcePort=t),placeholder:"如：80 或 80-90 或 80,81,82",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(s,{prop:"mappedPort",label:"映射端口:"},{default:l(()=>[e(u,{modelValue:a.formData.mappedPort,"onUpdate:modelValue":r[5]||(r[5]=t=>a.formData.mappedPort=t),placeholder:"如：8080 或 8080-8090",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(s,{prop:"lineId",label:"关联线路:"},{default:l(()=>[e(C,{modelValue:a.formData.lineId,"onUpdate:modelValue":r[6]||(r[6]=t=>a.formData.lineId=t),placeholder:"请选择关联线路",clearable:"",filterable:""},{default:l(()=>[(f(!0),v(k,null,x(a.internetLines,t=>(f(),S(p,{key:t.id,label:`${t.line_name} (${t.provider})`,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(s,{prop:"datacenter",label:"机房:"},{default:l(()=>[e(C,{modelValue:a.formData.datacenter,"onUpdate:modelValue":r[7]||(r[7]=t=>a.formData.datacenter=t),placeholder:"请选择机房",clearable:""},{default:l(()=>[(f(!0),v(k,null,x(a.datacenters,t=>(f(),S(p,{key:t.dict_code,label:t.dict_value,value:t.dict_value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(s,{prop:"assignedUser",label:"使用人:"},{default:l(()=>[e(u,{modelValue:a.formData.assignedUser,"onUpdate:modelValue":r[8]||(r[8]=t=>a.formData.assignedUser=t),placeholder:"请输入使用人",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(s,{prop:"status",label:"状态:"},{default:l(()=>[e(C,{modelValue:a.formData.status,"onUpdate:modelValue":r[9]||(r[9]=t=>a.formData.status=t),placeholder:"请选择状态",clearable:""},{default:l(()=>[e(p,{label:"启用",value:"active"}),e(p,{label:"禁用",value:"inactive"}),e(p,{label:"待定",value:"pending"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(s,{prop:"description",label:"功能说明:"},{default:l(()=>[e(u,{modelValue:a.formData.description,"onUpdate:modelValue":r[10]||(r[10]=t=>a.formData.description=t),type:"textarea",rows:2,placeholder:"请输入功能说明"},null,8,["modelValue"])]),_:1}),e(s,{prop:"whitelist",label:"白名单IP:"},{default:l(()=>[U("div",Y,[e(u,{modelValue:a.whitelistInput,"onUpdate:modelValue":r[11]||(r[11]=t=>a.whitelistInput=t),placeholder:"请输入IP地址，支持CIDR格式，如：*********** 或 ***********/24",onKeyup:N(n.addWhitelistIp,["enter"])},{append:l(()=>[e(_,{onClick:n.addWhitelistIp},{default:l(()=>[b("添加")]),_:1},8,["onClick"])]),_:1},8,["modelValue","onKeyup"]),a.formData.whitelist&&a.formData.whitelist.length>0?(f(),v("div",$,[(f(!0),v(k,null,x(a.formData.whitelist,(t,h)=>(f(),S(A,{key:h,closable:"",onClose:m=>n.removeWhitelistIp(h),style:{margin:"5px 5px 0 0"}},{default:l(()=>[b(L(t),1)]),_:2},1032,["onClose"]))),128))])):B("",!0)])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),e(M,{modelValue:a.dialogVisible.edit,"onUpdate:modelValue":r[27]||(r[27]=t=>a.dialogVisible.edit=t),title:"编辑IP映射",width:"600","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[U("div",le,[e(_,{onClick:r[26]||(r[26]=t=>a.dialogVisible.edit=!1),disabled:a.submitLoading},{default:l(()=>[b("取消")]),_:1},8,["disabled"]),e(_,{type:"primary",onClick:n.validateAndSubmitEdit,loading:a.submitLoading},{default:l(()=>[b("更新")]),_:1},8,["onClick","loading"])])]),default:l(()=>[e(F,{model:a.formData,rules:a.rules,ref:"editFormRef","label-position":"right","label-width":"120px"},{default:l(()=>[e(V,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(s,{prop:"internetIp",label:"互联网IP:"},{default:l(()=>[e(u,{modelValue:a.formData.internetIp,"onUpdate:modelValue":r[14]||(r[14]=t=>a.formData.internetIp=t),placeholder:"请输入互联网IP",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(s,{prop:"dmzIp",label:"DMZ-IP:"},{default:l(()=>[e(u,{modelValue:a.formData.dmzIp,"onUpdate:modelValue":r[15]||(r[15]=t=>a.formData.dmzIp=t),placeholder:"请输入DMZ-IP",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(s,{prop:"managementIp",label:"管理IP:"},{default:l(()=>[e(u,{modelValue:a.formData.managementIp,"onUpdate:modelValue":r[16]||(r[16]=t=>a.formData.managementIp=t),placeholder:"请输入管理IP",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(s,{prop:"protocol",label:"协议:"},{default:l(()=>[e(C,{modelValue:a.formData.protocol,"onUpdate:modelValue":r[17]||(r[17]=t=>a.formData.protocol=t),placeholder:"请选择协议",clearable:""},{default:l(()=>[e(p,{label:"TCP",value:"TCP"}),e(p,{label:"UDP",value:"UDP"}),e(p,{label:"ICMP",value:"ICMP"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(s,{prop:"sourcePort",label:"源端口:"},{default:l(()=>[e(u,{modelValue:a.formData.sourcePort,"onUpdate:modelValue":r[18]||(r[18]=t=>a.formData.sourcePort=t),placeholder:"如：80 或 80-90 或 80,81,82",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(s,{prop:"mappedPort",label:"映射端口:"},{default:l(()=>[e(u,{modelValue:a.formData.mappedPort,"onUpdate:modelValue":r[19]||(r[19]=t=>a.formData.mappedPort=t),placeholder:"如：8080 或 8080-8090",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(s,{prop:"lineId",label:"关联线路:"},{default:l(()=>[e(C,{modelValue:a.formData.lineId,"onUpdate:modelValue":r[20]||(r[20]=t=>a.formData.lineId=t),placeholder:"请选择关联线路",clearable:"",filterable:""},{default:l(()=>[(f(!0),v(k,null,x(a.internetLines,t=>(f(),S(p,{key:t.id,label:`${t.line_name} (${t.provider})`,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(s,{prop:"datacenter",label:"机房:"},{default:l(()=>[e(C,{modelValue:a.formData.datacenter,"onUpdate:modelValue":r[21]||(r[21]=t=>a.formData.datacenter=t),placeholder:"请选择机房",clearable:""},{default:l(()=>[(f(!0),v(k,null,x(a.datacenters,t=>(f(),S(p,{key:t.dict_code,label:t.dict_value,value:t.dict_value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:20},{default:l(()=>[e(d,{span:12},{default:l(()=>[e(s,{prop:"assignedUser",label:"使用人:"},{default:l(()=>[e(u,{modelValue:a.formData.assignedUser,"onUpdate:modelValue":r[22]||(r[22]=t=>a.formData.assignedUser=t),placeholder:"请输入使用人",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:12},{default:l(()=>[e(s,{prop:"status",label:"状态:"},{default:l(()=>[e(C,{modelValue:a.formData.status,"onUpdate:modelValue":r[23]||(r[23]=t=>a.formData.status=t),placeholder:"请选择状态",clearable:""},{default:l(()=>[e(p,{label:"启用",value:"active"}),e(p,{label:"禁用",value:"inactive"}),e(p,{label:"待定",value:"pending"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(s,{prop:"description",label:"功能说明:"},{default:l(()=>[e(u,{modelValue:a.formData.description,"onUpdate:modelValue":r[24]||(r[24]=t=>a.formData.description=t),type:"textarea",rows:2,placeholder:"请输入功能说明"},null,8,["modelValue"])]),_:1}),e(s,{prop:"whitelist",label:"白名单IP:"},{default:l(()=>[U("div",ae,[e(u,{modelValue:a.whitelistInput,"onUpdate:modelValue":r[25]||(r[25]=t=>a.whitelistInput=t),placeholder:"请输入IP地址，支持CIDR格式，如：*********** 或 ***********/24",onKeyup:N(n.addWhitelistIp,["enter"])},{append:l(()=>[e(_,{onClick:n.addWhitelistIp},{default:l(()=>[b("添加")]),_:1},8,["onClick"])]),_:1},8,["modelValue","onKeyup"]),a.formData.whitelist&&a.formData.whitelist.length>0?(f(),v("div",te,[(f(!0),v(k,null,x(a.formData.whitelist,(t,h)=>(f(),S(A,{key:h,closable:"",onClose:m=>n.removeWhitelistIp(h),style:{margin:"5px 5px 0 0"}},{default:l(()=>[b(L(t),1)]),_:2},1032,["onClose"]))),128))])):B("",!0)])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),e(M,{modelValue:a.dialogVisible.delete,"onUpdate:modelValue":r[29]||(r[29]=t=>a.dialogVisible.delete=t),title:"删除IP映射",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[U("div",re,[e(_,{onClick:r[28]||(r[28]=t=>a.dialogVisible.delete=!1)},{default:l(()=>[b("取消")]),_:1}),e(_,{type:"danger",onClick:n.submitDelete},{default:l(()=>[b("确认删除")]),_:1},8,["onClick"])])]),default:l(()=>[e(O,{type:"warning",title:`确定要删除IP映射 '${a.formData.internetIp}' 吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),e(T,{class:"search-card"},{default:l(()=>[e(F,{inline:!0},{default:l(()=>[e(V,{gutter:10},{default:l(()=>[e(d,{span:6},{default:l(()=>[e(s,{label:"互联网IP"},{default:l(()=>[e(u,{modelValue:a.search.internetIp,"onUpdate:modelValue":r[30]||(r[30]=t=>a.search.internetIp=t),placeholder:"请输入互联网IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:6},{default:l(()=>[e(s,{label:"协议"},{default:l(()=>[e(C,{modelValue:a.search.protocol,"onUpdate:modelValue":r[31]||(r[31]=t=>a.search.protocol=t),placeholder:"请选择协议",clearable:"",class:"form-control"},{default:l(()=>[e(p,{label:"TCP",value:"TCP"}),e(p,{label:"UDP",value:"UDP"}),e(p,{label:"ICMP",value:"ICMP"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{span:6},{default:l(()=>[e(s,{label:"机房"},{default:l(()=>[e(C,{modelValue:a.search.datacenter,"onUpdate:modelValue":r[32]||(r[32]=t=>a.search.datacenter=t),placeholder:"请选择机房",clearable:"",class:"form-control"},{default:l(()=>[(f(!0),v(k,null,x(a.datacenters,t=>(f(),S(p,{key:t.dict_code,label:t.dict_value,value:t.dict_value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{span:6},{default:l(()=>[e(s,{label:"状态"},{default:l(()=>[e(C,{modelValue:a.search.status,"onUpdate:modelValue":r[33]||(r[33]=t=>a.search.status=t),placeholder:"请选择状态",clearable:"",class:"form-control"},{default:l(()=>[e(p,{label:"启用",value:"active"}),e(p,{label:"禁用",value:"inactive"}),e(p,{label:"待定",value:"pending"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(V,{gutter:10},{default:l(()=>[e(d,{span:6},{default:l(()=>[e(s,{label:"映射端口"},{default:l(()=>[e(u,{modelValue:a.search.mappedPort,"onUpdate:modelValue":r[34]||(r[34]=t=>a.search.mappedPort=t),placeholder:"请输入映射端口",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:6},{default:l(()=>[e(s,{label:"使用人"},{default:l(()=>[e(u,{modelValue:a.search.assignedUser,"onUpdate:modelValue":r[35]||(r[35]=t=>a.search.assignedUser=t),placeholder:"请输入使用人",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:6},{default:l(()=>[e(s,{label:"关键词"},{default:l(()=>[e(u,{modelValue:a.search.keyword,"onUpdate:modelValue":r[36]||(r[36]=t=>a.search.keyword=t),placeholder:"搜索IP、端口、说明等",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(d,{span:6},{default:l(()=>[e(s,null,{default:l(()=>[e(_,{type:"primary",onClick:n.getData},{default:l(()=>[b("搜索")]),_:1},8,["onClick"]),e(_,{onClick:n.resetSearch},{default:l(()=>[b("重置")]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(T,{class:"toolbar-card"},{default:l(()=>[U("div",oe,[e(_,{type:"primary",onClick:n.showAddDialog},{default:l(()=>[b("新增IP映射")]),_:1},8,["onClick"]),e(_,{onClick:n.exportData,loading:a.exportLoading},{default:l(()=>[b("导出Excel")]),_:1},8,["onClick","loading"]),e(_,{type:"danger",disabled:a.selectedRows.length===0||a.deleteLoading,onClick:n.batchDelete,loading:a.deleteLoading},{default:l(()=>[b(" 批量删除 ("+L(a.selectedRows.length)+") ",1)]),_:1},8,["disabled","onClick","loading"])])]),_:1}),e(M,{modelValue:a.dialogVisible.batchDelete,"onUpdate:modelValue":r[38]||(r[38]=t=>a.dialogVisible.batchDelete=t),title:"批量删除IP映射",width:"500","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[U("div",de,[e(_,{onClick:r[37]||(r[37]=t=>a.dialogVisible.batchDelete=!1)},{default:l(()=>[b("取消")]),_:1}),e(_,{type:"danger",onClick:n.submitBatchDelete},{default:l(()=>[b("确认删除")]),_:1},8,["onClick"])])]),default:l(()=>[e(O,{type:"warning",title:`确定要删除选中的 ${a.selectedRows.length} 条IP映射记录吗？`,closable:!1},null,8,["title"]),U("div",ie,[se,U("ul",null,[(f(!0),v(k,null,x(a.selectedRows,t=>(f(),v("li",{key:t.id},L(t.internetIp)+":"+L(t.mappedPort)+" ("+L(t.protocol)+") ",1))),128))])])]),_:1},8,["modelValue"]),e(T,{class:"table-card"},{default:l(()=>[q((f(),S(D,{data:a.tableData,stripe:"",border:"","element-loading-text":"加载中...",onSelectionChange:n.handleSelectionChange},{default:l(()=>[e(I,{type:"selection",width:"55"}),e(I,{prop:"internetIp",label:"互联网IP","min-width":"120"}),e(I,{prop:"dmzIp",label:"DMZ-IP","min-width":"120"}),e(I,{prop:"managementIp",label:"管理IP","min-width":"120"}),e(I,{prop:"sourcePort",label:"源端口",width:"100"}),e(I,{prop:"mappedPort",label:"映射端口",width:"100"}),e(I,{prop:"protocol",label:"协议",width:"80"}),e(I,{prop:"description",label:"功能说明","min-width":"150","show-overflow-tooltip":""}),e(I,{prop:"datacenter",label:"机房",width:"100"}),e(I,{prop:"line_name",label:"关联线路","min-width":"120","show-overflow-tooltip":""}),e(I,{prop:"assignedUser",label:"使用人",width:"100"}),e(I,{prop:"status",label:"状态",width:"80"},{default:l(t=>[e(A,{type:t.row.status==="active"?"success":t.row.status==="inactive"?"danger":"warning"},{default:l(()=>[b(L(t.row.status==="active"?"启用":t.row.status==="inactive"?"禁用":"待定"),1)]),_:2},1032,["type"])]),_:1}),e(I,{prop:"whitelist",label:"白名单","min-width":"150","show-overflow-tooltip":""},{default:l(t=>[t.row.whitelist&&t.row.whitelist.length>0?(f(),v("span",ne,L(Array.isArray(t.row.whitelist)?t.row.whitelist.join(", "):t.row.whitelist),1)):(f(),v("span",pe,"-"))]),_:1}),e(I,{label:"操作",width:"150",fixed:"right"},{default:l(t=>[e(_,{size:"small",onClick:h=>n.editIpMapping(t.row)},{default:l(()=>[b("编辑")]),_:2},1032,["onClick"]),e(_,{size:"small",type:"danger",onClick:h=>n.deleteIpMapping(t.row)},{default:l(()=>[b("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[E,a.loading]]),U("div",ue,[e(g,{"current-page":a.currentPage,"onUpdate:currentPage":r[39]||(r[39]=t=>a.currentPage=t),"page-size":a.pageSize,"onUpdate:pageSize":r[40]||(r[40]=t=>a.pageSize=t),"page-sizes":[10,20,50,100],total:a.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:n.handleSizeChange,onCurrentChange:n.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const fe=Z(H,[["render",me],["__scopeId","data-v-8dc17e29"]]);export{fe as default};
