import{_ as qe,A as re,ag as ie,ap as de,G as ce,c as p,C as ue,a as r,D as Ge,m as v,w as s,f as l,F as M,l as S,x as f,t as y,b as k,h as m,g as Qe,r as h,S as $,o as Je,ad as Ke,p as Xe,q as Ze,E as u,Q as $e,Y as ea,U as ee,e as i,ae as aa,n as ta}from"./index-MGgq8mV5.js";import{t as oa,p as me,a as ge,b as ae}from"./dateUtils-BmBarIng.js";const la={name:"OpsCalendar",components:{Refresh:ce,Calendar:de,Upload:ie,Download:re},setup(){const W=ea(),{proxy:d}=Qe(),G=h(!1),e=h(!1),te=h(!1),Q=h(!1),N=h(!1),b=h(!1),E=h(!1),w=h((()=>{const a=localStorage.getItem("ops_calendar_selected_month");if(a)return console.log("从localStorage恢复月份:",a),a;{const o=new Date().toISOString().slice(0,7);return console.log("首次访问，使用当前月份:",o),o}})()),_=h([]),I=h(""),j=h([]),Y=h([]),P=h({main_duty_user:[],deputy_duty_user:[],duty_manager:[],simulation_user:[],inspection_user:[]}),O=h(null),J=h(""),K=h({}),X=h({}),H=h([]),z=h([]),A=h(1),t=h(10),L=h(0),oe=h(0),x=h(!1),U=h(!1),Z=h(!1),_e=$(()=>{const[a,o]=w.value.split("-").map(Number);return`${a}年${o}月`}),pe=$(()=>{if(!I.value)return"";const a=new Date(I.value);return`${a.getFullYear()}年${a.getMonth()+1}月${a.getDate()}日`}),he=$(()=>{if(!_.value||_.value.length===0)return 0;const[a,o]=w.value.split("-").map(Number),n=`${a}${o.toString().padStart(2,"0")}`;console.log("计算月度变更统计:",{currentDate:w.value,monthPrefix:n,calendarDataLength:_.value.length});let c=0;for(const D of _.value)if(D.day&&typeof D.day=="string"&&D.day.startsWith(n)){const T=Number(D.change_count)||0;c+=T,T>0&&console.log(`找到变更: ${D.day}, 变更数量: ${T} (原始值: ${D.change_count}, 类型: ${typeof D.change_count})`)}return console.log("月度变更总数:",c),c}),ye=$(()=>{const[a,o]=w.value.split("-").map(Number),n=new Date(a,o-1,1),D=new Date(a,o,0).getDate(),T=n.getDay();console.log("=== 日历计算开始 ==="),console.log(`年月: ${a}-${o}`),console.log(`本月天数: ${D}`),console.log(`1号是星期: ${T} (0=周日)`);const q=[];for(let V=0;V<T;V++){const F=new Date(a,o-1,-T+V+1);q.push({date:ae(F),dayNumber:F.getDate(),isCurrentMonth:!1,isWorkday:!1,isHoliday:!1,changeCount:0,changeSummary:""})}console.log(`添加了 ${T} 个上月填充天数`);for(let V=1;V<=D;V++){const F=new Date(a,o-1,V),je=ae(F),He=ge(F),g=_.value.find(Ae=>Ae.day===He);q.push({date:je,dayNumber:V,isCurrentMonth:!0,isWorkday:g?g.wrk===1:F.getDay()>=1&&F.getDay()<=5,isHoliday:g?g.spr===1:!1,changeCount:g?g.change_count:0,changeSummary:g?g.change_summary:"",mainDutyUser:g?g.main_duty_user:"",deputyDutyUser:g?g.deputy_duty_user:"",dutyManager:g?g.duty_manager:"",simulationUser:g?g.simulation_user:"",inspectionUser:g?g.inspection_user:"",mainDutyName:g&&g.main_duty_name||"",deputyDutyName:g&&g.deputy_duty_name||"",dutyManagerName:g&&g.duty_manager_name||"",simulationName:g&&g.simulation_name||"",inspectionName:g&&g.inspection_name||""})}const We=42-q.length;for(let V=1;V<=We;V++){const F=new Date(a,o,V);q.push({date:ae(F),dayNumber:F.getDate(),isCurrentMonth:!1,isWorkday:!1,isHoliday:!1,changeCount:0,changeSummary:""})}return q});oa();const R=async()=>{G.value=!0;try{const[a,o]=w.value.split("-").map(Number),n=await d.$axios.post("/api/get_ops_calendar",{year:a,month:o});n.data.code===0?_.value=n.data.msg:u.error(`获取日历数据失败: ${n.data.msg}`)}catch(a){console.error("获取日历数据失败:",a),u.error("获取日历数据失败")}finally{G.value=!1}},fe=async a=>{e.value=!0;try{const o=me(a),n=await d.$axios.post("/api/get_ops_calendar_changes",{date:o});n.data.code===0?j.value=n.data.msg:u.error(`获取变更详情失败: ${n.data.msg}`)}catch(o){console.error("获取变更详情失败:",o),u.error("获取变更详情失败")}finally{e.value=!1}},ve=a=>{const o=["calendar-day"],n=ae(new Date);return a.isCurrentMonth&&a.date===n&&o.push("today"),a.isCurrentMonth?(a.isHoliday?o.push("holiday"):a.isWorkday?o.push("workday"):o.push("weekend"),a.changeCount>0&&o.push("has-change")):o.push("other-month"),o.join(" ")},we=async()=>{localStorage.setItem("ops_calendar_selected_month",w.value),_.value=[],await ee(),await R(),console.log("=== 月份切换完成 ===")},De=()=>{R()},be=async()=>{const a=new Date().toISOString().slice(0,7);console.log(`回到当前月: ${w.value} -> ${a}`),w.value!==a?(w.value=a,localStorage.setItem("ops_calendar_selected_month",a),_.value=[],await ee(),await R()):u.info("当前已经是本月")},ke=async()=>{const[a,o]=w.value.split("-").map(Number);let n=a,c=o-1;c<1&&(c=12,n=a-1);const D=`${n}-${c.toString().padStart(2,"0")}`;console.log(`切换到上个月: ${w.value} -> ${D}`),w.value=D,localStorage.setItem("ops_calendar_selected_month",D),_.value=[],await ee(),await R()},Ce=async()=>{const[a,o]=w.value.split("-").map(Number);let n=a,c=o+1;c>12&&(c=1,n=a+1);const D=`${n}-${c.toString().padStart(2,"0")}`;console.log(`切换到下个月: ${w.value} -> ${D}`),w.value=D,localStorage.setItem("ops_calendar_selected_month",D),_.value=[],await ee(),await R()},xe=a=>{a.isCurrentMonth&&(I.value=a.date,console.log("点击日期，当前权限状态:",{hasEditPermission:x.value,hasViewPermission:U.value,changeCount:a.changeCount}),a.changeCount>0?(console.log(`点击日期: ${a.date}, 数据库日期: ${ge(new Date(a.date))}`),te.value=!0,fe(a.date)):(console.log("无变更日期，检查权限..."),x.value===!0?(console.log("有编辑权限，显示值班排班对话框"),le(a)):(console.log("无编辑权限，给出提示"),U.value===!0?u.info("您只有查看权限，无法编辑值班排班。如需查看值班信息，请右键点击日期。"):u.warning("您没有交易日历的编辑权限"))))},Ue=a=>{a.isCurrentMonth&&(console.log("右键点击日期，当前权限状态:",{hasEditPermission:x.value,hasViewPermission:U.value}),U.value===!0||x.value===!0?(console.log("有权限，显示值班排班对话框"),le(a)):(console.log("无权限，拒绝访问"),u.warning("您没有交易日历的编辑权限")))},le=a=>{I.value=a.date;const o=n=>n?n.split(",").map(c=>c.trim()).filter(c=>c):[];P.value={main_duty_user:o(a.mainDutyUser),deputy_duty_user:o(a.deputyDutyUser),duty_manager:o(a.dutyManager),simulation_user:o(a.simulationUser),inspection_user:o(a.inspectionUser)},Q.value=!0},ne=async()=>{Z.value=!0,x.value=!1,U.value=!1;try{const a=localStorage.getItem("loginUsername")||"unknown";console.log("检查用户权限，用户名:",a);const o=await d.$axios.post("/api/check_ops_calendar_duty_permission",{username:a});console.log("权限检查API响应:",o.data),o.data.code===0?(x.value=!!o.data.msg.hasEditPermission,U.value=!!o.data.msg.hasViewPermission,console.log("用户权限检查结果:",{username:a,hasEditPermission:x.value,hasViewPermission:U.value,rawPermissions:o.data.msg}),!U.value&&!x.value?(console.log("用户无任何权限"),u.warning("您没有交易日历的编辑权限")):console.log("用户有权限:",{view:U.value,edit:x.value})):(console.error("权限检查API返回错误:",o.data.msg),u.error(`权限检查失败: ${o.data.msg}`),x.value=!1,U.value=!1)}catch(a){console.error("权限检查请求失败:",a),u.error("权限检查失败"),x.value=!1,U.value=!1}finally{Z.value=!1,console.log("权限检查完成，最终权限状态:",{hasEditPermission:x.value,hasViewPermission:U.value})}},Ne=async()=>{try{const a=await d.$axios.post("/api/get_ops_calendar_users");a.data.code===0?Y.value=a.data.msg:u.error(`获取用户列表失败: ${a.data.msg}`)}catch(a){console.error("获取用户列表失败:",a),u.error("获取用户列表失败")}},Pe=async()=>{N.value=!0;try{const a=me(I.value);console.log(`保存值班排班，原始日期: ${I.value}, 转换后: ${a}`);const o=c=>!c||!Array.isArray(c)||c.length===0?null:c.join(","),n=await d.$axios.post("/api/update_ops_calendar_duty",{day:a,main_duty_user:o(P.value.main_duty_user),deputy_duty_user:o(P.value.deputy_duty_user),duty_manager:o(P.value.duty_manager),simulation_user:o(P.value.simulation_user),inspection_user:o(P.value.inspection_user),updated_by:localStorage.getItem("loginUsername")||"unknown"});n.data.code===0?(u.success("值班排班保存成功"),Q.value=!1,R()):u.error(`保存失败: ${n.data.msg}`)}catch(a){console.error("保存值班排班失败:",a),u.error("保存值班排班失败")}finally{N.value=!1}},Ve=a=>{W.push(`/ops_change_management/detail/${a.id}`)},Me=()=>{const a=localStorage.getItem("loginUsername")||"unknown";u({message:`当前用户: ${a}
编辑权限: ${x.value}
查看权限: ${U.value}`,type:"info",duration:5e3,dangerouslyUseHTMLString:!0}),console.log("=== 权限调试信息 ==="),console.log("当前用户名:",a),console.log("localStorage中的用户名:",{username:localStorage.getItem("username"),loginUsername:localStorage.getItem("loginUsername")}),console.log("权限状态:",{hasEditPermission:x.value,hasViewPermission:U.value,permissionLoading:Z.value}),console.log("===================")},Se=a=>a?a.split(",").map(n=>n.trim()).filter(n=>n).join(" "):"",Ie=a=>{if(!a||a==="")return"";const n=String(a).split(",").map(c=>c.trim()).filter(c=>c);return n.length===0?"":n.length===1?n[0]:n.length<=3?n.join(" "):`${n[0]}等${n.length}人`},Fe=a=>{if(!a)return"未知用户";const o=Y.value.find(n=>n.username===a);return o?o.real_name:a},ze=()=>{b.value=!0,H.value=[],z.value=[],A.value=1,L.value=0,oe.value=0,J.value="/api/import_ops_calendar_duty_preview",K.value={Authorization:`Bearer ${localStorage.getItem("token")||""}`},X.value={username:localStorage.getItem("loginUsername")||"unknown"}},Ee=async()=>{try{const a=await d.$axios.post("/api/download_ops_calendar_duty_template",{},{responseType:"blob"}),o=new Blob([a.data]),n=window.URL.createObjectURL(o),c=document.createElement("a");c.href=n,c.download="值班排班导入模板.xlsx",document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(n),u.success("模板下载成功")}catch(a){console.error("下载模板失败:",a),u.error("下载模板失败")}},Le=()=>{u.warning("最多只能上传1个文件")},Re=(a,o)=>{H.value=o,a.status==="ready"&&se(a.raw)},Te=a=>{const o=a.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||a.type==="application/vnd.ms-excel",n=a.size/1024/1024<10;return o?n?!0:(u.error("文件大小不能超过10MB！"),!1):(u.error("只能上传Excel格式的文件！"),!1)},se=async a=>{E.value=!0;try{const o=new FormData;o.append("file",a),o.append("username",localStorage.getItem("loginUsername")||"unknown");const n=await d.$axios.post("/api/import_ops_calendar_duty_preview",o,{headers:{Authorization:`Bearer ${localStorage.getItem("authToken")||""}`}});n.data.code===0?(z.value=n.data.msg.preview_data||[],L.value=n.data.msg.valid_count||0,oe.value=n.data.msg.invalid_count||0,u.success(`文件解析成功，共${z.value.length}条记录`)):(u.error(`文件解析失败: ${n.data.msg}`),z.value=[])}catch(o){console.error("文件预览失败:",o),u.error("文件解析失败，请检查文件格式"),z.value=[]}finally{E.value=!1}},Be=async()=>{if(L.value===0){u.warning("没有有效的记录可以导入");return}try{await $e.confirm(`确认导入 ${L.value} 条有效记录吗？无效记录将被忽略。`,"确认导入",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}),E.value=!0;const a=await d.$axios.post("/api/import_ops_calendar_duty_execute",{preview_data:z.value.filter(o=>o.valid),username:localStorage.getItem("loginUsername")||"unknown"});a.data.code===0?(u.success(`导入成功！共导入 ${a.data.msg.success_count} 条记录`),b.value=!1,R()):u.error(`导入失败: ${a.data.msg}`)}catch(a){if(a==="cancel")return;console.error("导入执行失败:",a),u.error("导入执行失败")}finally{E.value=!1}},Ye=(a,o,n)=>{console.log("导入成功:",a)},Oe=(a,o,n)=>{console.error("导入失败:",a),u.error("文件上传失败"),E.value=!1};return Je(async()=>{await ne(),R(),Ne()}),Ke(()=>{document.body.style.overflow=""}),{loading:G,dialogLoading:e,dialogVisible:te,dutyDialogVisible:Q,dutyLoading:N,importDialogVisible:b,importLoading:E,currentDate:w,calendarDays:ye,selectedDate:I,selectedDateChanges:j,userList:Y,dutyForm:P,importUploadRef:O,importUploadUrl:J,importHeaders:K,importUploadData:X,importFileList:H,importPreviewData:z,previewCurrentPage:A,previewPageSize:t,validRecordsCount:L,invalidRecordsCount:oe,currentMonthTitle:_e,selectedDateFormatted:pe,monthChangeCount:he,hasEditPermission:x,hasViewPermission:U,permissionLoading:Z,Refresh:ce,Calendar:de,Upload:ie,Download:re,getDayClass:ve,handleDateChange:we,refreshCalendar:De,goToCurrentMonth:be,goToPreviousMonth:ke,goToNextMonth:Ce,handleDayClick:xe,handleDayRightClick:Ue,showDutyDialog:le,saveDutySchedule:Pe,viewChangeDetail:Ve,checkUserPermission:ne,debugPermissions:Me,showImportDialog:ze,downloadTemplate:Ee,handleImportExceed:Le,handleImportFileChange:Re,beforeImportUpload:Te,uploadForPreview:se,executeImport:Be,handleImportSuccess:Ye,handleImportError:Oe,formatDutyNames:Se,formatDutyDisplay:Ie,getUserRealName:Fe}}},C=W=>(Xe("data-v-f2ca4569"),W=W(),Ze(),W),na={class:"calendar-container"},sa=C(()=>l("div",{class:"operation-description"},[l("strong",null,"左右键点击说明："),m("变更和值班同时存在时，左键查看变更详情，右键编辑值班排班 ")],-1)),ra={class:"card-header"},ia={class:"header-left"},da={class:"card-title"},ca=C(()=>l("div",{class:"header-center"},[l("div",{class:"legend"},[l("span",{class:"legend-item"},[l("span",{class:"legend-color workday"}),m(" 工作日 ")]),l("span",{class:"legend-item"},[l("span",{class:"legend-color weekend"}),m(" 非工作日 ")]),l("span",{class:"legend-item"},[l("span",{class:"legend-color has-change"}),m(" 有变更 ")])])],-1)),ua={class:"header-right"},ma={class:"month-selector"},ga={class:"calendar-grid"},_a=C(()=>l("div",{class:"week-header"},[l("div",{class:"week-day"},"日"),l("div",{class:"week-day"},"一"),l("div",{class:"week-day"},"二"),l("div",{class:"week-day"},"三"),l("div",{class:"week-day"},"四"),l("div",{class:"week-day"},"五"),l("div",{class:"week-day"},"六")],-1)),pa={class:"calendar-body"},ha=["onClick","onContextmenu"],ya={class:"day-header"},fa={class:"day-number"},va=["title"],wa={class:"day-content"},Da={key:0,class:"duty-info"},ba=["title"],ka=["title"],Ca=["title"],xa=["title"],Ua=["title"],Na={class:"dialog-content"},Pa={class:"dialog-header-info"},Va={class:"dialog-footer"},Ma={key:0,class:"tag-display"},Sa={key:0,class:"tag-display"},Ia={key:0,class:"tag-display"},Fa={key:0,class:"tag-display"},za={key:0,class:"tag-display"},Ea={class:"dialog-footer"},La=C(()=>l("p",null,"1. 请使用Excel文件(.xlsx/.xls)，包含以下列：",-1)),Ra=C(()=>l("p",{style:{"margin-left":"20px"}},"• 日期(必填): 格式为 YYYY-MM-DD，如：2024-01-15",-1)),Ta=C(()=>l("p",{style:{"margin-left":"20px"}},"• 主班人员: 填写用户名或真实姓名，多人用英文逗号分隔",-1)),Ba=C(()=>l("p",{style:{"margin-left":"20px"}},"• 副班人员: 填写用户名或真实姓名，多人用英文逗号分隔",-1)),Ya=C(()=>l("p",{style:{"margin-left":"20px"}},"• 值班经理: 填写用户名或真实姓名，多人用英文逗号分隔",-1)),Oa=C(()=>l("p",{style:{"margin-left":"20px"}},"• 仿真人员: 填写用户名或真实姓名，多人用英文逗号分隔",-1)),Wa=C(()=>l("p",{style:{"margin-left":"20px"}},"• 巡检人员: 填写用户名或真实姓名，多人用英文逗号分隔",-1)),ja=C(()=>l("p",null,"2. 文件大小不超过10MB",-1)),Ha=C(()=>l("p",null,"3. 导入前请先下载模板文件确保格式正确",-1)),Aa=C(()=>l("p",null,"4. 人员字段支持填写用户名(如:zhangsan)或真实姓名(如:张三)",-1)),qa=C(()=>l("p",null,"5. 多人排班示例: admin,zhangsan 或 管理员,张三",-1)),Ga={class:"import-actions",style:{"margin-bottom":"20px"}},Qa=C(()=>l("div",{class:"el-upload__text"},[l("div",null,"将Excel文件拖拽到此处"),l("div",null,[m("或"),l("em",null,"点击此区域选择文件")])],-1)),Ja=C(()=>l("div",{class:"el-upload__tip"},[l("div",null,"支持格式：Excel文件 (.xlsx / .xls)"),l("div",null,"文件大小：不超过 10MB")],-1)),Ka={key:0,style:{"margin-top":"20px"}},Xa={style:{"margin-top":"20px"}},Za={class:"dialog-footer"};function $a(W,d,G,e,te,Q){const N=k("el-tag"),b=k("el-button"),E=k("el-date-picker"),B=k("el-form-item"),w=k("el-card"),_=k("el-table-column"),I=k("el-table"),j=k("el-dialog"),Y=k("el-alert"),P=k("el-option"),O=k("el-select"),J=k("el-form"),K=k("Upload"),X=k("el-icon"),H=k("el-upload"),z=k("el-pagination"),A=Ge("loading");return i(),p("div",na,[sa,ue((i(),v(w,{class:"table-card",shadow:"hover"},{header:s(()=>[l("div",ra,[l("div",ia,[l("span",da,y(e.currentMonthTitle),1),e.monthChangeCount>0?(i(),v(N,{key:0,type:"primary",class:"month-tag"},{default:s(()=>[m(" 本月共 "+y(e.monthChangeCount)+" 个变更 ",1)]),_:1})):f("",!0)]),ca,l("div",ua,[r(B,{label:"选择月份：",class:"form-item-inline"},{default:s(()=>[l("div",ma,[r(b,{size:"small",onClick:e.goToPreviousMonth,disabled:e.loading,title:"上个月"},{default:s(()=>[m(" ‹ ")]),_:1},8,["onClick","disabled"]),r(E,{modelValue:e.currentDate,"onUpdate:modelValue":d[0]||(d[0]=t=>e.currentDate=t),type:"month",placeholder:"选择月份",format:"YYYY年MM月","value-format":"YYYY-MM",onChange:e.handleDateChange,style:{width:"140px",margin:"0 6px"},size:"small"},null,8,["modelValue","onChange"]),r(b,{size:"small",onClick:e.goToNextMonth,disabled:e.loading,title:"下个月"},{default:s(()=>[m(" › ")]),_:1},8,["onClick","disabled"])])]),_:1}),r(b,{type:"primary",size:"small",icon:e.Refresh,onClick:e.refreshCalendar,loading:e.loading},{default:s(()=>[m(" 刷新 ")]),_:1},8,["icon","onClick","loading"]),r(b,{type:"success",size:"small",onClick:e.goToCurrentMonth,disabled:e.loading,title:"回到当前月"},{default:s(()=>[m(" 当前月 ")]),_:1},8,["onClick","disabled"]),e.hasEditPermission?(i(),v(b,{key:0,type:"warning",size:"small",icon:e.Upload,onClick:e.showImportDialog,disabled:e.loading},{default:s(()=>[m(" 导入排班 ")]),_:1},8,["icon","onClick","disabled"])):f("",!0)])])]),default:s(()=>[l("div",ga,[_a,l("div",pa,[(i(!0),p(M,null,S(e.calendarDays,t=>(i(),p("div",{key:t.date,class:ta(e.getDayClass(t)),onClick:L=>e.handleDayClick(t),onContextmenu:aa(L=>e.handleDayRightClick(t),["prevent"])},[l("div",ya,[l("div",fa,y(t.dayNumber),1),t.changeCount>0?(i(),p("div",{key:0,class:"change-badge-corner",title:`${t.changeCount}个变更: ${t.changeSummary}`},y(t.changeCount),9,va)):f("",!0)]),l("div",wa,[t.isCurrentMonth&&(t.mainDutyName||t.deputyDutyName||t.dutyManagerName||t.simulationName||t.inspectionName)?(i(),p("div",Da,[t.mainDutyName?(i(),p("div",{key:0,class:"duty-item main-duty",title:`主班: ${e.formatDutyNames(t.mainDutyName)}`}," 主班: "+y(e.formatDutyDisplay(t.mainDutyName)),9,ba)):f("",!0),t.deputyDutyName?(i(),p("div",{key:1,class:"duty-item deputy-duty",title:`副班: ${e.formatDutyNames(t.deputyDutyName)}`}," 副班: "+y(e.formatDutyDisplay(t.deputyDutyName)),9,ka)):f("",!0),t.dutyManagerName?(i(),p("div",{key:2,class:"duty-item manager-duty",title:`值班经理: ${e.formatDutyNames(t.dutyManagerName)}`}," 经理: "+y(e.formatDutyDisplay(t.dutyManagerName)),9,Ca)):f("",!0),t.simulationName?(i(),p("div",{key:3,class:"duty-item simulation-duty",title:`仿真: ${e.formatDutyNames(t.simulationName)}`}," 仿真: "+y(e.formatDutyDisplay(t.simulationName)),9,xa)):f("",!0),t.inspectionName?(i(),p("div",{key:4,class:"duty-item inspection-duty",title:`巡检: ${e.formatDutyNames(t.inspectionName)}`}," 巡检: "+y(e.formatDutyDisplay(t.inspectionName)),9,Ua)):f("",!0)])):f("",!0)])],42,ha))),128))])])]),_:1})),[[A,e.loading]]),r(j,{modelValue:e.dialogVisible,"onUpdate:modelValue":d[2]||(d[2]=t=>e.dialogVisible=t),title:`${e.selectedDateFormatted} 变更详情`,width:"85%",top:"5vh","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:s(()=>[l("div",Va,[r(b,{onClick:d[1]||(d[1]=t=>e.dialogVisible=!1)},{default:s(()=>[m("关闭")]),_:1})])]),default:s(()=>[l("div",Na,[l("div",Pa,[r(N,{type:"info",size:"large"},{default:s(()=>[m(" 共找到 "+y(e.selectedDateChanges.length)+" 个变更 ",1)]),_:1})]),ue((i(),v(I,{data:e.selectedDateChanges,stripe:"",style:{width:"100%"},"header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold"}},{default:s(()=>[r(_,{prop:"change_id",label:"变更编号",width:"150","show-overflow-tooltip":""}),r(_,{prop:"title",label:"变更名称","min-width":"200","show-overflow-tooltip":""}),r(_,{prop:"system",label:"所属系统",width:"120","show-overflow-tooltip":""}),r(_,{label:"变更级别",width:"100"},{default:s(t=>[m(y(t.row.change_level_name||t.row.change_level),1)]),_:1}),r(_,{label:"变更负责人",width:"120"},{default:s(t=>[m(y(t.row.requester_name||t.row.requester),1)]),_:1}),r(_,{label:"实施人","min-width":"150","show-overflow-tooltip":""},{default:s(t=>[m(y(t.row.implementers_name||t.row.implementers),1)]),_:1}),r(_,{label:"操作",width:"100",fixed:"right"},{default:s(t=>[r(b,{type:"primary",link:"",size:"small",onClick:L=>e.viewChangeDetail(t.row)},{default:s(()=>[m(" 查看详情 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[A,e.dialogLoading]])])]),_:1},8,["modelValue","title"]),r(j,{modelValue:e.dutyDialogVisible,"onUpdate:modelValue":d[9]||(d[9]=t=>e.dutyDialogVisible=t),title:`${e.selectedDateFormatted} 值班排班`,width:"600px","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:s(()=>[l("div",Ea,[r(b,{onClick:d[8]||(d[8]=t=>e.dutyDialogVisible=!1)},{default:s(()=>[m(y(e.hasEditPermission?"取消":"关闭"),1)]),_:1}),e.hasEditPermission?(i(),v(b,{key:0,type:"primary",onClick:e.saveDutySchedule,loading:e.dutyLoading},{default:s(()=>[m(" 保存 ")]),_:1},8,["onClick","loading"])):f("",!0)])]),default:s(()=>[e.hasViewPermission&&!e.hasEditPermission?(i(),v(Y,{key:0,title:"您只有查看权限，无法修改值班排班",type:"warning",closable:!1,style:{"margin-bottom":"16px"}})):f("",!0),!e.hasViewPermission&&!e.hasEditPermission?(i(),v(Y,{key:1,title:"您没有交易日历的编辑权限",type:"error",closable:!1,style:{"margin-bottom":"16px"}})):f("",!0),r(J,{model:e.dutyForm,"label-width":"100px",class:"duty-form"},{default:s(()=>[r(B,{label:"主班人员："},{default:s(()=>[r(O,{modelValue:e.dutyForm.main_duty_user,"onUpdate:modelValue":d[3]||(d[3]=t=>e.dutyForm.main_duty_user=t),placeholder:"请选择主班人员（可多选）",clearable:"",filterable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%"},disabled:!e.hasEditPermission},{default:s(()=>[(i(!0),p(M,null,S(e.userList,t=>(i(),v(P,{key:t.username,label:t.real_name,value:t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),e.dutyForm.main_duty_user.length>0?(i(),p("div",Ma,[(i(!0),p(M,null,S(e.dutyForm.main_duty_user,t=>(i(),v(N,{key:t,class:"display-tag",type:"primary",effect:"light",size:"small"},{default:s(()=>[m(y(e.getUserRealName(t)),1)]),_:2},1024))),128))])):f("",!0)]),_:1}),r(B,{label:"副班人员："},{default:s(()=>[r(O,{modelValue:e.dutyForm.deputy_duty_user,"onUpdate:modelValue":d[4]||(d[4]=t=>e.dutyForm.deputy_duty_user=t),placeholder:"请选择副班人员（可多选）",clearable:"",filterable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%"},disabled:!e.hasEditPermission},{default:s(()=>[(i(!0),p(M,null,S(e.userList,t=>(i(),v(P,{key:t.username,label:t.real_name,value:t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),e.dutyForm.deputy_duty_user.length>0?(i(),p("div",Sa,[(i(!0),p(M,null,S(e.dutyForm.deputy_duty_user,t=>(i(),v(N,{key:t,class:"display-tag",type:"success",effect:"light",size:"small"},{default:s(()=>[m(y(e.getUserRealName(t)),1)]),_:2},1024))),128))])):f("",!0)]),_:1}),r(B,{label:"值班经理："},{default:s(()=>[r(O,{modelValue:e.dutyForm.duty_manager,"onUpdate:modelValue":d[5]||(d[5]=t=>e.dutyForm.duty_manager=t),placeholder:"请选择值班经理（可多选）",clearable:"",filterable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%"},disabled:!e.hasEditPermission},{default:s(()=>[(i(!0),p(M,null,S(e.userList,t=>(i(),v(P,{key:t.username,label:t.real_name,value:t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),e.dutyForm.duty_manager.length>0?(i(),p("div",Ia,[(i(!0),p(M,null,S(e.dutyForm.duty_manager,t=>(i(),v(N,{key:t,class:"display-tag",type:"warning",effect:"light",size:"small"},{default:s(()=>[m(y(e.getUserRealName(t)),1)]),_:2},1024))),128))])):f("",!0)]),_:1}),r(B,{label:"仿真人员："},{default:s(()=>[r(O,{modelValue:e.dutyForm.simulation_user,"onUpdate:modelValue":d[6]||(d[6]=t=>e.dutyForm.simulation_user=t),placeholder:"请选择仿真人员（可多选）",clearable:"",filterable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%"},disabled:!e.hasEditPermission},{default:s(()=>[(i(!0),p(M,null,S(e.userList,t=>(i(),v(P,{key:t.username,label:t.real_name,value:t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),e.dutyForm.simulation_user.length>0?(i(),p("div",Fa,[(i(!0),p(M,null,S(e.dutyForm.simulation_user,t=>(i(),v(N,{key:t,class:"display-tag",type:"info",effect:"light",size:"small"},{default:s(()=>[m(y(e.getUserRealName(t)),1)]),_:2},1024))),128))])):f("",!0)]),_:1}),r(B,{label:"巡检人员："},{default:s(()=>[r(O,{modelValue:e.dutyForm.inspection_user,"onUpdate:modelValue":d[7]||(d[7]=t=>e.dutyForm.inspection_user=t),placeholder:"请选择巡检人员（可多选）",clearable:"",filterable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",style:{width:"100%"},disabled:!e.hasEditPermission},{default:s(()=>[(i(!0),p(M,null,S(e.userList,t=>(i(),v(P,{key:t.username,label:t.real_name,value:t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),e.dutyForm.inspection_user.length>0?(i(),p("div",za,[(i(!0),p(M,null,S(e.dutyForm.inspection_user,t=>(i(),v(N,{key:t,class:"display-tag",type:"info",effect:"light",size:"small"},{default:s(()=>[m(y(e.getUserRealName(t)),1)]),_:2},1024))),128))])):f("",!0)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),r(j,{modelValue:e.importDialogVisible,"onUpdate:modelValue":d[13]||(d[13]=t=>e.importDialogVisible=t),title:"批量导入值班排班",width:"900px","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:s(()=>[l("div",Za,[r(b,{onClick:d[12]||(d[12]=t=>e.importDialogVisible=!1)},{default:s(()=>[m(" 关闭 ")]),_:1}),e.importFileList.length>0?(i(),v(b,{key:0,type:"primary",onClick:e.executeImport,loading:e.importLoading,disabled:e.validRecordsCount===0},{default:s(()=>[m(" 确认导入 ("+y(e.validRecordsCount)+"条) ",1)]),_:1},8,["onClick","loading","disabled"])):f("",!0)])]),default:s(()=>[r(Y,{title:"导入说明",type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:s(()=>[La,Ra,Ta,Ba,Ya,Oa,Wa,ja,Ha,Aa,qa]),_:1}),l("div",Ga,[r(b,{type:"success",icon:e.Download,onClick:e.downloadTemplate},{default:s(()=>[m(" 下载模板 ")]),_:1},8,["icon","onClick"])]),r(H,{ref:"importUploadRef",class:"import-upload",action:e.importUploadUrl,headers:e.importHeaders,data:e.importUploadData,"auto-upload":!1,limit:1,"on-exceed":e.handleImportExceed,"on-change":e.handleImportFileChange,"on-success":e.handleImportSuccess,"on-error":e.handleImportError,"before-upload":e.beforeImportUpload,"file-list":e.importFileList,accept:".xlsx,.xls",drag:""},{tip:s(()=>[Ja]),default:s(()=>[r(X,{class:"el-icon--upload"},{default:s(()=>[r(K)]),_:1}),Qa]),_:1},8,["action","headers","data","on-exceed","on-change","on-success","on-error","before-upload","file-list"]),e.importPreviewData.length>0?(i(),p("div",Ka,[l("h4",null,"导入预览 (共 "+y(e.importPreviewData.length)+" 条记录)",1),r(I,{data:e.importPreviewData.slice((e.previewCurrentPage-1)*e.previewPageSize,e.previewCurrentPage*e.previewPageSize),stripe:"",style:{width:"100%"},"max-height":"300px","header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold"}},{default:s(()=>[r(_,{prop:"date",label:"日期",width:"120"}),r(_,{prop:"main_duty_user_name",label:"主班人员",width:"100","show-overflow-tooltip":""}),r(_,{prop:"deputy_duty_user_name",label:"副班人员",width:"100","show-overflow-tooltip":""}),r(_,{prop:"duty_manager_name",label:"值班经理",width:"100","show-overflow-tooltip":""}),r(_,{prop:"simulation_user_name",label:"仿真人员",width:"100","show-overflow-tooltip":""}),r(_,{prop:"inspection_user_name",label:"巡检人员",width:"100","show-overflow-tooltip":""}),r(_,{label:"状态",width:"80"},{default:s(t=>[r(N,{type:t.row.valid?"success":"danger",size:"small"},{default:s(()=>[m(y(t.row.valid?"有效":"无效"),1)]),_:2},1032,["type"])]),_:1}),r(_,{prop:"error_message",label:"错误信息","min-width":"150","show-overflow-tooltip":""})]),_:1},8,["data"]),e.importPreviewData.length>e.previewPageSize?(i(),v(z,{key:0,"current-page":e.previewCurrentPage,"onUpdate:currentPage":d[10]||(d[10]=t=>e.previewCurrentPage=t),"page-size":e.previewPageSize,"onUpdate:pageSize":d[11]||(d[11]=t=>e.previewPageSize=t),"page-sizes":[10,20,50],total:e.importPreviewData.length,layout:"total, sizes, prev, pager, next, jumper",style:{"margin-top":"20px","text-align":"center"}},null,8,["current-page","page-size","total"])):f("",!0),l("div",Xa,[r(N,{type:"success"},{default:s(()=>[m("有效记录: "+y(e.validRecordsCount),1)]),_:1}),r(N,{type:"danger",style:{"margin-left":"10px"}},{default:s(()=>[m("无效记录: "+y(e.invalidRecordsCount),1)]),_:1})])])):f("",!0)]),_:1},8,["modelValue"])])}const ot=qe(la,[["render",$a],["__scopeId","data-v-f2ca4569"]]);export{ot as default};
