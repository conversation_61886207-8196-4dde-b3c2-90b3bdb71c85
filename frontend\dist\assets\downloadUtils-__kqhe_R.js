const f=(a,n,o,c)=>{try{const e=window.URL.createObjectURL(a),t=document.createElement("a");t.href=e,t.download=n,t.style.display="none",document.body.appendChild(t),t.click(),setTimeout(()=>{try{document.body.removeChild(t),window.URL.revokeObjectURL(e)}catch(r){console.warn("清理下载链接时出错:",r)}},100),o&&typeof o=="function"&&o()}catch(e){console.error("下载文件失败:",e),c&&typeof c=="function"&&c(e)}},m=a=>{if(!a)return null;try{const n=a.match(/filename\*\s*=\s*([^']*)'([^']*)'(.*)/);if(n){const c=n[1]||"UTF-8",e=n[3];if(c.toUpperCase()==="UTF-8")return decodeURIComponent(e)}const o=a.match(/filename\s*=\s*(["']?)([^"';\n]*)\1/);return o?o[2]:null}catch(n){return console.warn("解析Content-Disposition文件名失败:",n),null}},u=async(a,n,o={},c,e)=>{try{const t={method:"GET",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}},r=await fetch(a,{...t,...o});if(!r.ok)throw new Error(`下载失败: ${r.status} ${r.statusText}`);const d=await r.blob();let i=n;const s=r.headers.get("Content-Disposition");if(s){const l=m(s);l&&(i=l)}f(d,i,c,e)}catch(t){console.error("API下载失败:",t),e&&typeof e=="function"&&e(t)}};export{u as d};
