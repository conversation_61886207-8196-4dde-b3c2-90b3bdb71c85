import{_ as O,A as R,v as T,B as q,c as y,a as t,f as s,w as o,b as m,F as g,l as D,h as c,C as G,D as H,m as V,x as J,p as K,q as M,e as u}from"./index-MGgq8mV5.js";import{u as C,w as Q}from"./xlsx-DH6WiNtO.js";import{F as W}from"./FileSaver.min-Cr9SGvul.js";const X={components:{Plus:q,Search:T,Download:R},data(){var n,l,h;return{userArr:[],loading:!1,productionattributes:[],hasDeletePermission:(n=localStorage.getItem("role_code"))==null?void 0:n.includes("D"),hasUpdatePermission:(l=localStorage.getItem("role_code"))==null?void 0:l.includes("U"),hasInsertPermission:(h=localStorage.getItem("role_code"))==null?void 0:h.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{system_abbreviation:"",main_admin:"",production_attribute:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,external_system_id:"",system_abbreviation:"",main_admin:"",backup_admin:"",production_attribute:"",system_provider:"",system_function_summary:"",business_department:"",system_form:"",cs_client_name:"",bs_url:"",ip_port:"",monitoring_system_name:"",major_milestones:""},usersList:[]}},mounted(){this.loadData(),this.getDatadict("C","productionattributes"),this.loadUsersList()},methods:{handlePageChange(n){this.search.currentPage=n,this.loadData()},handlePageSizeChange(n){this.search.pageSize=parseInt(n),this.search.currentPage=1,this.loadData()},handleSortChange({prop:n,order:l}){this.search.sortProp=n,this.search.sortOrder=l==="ascending"?"asc":"desc",this.loadData()},resetSearch(){this.search={system_abbreviation:"",main_admin:"",production_attribute:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async loadData(){try{this.loading=!0;const n=await this.$axios.post("/api/get_cmdb_system_admin_responsibility",this.search);this.userArr=n.data.msg,this.search.total=n.data.total}catch(n){console.error("数据加载失败:",n),this.$message.error("数据加载失败")}finally{this.loading=!1}},async submitAdd(){try{await this.$axios.post("/api/add_cmdb_system_admin_responsibility",this.formData),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(n){console.error("添加失败:",n),this.$message.error("添加失败")}},async submitEdit(){try{await this.$axios.post("/api/update_cmdb_system_admin_responsibility",this.formData),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(n){console.error("更新失败:",n),this.$message.error("更新失败")}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_system_admin_responsibility",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(n){console.error("删除失败:",n),this.$message.error("删除失败")}},async getDatadict(n,l){try{const h=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:n});this[l]=h.data.msg}catch(h){console.error("数据加载失败:",h),this.$message.error("数据加载失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={}},handleEdit(n,l){this.dialogVisible.edit=!0,this.formData.id=l.id,this.formData.external_system_id=l.external_system_id,this.formData.system_abbreviation=l.system_abbreviation,this.formData.main_admin=l.main_admin,this.formData.backup_admin=l.backup_admin,this.formData.production_attribute=l.production_attribute,this.formData.system_provider=l.system_provider,this.formData.system_function_summary=l.system_function_summary,this.formData.business_department=l.business_department,this.formData.system_form=l.system_form,this.formData.cs_client_name=l.cs_client_name,this.formData.bs_url=l.bs_url,this.formData.ip_port=l.ip_port,this.formData.monitoring_system_name=l.monitoring_system_name,this.formData.major_milestones=l.major_milestones},handleDelete(n,l){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=l.id,this.formData.external_system_id=l.external_system_id},exportData(){const l=this.$refs.table.columns,h=l.map(_=>_.label),U=this.userArr.map(_=>l.map(x=>_[x.property])),a=[h,...U],p=C.aoa_to_sheet(a),i=C.book_new();C.book_append_sheet(i,p,"Sheet1");const b=Q(i,{bookType:"xlsx",type:"array"}),f=new Blob([b],{type:"application/octet-stream"});W.saveAs(f,"系统管理员责任表-外部机构.xlsx")},async loadUsersList(){try{const n=await this.$axios.post("/api/get_all_users_real_name");this.usersList=n.data.msg}catch(n){console.error("用户列表加载失败:",n),this.$message.error("用户列表加载失败")}}}},r=n=>(K("data-v-495c2fc9"),n=n(),M(),n),Y={class:"user-manage"},Z={class:"dialogdiv"},$=r(()=>s("span",{class:"label"},"外部系统编号:",-1)),ee=r(()=>s("span",{class:"label"},"业务系统简称:",-1)),le=r(()=>s("span",{class:"label"},"主岗:",-1)),ae=r(()=>s("span",{class:"label"},"备岗:",-1)),te=r(()=>s("span",{class:"label"},"生产属性:",-1)),se=r(()=>s("span",{class:"label"},"系统提供方:",-1)),oe=r(()=>s("span",{class:"label"},"系统功能简述:",-1)),ne=r(()=>s("span",{class:"label"},"负责该系统的业务部门:",-1)),re=r(()=>s("span",{class:"label"},"系统形态:",-1)),ie=r(()=>s("span",{class:"label"},"CS客户端程序名称:",-1)),de=r(()=>s("span",{class:"label"},"BS URL地址:",-1)),me=r(()=>s("span",{class:"label"},"IP:端口:",-1)),ue=r(()=>s("span",{class:"label"},"监控调用的业务系统名称:",-1)),_e=r(()=>s("span",{class:"label"},"重大历程:",-1)),pe={class:"dialog-footer"},ce={class:"dialogdiv"},be=r(()=>s("span",{class:"label"},"外部系统编号:",-1)),fe=r(()=>s("span",{class:"label"},"业务系统简称:",-1)),he=r(()=>s("span",{class:"label"},"主岗:",-1)),ye=r(()=>s("span",{class:"label"},"备岗:",-1)),Ve=r(()=>s("span",{class:"label"},"生产属性:",-1)),ge=r(()=>s("span",{class:"label"},"系统提供方:",-1)),De=r(()=>s("span",{class:"label"},"系统功能简述:",-1)),xe=r(()=>s("span",{class:"label"},"负责该系统的业务部门:",-1)),ve=r(()=>s("span",{class:"label"},"系统形态:",-1)),we=r(()=>s("span",{class:"label"},"CS客户端程序名称:",-1)),ke=r(()=>s("span",{class:"label"},"BS URL地址:",-1)),Ce=r(()=>s("span",{class:"label"},"IP:端口:",-1)),Ue=r(()=>s("span",{class:"label"},"监控调用的业务系统名称:",-1)),Se=r(()=>s("span",{class:"label"},"重大历程:",-1)),Pe={class:"dialog-footer"},ze={class:"button-container"},Ie={class:"action-bar unified-action-bar"},Le={class:"action-bar-left"},Be={class:"action-bar-right"},Ae={style:{display:"flex","white-space":"nowrap"}},je={class:"pagination"};function Ee(n,l,h,U,a,p){const i=m("el-input"),b=m("el-option"),f=m("el-select"),_=m("el-button"),x=m("el-dialog"),P=m("el-alert"),v=m("el-form-item"),w=m("el-col"),z=m("Search"),k=m("el-icon"),I=m("el-row"),L=m("el-form"),S=m("el-card"),B=m("Plus"),A=m("Download"),d=m("el-table-column"),j=m("el-table"),E=m("el-pagination"),F=H("loading");return u(),y("div",Y,[t(x,{modelValue:a.dialogVisible.add,"onUpdate:modelValue":l[15]||(l[15]=e=>a.dialogVisible.add=e),title:"新增系统信息",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:o(()=>[s("div",pe,[t(_,{onClick:l[14]||(l[14]=e=>a.dialogVisible.add=!1)},{default:o(()=>[c("返回")]),_:1}),t(_,{type:"primary",onClick:p.submitAdd},{default:o(()=>[c("确定")]),_:1},8,["onClick"])])]),default:o(()=>[s("div",Z,[s("p",null,[$,t(i,{modelValue:a.formData.external_system_id,"onUpdate:modelValue":l[0]||(l[0]=e=>a.formData.external_system_id=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ee,t(i,{modelValue:a.formData.system_abbreviation,"onUpdate:modelValue":l[1]||(l[1]=e=>a.formData.system_abbreviation=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[le,t(f,{modelValue:a.formData.main_admin,"onUpdate:modelValue":l[2]||(l[2]=e=>a.formData.main_admin=e),style:{width:"240px"},placeholder:"请选择主岗",filterable:"",clearable:""},{default:o(()=>[(u(!0),y(g,null,D(a.usersList,e=>(u(),V(b,{key:e.real_name,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[ae,t(f,{modelValue:a.formData.backup_admin,"onUpdate:modelValue":l[3]||(l[3]=e=>a.formData.backup_admin=e),style:{width:"240px"},placeholder:"请选择备岗",filterable:"",clearable:""},{default:o(()=>[(u(!0),y(g,null,D(a.usersList,e=>(u(),V(b,{key:e.real_name,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[te,t(f,{modelValue:a.formData.production_attribute,"onUpdate:modelValue":l[4]||(l[4]=e=>a.formData.production_attribute=e),style:{width:"240px"},placeholder:"请选择生产属性",clearable:""},{default:o(()=>[(u(!0),y(g,null,D(a.productionattributes,e=>(u(),V(b,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[se,t(i,{modelValue:a.formData.system_provider,"onUpdate:modelValue":l[5]||(l[5]=e=>a.formData.system_provider=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[oe,t(i,{type:"textarea",rows:2,modelValue:a.formData.system_function_summary,"onUpdate:modelValue":l[6]||(l[6]=e=>a.formData.system_function_summary=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ne,t(i,{modelValue:a.formData.business_department,"onUpdate:modelValue":l[7]||(l[7]=e=>a.formData.business_department=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[re,t(i,{modelValue:a.formData.system_form,"onUpdate:modelValue":l[8]||(l[8]=e=>a.formData.system_form=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ie,t(i,{modelValue:a.formData.cs_client_name,"onUpdate:modelValue":l[9]||(l[9]=e=>a.formData.cs_client_name=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[de,t(i,{modelValue:a.formData.bs_url,"onUpdate:modelValue":l[10]||(l[10]=e=>a.formData.bs_url=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[me,t(i,{modelValue:a.formData.ip_port,"onUpdate:modelValue":l[11]||(l[11]=e=>a.formData.ip_port=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ue,t(i,{modelValue:a.formData.monitoring_system_name,"onUpdate:modelValue":l[12]||(l[12]=e=>a.formData.monitoring_system_name=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[_e,t(i,{type:"textarea",rows:2,modelValue:a.formData.major_milestones,"onUpdate:modelValue":l[13]||(l[13]=e=>a.formData.major_milestones=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),t(x,{modelValue:a.dialogVisible.edit,"onUpdate:modelValue":l[31]||(l[31]=e=>a.dialogVisible.edit=e),title:"编辑系统信息",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:o(()=>[s("div",Pe,[t(_,{onClick:l[30]||(l[30]=e=>a.dialogVisible.edit=!1)},{default:o(()=>[c("取消")]),_:1}),t(_,{type:"primary",onClick:p.submitEdit},{default:o(()=>[c("更新")]),_:1},8,["onClick"])])]),default:o(()=>[s("div",ce,[s("p",null,[be,t(i,{modelValue:a.formData.external_system_id,"onUpdate:modelValue":l[16]||(l[16]=e=>a.formData.external_system_id=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[fe,t(i,{modelValue:a.formData.system_abbreviation,"onUpdate:modelValue":l[17]||(l[17]=e=>a.formData.system_abbreviation=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[he,t(f,{modelValue:a.formData.main_admin,"onUpdate:modelValue":l[18]||(l[18]=e=>a.formData.main_admin=e),style:{width:"240px"},placeholder:"请选择主岗",filterable:"",clearable:""},{default:o(()=>[(u(!0),y(g,null,D(a.usersList,e=>(u(),V(b,{key:e.real_name,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[ye,t(f,{modelValue:a.formData.backup_admin,"onUpdate:modelValue":l[19]||(l[19]=e=>a.formData.backup_admin=e),style:{width:"240px"},placeholder:"请选择备岗",filterable:"",clearable:""},{default:o(()=>[(u(!0),y(g,null,D(a.usersList,e=>(u(),V(b,{key:e.real_name,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[Ve,t(f,{modelValue:a.formData.production_attribute,"onUpdate:modelValue":l[20]||(l[20]=e=>a.formData.production_attribute=e),style:{width:"240px"},placeholder:"请选择生产属性",clearable:""},{default:o(()=>[(u(!0),y(g,null,D(a.productionattributes,e=>(u(),V(b,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),s("p",null,[ge,t(i,{modelValue:a.formData.system_provider,"onUpdate:modelValue":l[21]||(l[21]=e=>a.formData.system_provider=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[De,t(i,{type:"textarea",rows:2,modelValue:a.formData.system_function_summary,"onUpdate:modelValue":l[22]||(l[22]=e=>a.formData.system_function_summary=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[xe,t(i,{modelValue:a.formData.business_department,"onUpdate:modelValue":l[23]||(l[23]=e=>a.formData.business_department=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ve,t(i,{modelValue:a.formData.system_form,"onUpdate:modelValue":l[24]||(l[24]=e=>a.formData.system_form=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[we,t(i,{modelValue:a.formData.cs_client_name,"onUpdate:modelValue":l[25]||(l[25]=e=>a.formData.cs_client_name=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[ke,t(i,{modelValue:a.formData.bs_url,"onUpdate:modelValue":l[26]||(l[26]=e=>a.formData.bs_url=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[Ce,t(i,{modelValue:a.formData.ip_port,"onUpdate:modelValue":l[27]||(l[27]=e=>a.formData.ip_port=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[Ue,t(i,{modelValue:a.formData.monitoring_system_name,"onUpdate:modelValue":l[28]||(l[28]=e=>a.formData.monitoring_system_name=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])]),s("p",null,[Se,t(i,{type:"textarea",rows:2,modelValue:a.formData.major_milestones,"onUpdate:modelValue":l[29]||(l[29]=e=>a.formData.major_milestones=e),style:{width:"240px"},clearable:""},null,8,["modelValue"])])])]),_:1},8,["modelValue"]),t(x,{modelValue:a.dialogVisible.delete,"onUpdate:modelValue":l[33]||(l[33]=e=>a.dialogVisible.delete=e),title:"删除外部系统编号",width:"500","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:o(()=>[s("div",null,[t(_,{onClick:l[32]||(l[32]=e=>a.dialogVisible.delete=!1)},{default:o(()=>[c("取消")]),_:1}),t(_,{type:"danger",onClick:p.submitDelete},{default:o(()=>[c("确认删除")]),_:1},8,["onClick"])])]),default:o(()=>[t(P,{type:"warning",title:`确定要删除 外部系统编号 为 ${a.formData.external_system_id} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),t(S,{class:"search-card"},{default:o(()=>[t(L,{inline:!0},{default:o(()=>[t(I,{gutter:10},{default:o(()=>[t(w,{span:6},{default:o(()=>[t(v,{label:"业务系统简称"},{default:o(()=>[t(i,{modelValue:a.search.system_abbreviation,"onUpdate:modelValue":l[34]||(l[34]=e=>a.search.system_abbreviation=e),placeholder:"请输入业务系统简称",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),t(w,{span:6},{default:o(()=>[t(v,{label:"主岗"},{default:o(()=>[t(f,{modelValue:a.search.main_admin,"onUpdate:modelValue":l[35]||(l[35]=e=>a.search.main_admin=e),placeholder:"请选择主岗",filterable:"",clearable:"",class:"form-control"},{default:o(()=>[(u(!0),y(g,null,D(a.usersList,e=>(u(),V(b,{key:e.real_name,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(w,{span:6},{default:o(()=>[t(v,{label:"生产属性"},{default:o(()=>[t(f,{modelValue:a.search.production_attribute,"onUpdate:modelValue":l[36]||(l[36]=e=>a.search.production_attribute=e),placeholder:"请选择生产属性",clearable:"",class:"form-control"},{default:o(()=>[(u(!0),y(g,null,D(a.productionattributes,e=>(u(),V(b,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(w,{span:6,class:"search-buttons-col"},{default:o(()=>[t(v,{label:" ",class:"form-item-with-label search-buttons"},{default:o(()=>[s("div",ze,[t(_,{type:"primary",onClick:p.loadData},{default:o(()=>[t(k,null,{default:o(()=>[t(z)]),_:1}),c("查询 ")]),_:1},8,["onClick"]),t(_,{onClick:p.resetSearch},{default:o(()=>[c("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),s("div",Ie,[s("div",Le,[t(_,{type:"success",disabled:!a.hasInsertPermission,onClick:p.handleAdd},{default:o(()=>[t(k,null,{default:o(()=>[t(B)]),_:1}),c("新增系统 ")]),_:1},8,["disabled","onClick"])]),s("div",Be,[t(_,{type:"info",onClick:p.exportData},{default:o(()=>[t(k,null,{default:o(()=>[t(A)]),_:1}),c(" 导出数据 ")]),_:1},8,["onClick"])])]),t(S,{class:"table-card"},{default:o(()=>[G((u(),V(j,{data:a.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:p.handleSortChange},{default:o(()=>[J("",!0),t(d,{prop:"external_system_id",label:"外部系统编号",sortable:""}),t(d,{prop:"system_abbreviation",label:"业务系统简称",sortable:""}),t(d,{prop:"main_admin",label:"主岗",sortable:""}),t(d,{prop:"backup_admin",label:"备岗",sortable:""}),t(d,{prop:"production_attribute",label:"生产属性",sortable:""}),t(d,{prop:"system_provider",label:"系统提供方",sortable:""}),t(d,{prop:"system_function_summary",label:"系统功能简述",sortable:""}),t(d,{prop:"business_department",label:"负责该系统的业务部门",sortable:""}),t(d,{prop:"system_form",label:"系统形态",sortable:""}),t(d,{prop:"cs_client_name",label:"CS客户端程序名称",sortable:""}),t(d,{prop:"bs_url",label:"BS URL地址",sortable:""}),t(d,{prop:"ip_port",label:"IP:端口",sortable:""}),t(d,{prop:"monitoring_system_name",label:"业务系统英文简称",sortable:""}),t(d,{prop:"major_milestones",label:"重大事件",sortable:""}),t(d,{prop:"created_at",label:"创建时间",sortable:""}),t(d,{prop:"created_by",label:"创建人",sortable:""}),t(d,{prop:"updated_at",label:"更新时间",sortable:""}),t(d,{prop:"updated_by",label:"更新人",sortable:""}),t(d,{label:"操作",fixed:"right"},{default:o(e=>[s("div",Ae,[t(_,{size:"small",type:"warning",disabled:!a.hasUpdatePermission,onClick:N=>p.handleEdit(e.$index,e.row)},{default:o(()=>[c("编辑")]),_:2},1032,["disabled","onClick"]),t(_,{size:"small",type:"danger",disabled:!a.hasDeletePermission,onClick:N=>p.handleDelete(e.$index,e.row)},{default:o(()=>[c("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[F,a.loading]]),s("div",je,[t(E,{background:"","current-page":a.search.currentPage,"page-size":a.search.pageSize,total:a.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:p.handlePageSizeChange,onCurrentChange:p.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const Re=O(X,[["render",Ee],["__scopeId","data-v-495c2fc9"]]);export{Re as default};
