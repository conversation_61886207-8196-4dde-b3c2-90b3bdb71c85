const fs = require('fs');
const path = require('path');
const axios = require('axios');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function testImportanceAPI() {
    try {
        console.log('测试重要性数据字典API...');
        
        // 测试使用dict_type参数
        const response1 = await axios.post('http://localhost:3000/api/get_cmdb_data_dictionary', {
            dict_type: 'importance'
        });
        
        console.log('使用dict_type参数的响应:', response1.data);
        
        // 测试使用dict_code参数（应该不会返回数据）
        const response2 = await axios.post('http://localhost:3000/api/get_cmdb_data_dictionary', {
            dict_code: 'importance'
        });
        
        console.log('使用dict_code参数的响应:', response2.data);
        
        console.log('测试完成！');
        
    } catch (error) {
        console.error('测试失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testImportanceAPI();
