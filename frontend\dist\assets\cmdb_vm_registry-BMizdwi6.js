import{_ as M,G as N,A as O,v as G,B as X,c as f,a as t,f as v,w as r,b as _,F as b,l as y,h as g,C as j,D as K,m as h,x as H,t as q,e as s}from"./index-MGgq8mV5.js";import{u as U,w as J}from"./xlsx-DH6WiNtO.js";import{F as Q}from"./FileSaver.min-Cr9SGvul.js";const W={components:{Plus:X,Search:G,Download:O,Refresh:N},data(){var o,a,n;return{userArr:[],loading:!1,operatingsystems:[],datacenter1s:[],operationstatus:[],usersList:[],hasDeletePermission:(o=localStorage.getItem("role_code"))==null?void 0:o.includes("D"),hasUpdatePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("U"),hasInsertPermission:(n=localStorage.getItem("role_code"))==null?void 0:n.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{management_ip:"",hostname:"",admin1:"",admin2:"",host_ip:"",data_center1:"",operating_system:"",monitoring_requirement:"",is_monitored:"",operation_status:"",online_status:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,management_ip:"",hostname:"",function_purpose:"",admin1:"",admin2:"",operating_system:"",data_center1:"",operation_status:"D00035",app_system_id:"",virtual_host_ip:"",data_center2:"",monitoring_requirement:"是",monitoring_requirement_description:"",is_monitored:"是"},rules:{management_ip:[{required:!0,message:"请输入管理IP",trigger:"blur"},{pattern:/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"},{validator:(x,l,m)=>{if(!l){m();return}this.checkVmManagementIpDuplicate(l,this.formData.id||null).then(p=>{p.exists?m(new Error(p.msg)):m()}).catch(p=>{console.error("检查虚拟机管理IP失败:",p),m(new Error("检查虚拟机管理IP失败，请稍后重试"))})},trigger:"blur"}],hostname:[{required:!0,message:"请输入主机名",trigger:"blur"},{min:2,max:50,message:"主机名长度应在2-50个字符之间",trigger:"blur"}],function_purpose:[{required:!0,message:"请输入功能用途",trigger:"blur"}],admin1:[{required:!0,message:"请选择管理员1",trigger:"change"}],operating_system:[{required:!0,message:"请选择操作系统",trigger:"change"}],data_center1:[{required:!0,message:"请选择所属机房",trigger:"change"}],operation_status:[{required:!0,message:"请选择生命周期",trigger:"change"}],monitoring_requirement:[{required:!0,message:"请选择需要监控",trigger:"change"}],monitoring_requirement_description:[{validator:this.validateMonitoringDescription,trigger:"blur"}]}}},mounted(){this.$route.query.search_ip&&(this.search.management_ip=this.$route.query.search_ip),this.loadData(),this.getDatadict("K","operatingsystems"),this.getDatadict("A","datacenter1s"),this.getDatadict("D","operationstatus"),this.loadUsersList(),this.$route.query.from_discovery==="true"&&this.$nextTick(()=>{this.handleAddFromDiscovery()})},methods:{handlePageChange(o){this.search.currentPage=o,this.loadData()},handlePageSizeChange(o){this.search.pageSize=parseInt(o),this.search.currentPage=1,this.loadData()},handleSortChange({prop:o,order:a}){this.search.sortProp=o,this.search.sortOrder=a==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const o=await this.$axios.post("/api/get_cmdb_vm_registry",this.search);this.userArr=o.data.msg,this.search.total=o.data.total}catch(o){console.error("数据加载失败:",o),this.$message.error("数据加载失败")}finally{this.loading=!1}},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){var o,a;try{const n={management_ip:this.formData.management_ip,hostname:this.formData.hostname,function_purpose:this.formData.function_purpose,admin1:this.formData.admin1,admin2:this.formData.admin2||"",host_ip:"",operating_system:this.formData.operating_system,data_center1:this.formData.data_center1||"",operation_status:this.formData.operation_status,admin:"",app_system_id:"",virtual_host_ip:"",data_center2:"",monitoring_requirement:this.formData.monitoring_requirement,monitoring_requirement_description:this.formData.monitoring_requirement_description||"",is_monitored:"是",username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/add_cmdb_vm_registry",n),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(n){console.error("添加失败:",n),this.$message.error(((a=(o=n.response)==null?void 0:o.data)==null?void 0:a.msg)||"添加失败")}},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){var o,a;try{const n={id:this.formData.id,management_ip:this.formData.management_ip,hostname:this.formData.hostname,function_purpose:this.formData.function_purpose,admin1:this.formData.admin1,admin2:this.formData.admin2||"",host_ip:"",operating_system:this.formData.operating_system,data_center1:this.formData.data_center1||"",operation_status:this.formData.operation_status,admin:"",app_system_id:"",virtual_host_ip:"",data_center2:"",monitoring_requirement:this.formData.monitoring_requirement,monitoring_requirement_description:this.formData.monitoring_requirement_description||"",is_monitored:"是",username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/update_cmdb_vm_registry",n),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(n){console.error("更新失败:",n),this.$message.error(((a=(o=n.response)==null?void 0:o.data)==null?void 0:a.msg)||"更新失败")}},async submitDelete(){var o,a;try{const n={...this.formData,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/del_cmdb_vm_registry",n),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(n){console.error("删除失败:",n),this.$message.error(((a=(o=n.response)==null?void 0:o.data)==null?void 0:a.msg)||"删除失败")}},resetSearch(){this.search={management_ip:"",hostname:"",admin1:"",admin2:"",host_ip:"",operating_system:"",data_center1:"",monitoring_requirement:"",is_monitored:"",operation_status:"",online_status:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async getDatadict(o,a){try{const n=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:o});this[a]=n.data.msg}catch(n){console.error("数据加载失败:",n),this.$message.error("数据加载失败")}},getOperationStatusCode(o){if(!o||!this.operationstatus||this.operationstatus.length===0)return"D00035";const a=this.operationstatus.find(n=>n.dict_name===o);return a?a.dict_code:"D00035"},async loadUsersList(){try{const o=await this.$axios.post("/api/get_all_users_real_name");this.usersList=o.data.msg}catch(o){console.error("用户列表加载失败:",o),this.$message.error("用户列表加载失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={id:null,management_ip:"",hostname:"",function_purpose:"",admin1:"",admin2:"",operating_system:"",data_center1:"",operation_status:"D00035",monitoring_requirement:"是",monitoring_requirement_description:"",is_monitored:"是"},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields()})},handleEdit(o,a){this.dialogVisible.edit=!0,this.formData.id=a.id,this.formData.management_ip=a.management_ip,this.formData.hostname=a.hostname,this.formData.function_purpose=a.function_purpose,this.formData.admin1=a.admin1,this.formData.admin2=a.admin2,this.formData.operating_system=a.operating_system_code||a.operating_system,this.formData.data_center1=a.data_center1_code||a.data_center1,this.formData.operation_status=a.operation_status_code||this.getOperationStatusCode(a.operation_status),this.formData.monitoring_requirement=a.monitoring_requirement,this.formData.monitoring_requirement_description=a.monitoring_requirement_description,this.$refs.editFormRef&&this.$nextTick(()=>{this.$refs.editFormRef.clearValidate()})},handleDelete(o,a){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=a.id,this.formData.management_ip=a.management_ip},exportData(){const a=this.$refs.table.columns,n=a.map(u=>u.label),x=this.userArr.map(u=>a.map(w=>u[w.property])),l=[n,...x],m=U.aoa_to_sheet(l),p=U.book_new();U.book_append_sheet(p,m,"Sheet1");const i=J(p,{bookType:"xlsx",type:"array"}),d=new Blob([i],{type:"application/octet-stream"});Q.saveAs(d,"虚拟机信息.xlsx")},getLifecycleTagType(o){switch(o){case"正常":return"success";case"故障":return"danger";case"闲置":return"info";case"报废":return"info";case"预报废":return"warning";default:return o&&o.includes("正常")?"success":o&&o.includes("故障")?"danger":o&&o.includes("闲置")||o&&o.includes("报废")&&!o.includes("预")?"info":o&&o.includes("预报废")?"warning":"info"}},handleAddFromDiscovery(){this.dialogVisible.add=!0;const{ip_address:o,hostname:a,open_ports:n}=this.$route.query;this.formData={management_ip:o||"",hostname:a||"",function_purpose:"",admin1:"",admin2:"",host_ip:"",operating_system:"",data_center1:"",is_monitored:"",remarks:n?`开放端口: ${n}`:""},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.$message.info("请完善资产信息并提交")}),this.$router.replace({path:this.$route.path})},handleMonitoringRequirementChange(o){o==="是"&&(this.formData.monitoring_requirement_description="")},validateMonitoringDescription(o,a,n){this.formData.monitoring_requirement==="否"&&(!a||a.trim()==="")?n(new Error('当需要监控为"否"时，不监控原因为必填项')):n()},async checkVmManagementIpDuplicate(o,a=null){if(!o)return{exists:!1};try{return(await this.$axios.post("/api/check_vm_management_ip",{management_ip:o,id:a,operation_status:this.formData.operation_status})).data}catch(n){throw console.error("检查虚拟机管理IP失败:",n),new Error("检查虚拟机管理IP失败，请稍后重试")}}}},Y={class:"user-manage"},Z={class:"dialogdiv"},$={class:"dialog-footer"},ee={class:"dialogdiv"},te={class:"dialog-footer"},ae={class:"button-container"},le={class:"action-bar unified-action-bar"},re={class:"action-bar-left"},oe={class:"action-bar-right"},ne={key:0},ie={key:1,class:"text-gray-400"},se={style:{display:"flex","white-space":"nowrap"}},de={class:"pagination"};function ue(o,a,n,x,l,m){const p=_("el-input"),i=_("el-form-item"),d=_("el-option"),u=_("el-select"),w=_("el-form"),D=_("el-button"),C=_("el-dialog"),I=_("el-alert"),V=_("el-col"),A=_("Search"),P=_("el-icon"),F=_("el-row"),S=_("el-card"),E=_("Plus"),z=_("Download"),c=_("el-table-column"),k=_("el-tag"),R=_("el-table"),L=_("el-pagination"),T=K("loading");return s(),f("div",Y,[t(C,{modelValue:l.dialogVisible.add,"onUpdate:modelValue":a[11]||(a[11]=e=>l.dialogVisible.add=e),title:"新增虚拟机信息",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:r(()=>[v("div",$,[t(D,{onClick:a[10]||(a[10]=e=>l.dialogVisible.add=!1)},{default:r(()=>[g("返回")]),_:1}),t(D,{type:"primary",onClick:m.validateAndSubmitAdd},{default:r(()=>[g("确定")]),_:1},8,["onClick"])])]),default:r(()=>[v("div",Z,[t(w,{model:l.formData,rules:l.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:r(()=>[t(i,{prop:"management_ip",label:"管理IP:"},{default:r(()=>[t(p,{modelValue:l.formData.management_ip,"onUpdate:modelValue":a[0]||(a[0]=e=>l.formData.management_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),t(i,{prop:"hostname",label:"主机名:"},{default:r(()=>[t(p,{modelValue:l.formData.hostname,"onUpdate:modelValue":a[1]||(a[1]=e=>l.formData.hostname=e),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),t(i,{prop:"function_purpose",label:"功能用途:"},{default:r(()=>[t(p,{modelValue:l.formData.function_purpose,"onUpdate:modelValue":a[2]||(a[2]=e=>l.formData.function_purpose=e),style:{width:"240px"},type:"textarea",clearable:"",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),t(i,{prop:"admin1",label:"管理员1:"},{default:r(()=>[t(u,{modelValue:l.formData.admin1,"onUpdate:modelValue":a[3]||(a[3]=e=>l.formData.admin1=e),style:{width:"240px"},placeholder:"请选择管理员1",clearable:"",filterable:""},{default:r(()=>[(s(!0),f(b,null,y(l.usersList,e=>(s(),h(d,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"admin2",label:"管理员2:"},{default:r(()=>[t(u,{modelValue:l.formData.admin2,"onUpdate:modelValue":a[4]||(a[4]=e=>l.formData.admin2=e),style:{width:"240px"},placeholder:"请选择管理员2",clearable:"",filterable:""},{default:r(()=>[(s(!0),f(b,null,y(l.usersList,e=>(s(),h(d,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"operating_system",label:"操作系统:"},{default:r(()=>[t(u,{modelValue:l.formData.operating_system,"onUpdate:modelValue":a[5]||(a[5]=e=>l.formData.operating_system=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择操作系统"},{default:r(()=>[(s(!0),f(b,null,y(l.operatingsystems,e=>(s(),h(d,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"data_center1",label:"所属机房:"},{default:r(()=>[t(u,{modelValue:l.formData.data_center1,"onUpdate:modelValue":a[6]||(a[6]=e=>l.formData.data_center1=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择所属机房"},{default:r(()=>[(s(!0),f(b,null,y(l.datacenter1s,e=>(s(),h(d,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"operation_status",label:"生命周期:"},{default:r(()=>[t(u,{modelValue:l.formData.operation_status,"onUpdate:modelValue":a[7]||(a[7]=e=>l.formData.operation_status=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择生命周期"},{default:r(()=>[(s(!0),f(b,null,y(l.operationstatus,e=>(s(),h(d,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"monitoring_requirement",label:"需要监控:"},{default:r(()=>[t(u,{modelValue:l.formData.monitoring_requirement,"onUpdate:modelValue":a[8]||(a[8]=e=>l.formData.monitoring_requirement=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择需要监控",onChange:m.handleMonitoringRequirementChange},{default:r(()=>[t(d,{label:"是",value:"是"}),t(d,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),t(i,{prop:"monitoring_requirement_description",label:"不监控原因:",required:l.formData.monitoring_requirement==="否"},{default:r(()=>[t(p,{modelValue:l.formData.monitoring_requirement_description,"onUpdate:modelValue":a[9]||(a[9]=e=>l.formData.monitoring_requirement_description=e),style:{width:"240px"},type:"textarea",rows:3,clearable:"",placeholder:"当需要监控为否时必填",disabled:l.formData.monitoring_requirement==="是"},null,8,["modelValue","disabled"])]),_:1},8,["required"])]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),t(C,{modelValue:l.dialogVisible.edit,"onUpdate:modelValue":a[23]||(a[23]=e=>l.dialogVisible.edit=e),title:"编辑虚拟机信息",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:r(()=>[v("div",te,[t(D,{onClick:a[22]||(a[22]=e=>l.dialogVisible.edit=!1)},{default:r(()=>[g("取消")]),_:1}),t(D,{type:"primary",onClick:m.validateAndSubmitEdit},{default:r(()=>[g("更新")]),_:1},8,["onClick"])])]),default:r(()=>[v("div",ee,[t(w,{model:l.formData,rules:l.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:r(()=>[t(i,{prop:"management_ip",label:"管理IP:"},{default:r(()=>[t(p,{modelValue:l.formData.management_ip,"onUpdate:modelValue":a[12]||(a[12]=e=>l.formData.management_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),t(i,{prop:"hostname",label:"主机名:"},{default:r(()=>[t(p,{modelValue:l.formData.hostname,"onUpdate:modelValue":a[13]||(a[13]=e=>l.formData.hostname=e),style:{width:"240px"},clearable:"",placeholder:"请输入主机名"},null,8,["modelValue"])]),_:1}),t(i,{prop:"function_purpose",label:"功能用途:"},{default:r(()=>[t(p,{modelValue:l.formData.function_purpose,"onUpdate:modelValue":a[14]||(a[14]=e=>l.formData.function_purpose=e),style:{width:"240px"},type:"textarea",clearable:"",placeholder:"请输入功能用途"},null,8,["modelValue"])]),_:1}),t(i,{prop:"admin1",label:"管理员1:"},{default:r(()=>[t(u,{modelValue:l.formData.admin1,"onUpdate:modelValue":a[15]||(a[15]=e=>l.formData.admin1=e),style:{width:"240px"},placeholder:"请选择管理员1",clearable:"",filterable:""},{default:r(()=>[(s(!0),f(b,null,y(l.usersList,e=>(s(),h(d,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"admin2",label:"管理员2:"},{default:r(()=>[t(u,{modelValue:l.formData.admin2,"onUpdate:modelValue":a[16]||(a[16]=e=>l.formData.admin2=e),style:{width:"240px"},placeholder:"请选择管理员2",clearable:"",filterable:""},{default:r(()=>[(s(!0),f(b,null,y(l.usersList,e=>(s(),h(d,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"operating_system",label:"操作系统:"},{default:r(()=>[t(u,{modelValue:l.formData.operating_system,"onUpdate:modelValue":a[17]||(a[17]=e=>l.formData.operating_system=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择操作系统"},{default:r(()=>[(s(!0),f(b,null,y(l.operatingsystems,e=>(s(),h(d,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"data_center1",label:"所属机房:"},{default:r(()=>[t(u,{modelValue:l.formData.data_center1,"onUpdate:modelValue":a[18]||(a[18]=e=>l.formData.data_center1=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择所属机房"},{default:r(()=>[(s(!0),f(b,null,y(l.datacenter1s,e=>(s(),h(d,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"operation_status",label:"生命周期:"},{default:r(()=>[t(u,{modelValue:l.formData.operation_status,"onUpdate:modelValue":a[19]||(a[19]=e=>l.formData.operation_status=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择生命周期"},{default:r(()=>[(s(!0),f(b,null,y(l.operationstatus,e=>(s(),h(d,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"monitoring_requirement",label:"需要监控:"},{default:r(()=>[t(u,{modelValue:l.formData.monitoring_requirement,"onUpdate:modelValue":a[20]||(a[20]=e=>l.formData.monitoring_requirement=e),style:{width:"240px"},clearable:"",filterable:"",placeholder:"请选择需要监控",onChange:m.handleMonitoringRequirementChange},{default:r(()=>[t(d,{label:"是",value:"是"}),t(d,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),t(i,{prop:"monitoring_requirement_description",label:"不监控原因:",required:l.formData.monitoring_requirement==="否"},{default:r(()=>[t(p,{modelValue:l.formData.monitoring_requirement_description,"onUpdate:modelValue":a[21]||(a[21]=e=>l.formData.monitoring_requirement_description=e),style:{width:"240px"},type:"textarea",rows:3,clearable:"",placeholder:"当需要监控为否时必填",disabled:l.formData.monitoring_requirement==="是"},null,8,["modelValue","disabled"])]),_:1},8,["required"])]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),t(C,{modelValue:l.dialogVisible.delete,"onUpdate:modelValue":a[25]||(a[25]=e=>l.dialogVisible.delete=e),title:"删除管理IP",width:"500","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:r(()=>[v("div",null,[t(D,{onClick:a[24]||(a[24]=e=>l.dialogVisible.delete=!1)},{default:r(()=>[g("取消")]),_:1}),t(D,{type:"danger",onClick:m.submitDelete},{default:r(()=>[g("确认删除")]),_:1},8,["onClick"])])]),default:r(()=>[t(I,{type:"warning",title:`确定要删除 IP 为 ${l.formData.management_ip} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),t(S,{class:"search-card"},{default:r(()=>[t(w,{inline:!0},{default:r(()=>[t(F,{gutter:10},{default:r(()=>[t(V,{span:6},{default:r(()=>[t(i,{label:"管理IP"},{default:r(()=>[t(p,{modelValue:l.search.management_ip,"onUpdate:modelValue":a[26]||(a[26]=e=>l.search.management_ip=e),placeholder:"请输入管理IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),t(V,{span:6},{default:r(()=>[t(i,{label:"ESXi主机IP"},{default:r(()=>[t(p,{modelValue:l.search.host_ip,"onUpdate:modelValue":a[27]||(a[27]=e=>l.search.host_ip=e),placeholder:"请输入ESXi主机IP",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),t(V,{span:6},{default:r(()=>[t(i,{label:"主机名"},{default:r(()=>[t(p,{modelValue:l.search.hostname,"onUpdate:modelValue":a[28]||(a[28]=e=>l.search.hostname=e),placeholder:"请输入主机名",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),t(V,{span:6},{default:r(()=>[t(i,{label:"管理员1"},{default:r(()=>[t(u,{modelValue:l.search.admin1,"onUpdate:modelValue":a[29]||(a[29]=e=>l.search.admin1=e),placeholder:"请选择管理员1",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(s(!0),f(b,null,y(l.usersList,e=>(s(),h(d,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(V,{span:6},{default:r(()=>[t(i,{label:"管理员2"},{default:r(()=>[t(u,{modelValue:l.search.admin2,"onUpdate:modelValue":a[30]||(a[30]=e=>l.search.admin2=e),placeholder:"请选择管理员2",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(s(!0),f(b,null,y(l.usersList,e=>(s(),h(d,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(V,{span:6},{default:r(()=>[t(i,{label:"所属机房"},{default:r(()=>[t(u,{modelValue:l.search.data_center1,"onUpdate:modelValue":a[31]||(a[31]=e=>l.search.data_center1=e),placeholder:"请选择所属机房",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(s(!0),f(b,null,y(l.datacenter1s,e=>(s(),h(d,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(V,{span:6},{default:r(()=>[t(i,{label:"操作系统"},{default:r(()=>[t(u,{modelValue:l.search.operating_system,"onUpdate:modelValue":a[32]||(a[32]=e=>l.search.operating_system=e),placeholder:"请选择操作系统",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(s(!0),f(b,null,y(l.operatingsystems,e=>(s(),h(d,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(V,{span:6},{default:r(()=>[t(i,{label:"需要监控"},{default:r(()=>[t(u,{modelValue:l.search.monitoring_requirement,"onUpdate:modelValue":a[33]||(a[33]=e=>l.search.monitoring_requirement=e),placeholder:"请选择需要监控",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[t(d,{label:"是",value:"是"}),t(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(V,{span:6},{default:r(()=>[t(i,{label:"监控状态"},{default:r(()=>[t(u,{modelValue:l.search.is_monitored,"onUpdate:modelValue":a[34]||(a[34]=e=>l.search.is_monitored=e),placeholder:"请选择监控状态",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[t(d,{label:"是",value:"是"}),t(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(V,{span:6},{default:r(()=>[t(i,{label:"生命周期"},{default:r(()=>[t(u,{modelValue:l.search.operation_status,"onUpdate:modelValue":a[35]||(a[35]=e=>l.search.operation_status=e),placeholder:"请选择生命周期",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[(s(!0),f(b,null,y(l.operationstatus,e=>(s(),h(d,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(V,{span:6},{default:r(()=>[t(i,{label:"PING状态"},{default:r(()=>[t(u,{modelValue:l.search.online_status,"onUpdate:modelValue":a[36]||(a[36]=e=>l.search.online_status=e),placeholder:"请选择PING状态",clearable:"",filterable:"",class:"form-control"},{default:r(()=>[t(d,{label:"在线",value:"在线"}),t(d,{label:"离线",value:"离线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(V,{span:24,class:"search-buttons-col"},{default:r(()=>[t(i,{label:" ",class:"form-item-with-label search-buttons"},{default:r(()=>[v("div",ae,[t(D,{type:"primary",onClick:m.loadData},{default:r(()=>[t(P,null,{default:r(()=>[t(A)]),_:1}),g("查询 ")]),_:1},8,["onClick"]),t(D,{onClick:m.resetSearch},{default:r(()=>[g("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),v("div",le,[v("div",re,[t(D,{type:"success",disabled:!l.hasInsertPermission,onClick:m.handleAdd},{default:r(()=>[t(P,null,{default:r(()=>[t(E)]),_:1}),g("新增资产 ")]),_:1},8,["disabled","onClick"])]),v("div",oe,[t(D,{type:"info",onClick:m.exportData},{default:r(()=>[t(P,null,{default:r(()=>[t(z)]),_:1}),g(" 导出数据 ")]),_:1},8,["onClick"])])]),t(S,{class:"table-card"},{default:r(()=>[j((s(),h(R,{data:l.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:m.handleSortChange},{default:r(()=>[H("",!0),t(c,{prop:"management_ip",label:"管理IP",sortable:""}),t(c,{prop:"hostname",label:"主机名",sortable:""}),t(c,{prop:"function_purpose",label:"功能用途",sortable:""}),t(c,{prop:"admin1",label:"管理员1",sortable:""}),t(c,{prop:"admin2",label:"管理员2",sortable:""}),t(c,{prop:"host_ip_display",label:"ESXi主机IP",sortable:""},{default:r(e=>[e.row.host_ip_display?(s(),f("span",ne,q(e.row.host_ip_display),1)):(s(),f("span",ie,"-"))]),_:1}),t(c,{prop:"operating_system",label:"操作系统",sortable:""}),t(c,{prop:"data_center1",label:"所属机房",sortable:""}),t(c,{prop:"operation_status",label:"生命周期",sortable:""},{default:r(e=>[t(k,{type:m.getLifecycleTagType(e.row.operation_status)},{default:r(()=>[g(q(e.row.operation_status),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"monitoring_requirement",label:"需要监控",sortable:""},{default:r(e=>[t(k,{type:e.row.monitoring_requirement==="是"?"success":"danger"},{default:r(()=>[g(q(e.row.monitoring_requirement),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"monitoring_requirement_description",label:"不监控原因",sortable:""}),t(c,{prop:"is_monitored",label:"监控状态",sortable:""},{default:r(e=>[t(k,{type:e.row.is_monitored==="是"?"success":"danger"},{default:r(()=>[g(q(e.row.is_monitored),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"online_status",label:"PING状态",sortable:""},{default:r(e=>[t(k,{type:e.row.online_status==="在线"?"success":"danger"},{default:r(()=>[g(q(e.row.online_status),1)]),_:2},1032,["type"])]),_:1}),t(c,{prop:"created_at",label:"创建时间",sortable:""}),t(c,{prop:"created_by",label:"创建人",sortable:""}),t(c,{prop:"updated_at",label:"更新时间",sortable:""}),t(c,{prop:"updated_by",label:"更新人",sortable:""}),t(c,{label:"操作",fixed:"right"},{default:r(e=>[v("div",se,[t(D,{size:"small",type:"warning",disabled:!l.hasUpdatePermission,onClick:B=>m.handleEdit(e.$index,e.row)},{default:r(()=>[g("编辑")]),_:2},1032,["disabled","onClick"]),t(D,{size:"small",type:"danger",disabled:!l.hasDeletePermission,onClick:B=>m.handleDelete(e.$index,e.row)},{default:r(()=>[g("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[T,l.loading]]),v("div",de,[t(L,{background:"","current-page":l.search.currentPage,"page-size":l.search.pageSize,total:l.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:m.handlePageSizeChange,onCurrentChange:m.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const ce=M(W,[["render",ue],["__scopeId","data-v-aa2108f7"]]);export{ce as default};
