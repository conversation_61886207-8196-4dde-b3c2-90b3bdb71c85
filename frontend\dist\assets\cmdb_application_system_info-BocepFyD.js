import{_ as B,A as N,v as G,B as j,c as g,a as t,f as q,w as s,b as p,F as v,l as V,h as f,C as H,D as K,m as y,x as J,t as w,e as n}from"./index-MGgq8mV5.js";import{u as U,w as Q}from"./xlsx-DH6WiNtO.js";import{F as W}from"./FileSaver.min-Cr9SGvul.js";const X={components:{Plus:j,Search:G,Download:N},data(){var r,a,_;return{userArr:[],loading:!1,productionattributes:[],masterslaveroles:[],backupmodes:[],systemclassifications:[],datacenters:[],operationstatuses:[],systemoperationstatuses:[],operatingsystems:[],businessSystems:[],usersList:[],hasDeletePermission:(r=localStorage.getItem("role_code"))==null?void 0:r.includes("D"),hasUpdatePermission:(a=localStorage.getItem("role_code"))==null?void 0:a.includes("U"),hasInsertPermission:(_=localStorage.getItem("role_code"))==null?void 0:_.includes("I"),dialogVisible:{add:!1,edit:!1,delete:!1},search:{management_ip:"",hostname:"",function_purpose:"",server_admin1:"",data_center:"",machine_usage_status:"",business_system_name:"",system_administrator:"",system_classification:"",system_operation_status:"",monitoring_requirement:"",is_monitored:"",production_attributes:"",operation_status:"",operating_system:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},autoCompleteOptions:{management_ip:[],hostname:[],function_purpose:[],business_system_name:[],internet_ip:[],internet_port:[],remarks:[]},formData:{id:null,management_ip:"",hostname:"",function_purpose:"",server_admin1:"",server_admin2:"",data_center:"",machine_usage_status:"",remarks:"",business_system_name:"",system_administrator:"",system_classification:"",monitoring_requirement:"是",monitoring_requirement_description:"",is_monitored:"",production_attributes:"",master_slave_role:"",related_master_slave_ips:"",backup_mode:"",internet_ip:"",internet_port:"",operating_system:"",has_antivirus_software:"否"},rules:{management_ip:[{required:!0,message:"请输入管理IP",trigger:"blur"},{pattern:/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,message:"请输入正确的IP地址格式",trigger:"blur"}],business_system_name:[{required:!0,message:"请选择归属业务系统",trigger:"change"}],master_slave_role:[{required:!0,message:"请选择主从角色",trigger:"change"}],production_attributes:[{required:!0,message:"请选择生产属性",trigger:"change"}],monitoring_requirement:[{required:!0,message:"请选择需要监控",trigger:"change"}],monitoring_requirement_description:[{required:!1,validator:(c,l,o)=>{this.formData.monitoring_requirement==="否"&&(!l||l.trim()==="")?o(new Error('当需要监控为"否"时，不监控原因为必填项')):o()},trigger:"blur"}],related_master_slave_ips:[{required:!1,validator:(c,l,o)=>{if(this.formData.master_slave_role&&!this.isSingleMachine(this.formData.master_slave_role))if(!l)o(new Error("当主从角色不为单机时，关联主从机IP为必填项"));else{const h=l.split(","),i=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;h.filter(u=>!i.test(u.trim())).length>0?o(new Error("请输入正确的IP地址格式，多个IP请用英文逗号分隔")):o()}else o()},trigger:"blur"}]}}},mounted(){this.$route.query.search_ip&&(this.search.management_ip=this.$route.query.search_ip),this.loadData(),this.getDatadict("C","productionattributes"),this.getDatadict("F","masterslaveroles"),this.getDatadict("G","backupmodes"),this.getDatadict("A","datacenters"),this.getDatadict("I","systemclassifications"),this.getDatadict("D","operationstatuses"),this.getDatadict("H","systemoperationstatuses"),this.getDatadict("K","operatingsystems"),this.getBusinessSystems(),this.loadUsersList(),this.initAutoCompleteOptions(),this.$route.query.from_discovery==="true"&&this.$nextTick(()=>{this.handleAddFromDiscovery()})},methods:{getLifecycleTagType(r){switch(r){case"正常":return"success";case"故障":return"danger";case"闲置":return"info";case"报废":return"info";case"预报废":return"warning";default:return r&&r.includes("正常")?"success":r&&r.includes("故障")?"danger":r&&r.includes("闲置")||r&&r.includes("报废")&&!r.includes("预")?"info":r&&r.includes("预报废")?"warning":"info"}},getSystemOperationStatusTagType(r){switch(r){case"在用":return"success";case"下线":return"danger";case"归档":return"info";default:return r&&(r.includes("在用")||r.includes("运行"))?"success":r&&r.includes("下线")?"danger":(r&&r.includes("归档"),"info")}},handlePageChange(r){this.search.currentPage=r,this.loadData()},handlePageSizeChange(r){this.search.pageSize=parseInt(r),this.search.currentPage=1,this.loadData()},handleSortChange({prop:r,order:a}){this.search.sortProp=r,this.search.sortOrder=a==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const r=await this.$axios.post("/api/get_cmdb_application_system_info",this.search);this.userArr=r.data.msg,this.search.total=r.data.total,this.updateAutoCompleteOptions()}catch{this.$message.error("数据加载失败")}finally{this.loading=!1}},updateAutoCompleteOptions(){["management_ip","hostname","function_purpose"].forEach(a=>{const _=[...new Set(this.userArr.map(c=>c[a]).filter(c=>c&&c.trim()!==""))];this.autoCompleteOptions[a]=_.map(c=>({value:c}))})},querySearchAsync(r,a,_){const c=this.autoCompleteOptions[_]||[];if(!r){a(c.slice(0,10));return}const l=c.filter(o=>o.value.toLowerCase().includes(r.toLowerCase()));a(l.slice(0,10))},handleSelect(r){console.log("Selected:",r)},async validateAndSubmitAdd(){try{await this.$refs.addFormRef.validate(),await this.submitAdd()}catch{this.$message.error("请完善必填项后再提交")}},async submitAdd(){var r,a;try{const _={...this.formData,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/add_cmdb_application_system_info",_),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(_){this.$message.error(((a=(r=_.response)==null?void 0:r.data)==null?void 0:a.msg)||"添加失败")}},async validateAndSubmitEdit(){try{await this.$refs.editFormRef.validate(),await this.submitEdit()}catch{this.$message.error("请完善必填项后再提交")}},async submitEdit(){var r,a;try{const _={...this.formData,username:localStorage.getItem("loginUsername")||"unknown"};await this.$axios.post("/api/update_cmdb_application_system_info",_),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(_){this.$message.error(((a=(r=_.response)==null?void 0:r.data)==null?void 0:a.msg)||"更新失败")}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_application_system_info",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch{this.$message.error("删除失败")}},resetSearch(){this.search={management_ip:"",hostname:"",function_purpose:"",server_admin1:"",data_center:"",machine_usage_status:"",business_system_name:"",system_administrator:"",system_classification:"",system_operation_status:"",monitoring_requirement:"",is_monitored:"",production_attributes:"",operation_status:"",operating_system:"",is_virtual_machine:"",total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.loadData()},async getDatadict(r,a){try{const _=await this.$axios.post("/api/get_cmdb_data_dictionary",{dict_code:r});this[a]=_.data.msg}catch{this.$message.error("数据加载失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={monitoring_requirement:"是",monitoring_requirement_description:"",has_antivirus_software:"否",master_slave_role:""},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.$refs.addFormRef.clearValidate("related_master_slave_ips")})},handleEdit(r,a){this.dialogVisible.edit=!0,this.formData.id=a.id,this.formData.management_ip=a.management_ip,this.formData.hostname=a.hostname,this.formData.function_purpose=a.function_purpose,this.formData.server_admin1=a.server_admin1,this.formData.server_admin2=a.server_admin2,this.formData.data_center=a.data_center_code||a.data_center,this.formData.machine_usage_status=a.machine_usage_status,this.formData.remarks=a.remarks,this.formData.business_system_name=a.business_system_name,this.formData.system_administrator=a.system_administrator,this.formData.system_classification=a.system_classification,this.formData.monitoring_requirement=a.monitoring_requirement,this.formData.monitoring_requirement_description=a.monitoring_requirement_description,this.formData.is_monitored=a.is_monitored,this.formData.production_attributes=a.production_attributes_code||a.production_attributes,this.formData.master_slave_role=a.master_slave_role,this.formData.related_master_slave_ips=a.related_master_slave_ips,this.formData.backup_mode=a.backup_mode,this.formData.internet_ip=a.internet_ip,this.formData.internet_port=a.internet_port,this.formData.operating_system=a.operating_system,this.formData.has_antivirus_software=a.has_antivirus_software,this.$refs.editFormRef&&this.$nextTick(()=>{this.$refs.editFormRef.clearValidate(),this.isSingleMachine(this.formData.master_slave_role)&&this.$refs.editFormRef.clearValidate("related_master_slave_ips"),this.validateRelatedMasterSlaveIps()})},handleDelete(r,a){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=a.id,this.formData.management_ip=a.management_ip},exportData(){const a=this.$refs.table.columns,_=a.map(u=>u.label),c=this.userArr.map(u=>a.map(C=>u[C.property])),l=[_,...c],o=U.aoa_to_sheet(l),h=U.book_new();U.book_append_sheet(h,o,"Sheet1");const i=Q(h,{bookType:"xlsx",type:"array"}),d=new Blob([i],{type:"application/octet-stream"});W.saveAs(d,"应用系统信息.xlsx")},getSingleMachineCode(){const r=this.masterslaveroles.find(a=>a.dict_name==="单机");return r?r.dict_code:"F00047"},isSingleMachine(r){if(!r)return!1;const a=this.getSingleMachineCode();return r===a?!0:r==="单机"},getSingleMachineRequired(){return this.formData.master_slave_role&&!this.isSingleMachine(this.formData.master_slave_role)},async getBusinessSystems(){try{const r=await this.$axios.post("/api/get_cmdb_system_admin_responsibility_company",{currentPage:1,pageSize:1e3,sortProp:"system_abbreviation",sortOrder:"asc"});r.data&&r.data.msg&&(this.businessSystems=r.data.msg.filter(a=>a.system_abbreviation).map(a=>({value:a.system_abbreviation,label:a.system_abbreviation})),this.businessSystems=this.businessSystems.filter((a,_,c)=>_===c.findIndex(l=>l.value===a.value)))}catch{this.$message.error("获取业务系统列表失败")}},async loadUsersList(){try{const r=await this.$axios.post("/api/get_all_users_real_name");this.usersList=r.data.msg}catch{this.$message.error("用户列表加载失败")}},handleMonitoringRequirementChange(r){r==="是"?(this.formData.monitoring_requirement_description="",this.$nextTick(()=>{this.$refs.addFormRef&&this.$refs.addFormRef.clearValidate("monitoring_requirement_description"),this.$refs.editFormRef&&this.$refs.editFormRef.clearValidate("monitoring_requirement_description")})):r==="否"&&this.$nextTick(()=>{this.$refs.addFormRef&&this.$refs.addFormRef.validateField("monitoring_requirement_description"),this.$refs.editFormRef&&this.$refs.editFormRef.validateField("monitoring_requirement_description")})},validateRelatedMasterSlaveIps(){this.$nextTick(()=>{this.$refs.addFormRef&&(this.isSingleMachine(this.formData.master_slave_role)?this.$refs.addFormRef.clearValidate("related_master_slave_ips"):this.$refs.addFormRef.validateField("related_master_slave_ips")),this.$refs.editFormRef&&(this.isSingleMachine(this.formData.master_slave_role)?this.$refs.editFormRef.clearValidate("related_master_slave_ips"):this.$refs.editFormRef.validateField("related_master_slave_ips"))})},handleAddFromDiscovery(){this.dialogVisible.add=!0;const{ip_address:r,hostname:a,open_ports:_}=this.$route.query;this.formData={management_ip:r||"",hostname:a||"",business_system_name:"",remarks:"",monitoring_requirement:"是",monitoring_requirement_description:"",has_antivirus_software:"否",master_slave_role:""},this.$refs.addFormRef&&this.$nextTick(()=>{this.$refs.addFormRef.resetFields(),this.$refs.addFormRef.clearValidate("related_master_slave_ips"),this.$message.info("请完善应用系统信息并提交")}),this.$router.replace({path:this.$route.path})},async initAutoCompleteOptions(){try{const r=await this.$axios.post("/api/get_cmdb_application_system_info",{currentPage:1,pageSize:1e4,sortProp:"updated_at",sortOrder:"desc"});if(r.data&&r.data.msg){const a=r.data.msg;["management_ip","hostname","function_purpose"].forEach(c=>{const l=[...new Set(a.map(o=>o[c]).filter(o=>o&&o.trim()!==""))];this.autoCompleteOptions[c]=l.map(o=>({value:o}))})}}catch(r){console.error("初始化自动完成选项失败:",r)}}}},Y={class:"user-manage"},Z={class:"dialogdiv"},$={class:"dialog-footer"},ee={class:"dialogdiv"},te={class:"dialog-footer"},ae={class:"button-container"},le={class:"action-bar unified-action-bar"},se={class:"action-bar-left"},re={class:"action-bar-right"},oe={style:{display:"flex","white-space":"nowrap"}},ie={class:"pagination"};function ne(r,a,_,c,l,o){const h=p("el-input"),i=p("el-form-item"),d=p("el-option"),u=p("el-select"),C=p("el-form"),D=p("el-button"),x=p("el-dialog"),R=p("el-alert"),P=p("el-autocomplete"),b=p("el-col"),A=p("Search"),I=p("el-icon"),M=p("el-row"),F=p("el-card"),T=p("Plus"),z=p("Download"),m=p("el-table-column"),k=p("el-tag"),O=p("el-table"),E=p("el-pagination"),L=K("loading");return n(),g("div",Y,[t(x,{modelValue:l.dialogVisible.add,"onUpdate:modelValue":a[11]||(a[11]=e=>l.dialogVisible.add=e),title:"新增应用系统信息",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:s(()=>[q("div",$,[t(D,{onClick:a[10]||(a[10]=e=>l.dialogVisible.add=!1)},{default:s(()=>[f("返回")]),_:1}),t(D,{type:"primary",onClick:o.validateAndSubmitAdd},{default:s(()=>[f("确定")]),_:1},8,["onClick"])])]),default:s(()=>[q("div",Z,[t(C,{model:l.formData,rules:l.rules,ref:"addFormRef","label-position":"right","status-icon":""},{default:s(()=>[t(i,{prop:"management_ip",label:"管理IP:"},{default:s(()=>[t(h,{modelValue:l.formData.management_ip,"onUpdate:modelValue":a[0]||(a[0]=e=>l.formData.management_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),t(i,{prop:"business_system_name",label:"归属业务系统:",required:""},{default:s(()=>[t(u,{modelValue:l.formData.business_system_name,"onUpdate:modelValue":a[1]||(a[1]=e=>l.formData.business_system_name=e),style:{width:"240px"},clearable:"",placeholder:"请选择归属业务系统",filterable:""},{default:s(()=>[(n(!0),g(v,null,V(l.businessSystems,e=>(n(),y(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"monitoring_requirement",label:"需要监控:",required:""},{default:s(()=>[t(u,{modelValue:l.formData.monitoring_requirement,"onUpdate:modelValue":a[2]||(a[2]=e=>l.formData.monitoring_requirement=e),style:{width:"240px"},clearable:"",placeholder:"请选择需要监控",onChange:o.handleMonitoringRequirementChange},{default:s(()=>[t(d,{label:"是",value:"是"}),t(d,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),t(i,{prop:"monitoring_requirement_description",label:"不监控原因:",required:l.formData.monitoring_requirement==="否"},{default:s(()=>[t(h,{modelValue:l.formData.monitoring_requirement_description,"onUpdate:modelValue":a[3]||(a[3]=e=>l.formData.monitoring_requirement_description=e),type:"textarea",rows:3,style:{width:"240px"},clearable:"",placeholder:"请输入不监控原因",disabled:l.formData.monitoring_requirement==="是"},null,8,["modelValue","disabled"])]),_:1},8,["required"]),t(i,{prop:"production_attributes",label:"生产属性:",required:""},{default:s(()=>[t(u,{modelValue:l.formData.production_attributes,"onUpdate:modelValue":a[4]||(a[4]=e=>l.formData.production_attributes=e),style:{width:"240px"},clearable:"",placeholder:"请选择生产属性"},{default:s(()=>[(n(!0),g(v,null,V(l.productionattributes,e=>(n(),y(d,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"master_slave_role",label:"主从角色:",required:""},{default:s(()=>[t(u,{modelValue:l.formData.master_slave_role,"onUpdate:modelValue":a[5]||(a[5]=e=>l.formData.master_slave_role=e),style:{width:"240px"},clearable:"",placeholder:"请选择主从角色",onChange:o.validateRelatedMasterSlaveIps},{default:s(()=>[(n(!0),g(v,null,V(l.masterslaveroles,e=>(n(),y(d,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),t(i,{prop:"related_master_slave_ips",label:"关联主从机IP:",required:o.getSingleMachineRequired()},{default:s(()=>[t(h,{modelValue:l.formData.related_master_slave_ips,"onUpdate:modelValue":a[6]||(a[6]=e=>l.formData.related_master_slave_ips=e),style:{width:"240px"},clearable:"",placeholder:"请输入关联主从机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),t(i,{prop:"internet_ip",label:"互联网IP:"},{default:s(()=>[t(h,{modelValue:l.formData.internet_ip,"onUpdate:modelValue":a[7]||(a[7]=e=>l.formData.internet_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入互联网IP"},null,8,["modelValue"])]),_:1}),t(i,{prop:"internet_port",label:"互联网端口:"},{default:s(()=>[t(h,{modelValue:l.formData.internet_port,"onUpdate:modelValue":a[8]||(a[8]=e=>l.formData.internet_port=e),style:{width:"240px"},clearable:"",placeholder:"请输入互联网端口"},null,8,["modelValue"])]),_:1}),t(i,{prop:"remarks",label:"离线备注:"},{default:s(()=>[t(h,{modelValue:l.formData.remarks,"onUpdate:modelValue":a[9]||(a[9]=e=>l.formData.remarks=e),style:{width:"240px"},type:"textarea",clearable:"",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),t(x,{modelValue:l.dialogVisible.edit,"onUpdate:modelValue":a[23]||(a[23]=e=>l.dialogVisible.edit=e),title:"编辑应用系统信息",width:"400","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:s(()=>[q("div",te,[t(D,{onClick:a[22]||(a[22]=e=>l.dialogVisible.edit=!1)},{default:s(()=>[f("取消")]),_:1}),t(D,{type:"primary",onClick:o.validateAndSubmitEdit},{default:s(()=>[f("更新")]),_:1},8,["onClick"])])]),default:s(()=>[q("div",ee,[t(C,{model:l.formData,rules:l.rules,ref:"editFormRef","label-position":"right","status-icon":""},{default:s(()=>[t(i,{prop:"management_ip",label:"管理IP:"},{default:s(()=>[t(h,{modelValue:l.formData.management_ip,"onUpdate:modelValue":a[12]||(a[12]=e=>l.formData.management_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入管理IP"},null,8,["modelValue"])]),_:1}),t(i,{prop:"business_system_name",label:"归属业务系统:",required:""},{default:s(()=>[t(u,{modelValue:l.formData.business_system_name,"onUpdate:modelValue":a[13]||(a[13]=e=>l.formData.business_system_name=e),style:{width:"240px"},clearable:"",placeholder:"请选择归属业务系统",filterable:""},{default:s(()=>[(n(!0),g(v,null,V(l.businessSystems,e=>(n(),y(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"monitoring_requirement",label:"需要监控:",required:""},{default:s(()=>[t(u,{modelValue:l.formData.monitoring_requirement,"onUpdate:modelValue":a[14]||(a[14]=e=>l.formData.monitoring_requirement=e),style:{width:"240px"},clearable:"",placeholder:"请选择需要监控",onChange:o.handleMonitoringRequirementChange},{default:s(()=>[t(d,{label:"是",value:"是"}),t(d,{label:"否",value:"否"})]),_:1},8,["modelValue","onChange"])]),_:1}),t(i,{prop:"monitoring_requirement_description",label:"不监控原因:",required:l.formData.monitoring_requirement==="否"},{default:s(()=>[t(h,{modelValue:l.formData.monitoring_requirement_description,"onUpdate:modelValue":a[15]||(a[15]=e=>l.formData.monitoring_requirement_description=e),type:"textarea",rows:3,style:{width:"240px"},clearable:"",placeholder:"请输入不监控原因",disabled:l.formData.monitoring_requirement==="是"},null,8,["modelValue","disabled"])]),_:1},8,["required"]),t(i,{prop:"production_attributes",label:"生产属性:",required:""},{default:s(()=>[t(u,{modelValue:l.formData.production_attributes,"onUpdate:modelValue":a[16]||(a[16]=e=>l.formData.production_attributes=e),style:{width:"240px"},clearable:"",placeholder:"请选择生产属性"},{default:s(()=>[(n(!0),g(v,null,V(l.productionattributes,e=>(n(),y(d,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{prop:"master_slave_role",label:"主从角色:",required:""},{default:s(()=>[t(u,{modelValue:l.formData.master_slave_role,"onUpdate:modelValue":a[17]||(a[17]=e=>l.formData.master_slave_role=e),style:{width:"240px"},clearable:"",placeholder:"请选择主从角色",onChange:o.validateRelatedMasterSlaveIps},{default:s(()=>[(n(!0),g(v,null,V(l.masterslaveroles,e=>(n(),y(d,{key:e.dict_code,label:e.dict_name,value:e.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),t(i,{prop:"related_master_slave_ips",label:"关联主从机IP:",required:o.getSingleMachineRequired()},{default:s(()=>[t(h,{modelValue:l.formData.related_master_slave_ips,"onUpdate:modelValue":a[18]||(a[18]=e=>l.formData.related_master_slave_ips=e),style:{width:"240px"},clearable:"",placeholder:"请输入关联主从机IP，多个IP用英文逗号分隔"},null,8,["modelValue"])]),_:1},8,["required"]),t(i,{prop:"internet_ip",label:"互联网IP:"},{default:s(()=>[t(h,{modelValue:l.formData.internet_ip,"onUpdate:modelValue":a[19]||(a[19]=e=>l.formData.internet_ip=e),style:{width:"240px"},clearable:"",placeholder:"请输入互联网IP"},null,8,["modelValue"])]),_:1}),t(i,{prop:"internet_port",label:"互联网端口:"},{default:s(()=>[t(h,{modelValue:l.formData.internet_port,"onUpdate:modelValue":a[20]||(a[20]=e=>l.formData.internet_port=e),style:{width:"240px"},clearable:"",placeholder:"请输入互联网端口"},null,8,["modelValue"])]),_:1}),t(i,{prop:"remarks",label:"离线备注:"},{default:s(()=>[t(h,{modelValue:l.formData.remarks,"onUpdate:modelValue":a[21]||(a[21]=e=>l.formData.remarks=e),style:{width:"240px"},type:"textarea",clearable:"",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),t(x,{modelValue:l.dialogVisible.delete,"onUpdate:modelValue":a[25]||(a[25]=e=>l.dialogVisible.delete=e),title:"删除管理IP",width:"500","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:s(()=>[q("div",null,[t(D,{onClick:a[24]||(a[24]=e=>l.dialogVisible.delete=!1)},{default:s(()=>[f("取消")]),_:1}),t(D,{type:"danger",onClick:o.submitDelete},{default:s(()=>[f("确认删除")]),_:1},8,["onClick"])])]),default:s(()=>[t(R,{type:"warning",title:`确定要删除 IP 为 ${l.formData.management_ip} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),t(F,{class:"search-card"},{default:s(()=>[t(C,{inline:!0},{default:s(()=>[t(M,{gutter:10},{default:s(()=>[t(b,{span:6},{default:s(()=>[t(i,{label:"管理IP"},{default:s(()=>[t(P,{modelValue:l.search.management_ip,"onUpdate:modelValue":a[26]||(a[26]=e=>l.search.management_ip=e),"fetch-suggestions":(e,S)=>o.querySearchAsync(e,S,"management_ip"),placeholder:"请输入管理IP",clearable:"",class:"form-control",onSelect:o.handleSelect},null,8,["modelValue","fetch-suggestions","onSelect"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"主机名"},{default:s(()=>[t(P,{modelValue:l.search.hostname,"onUpdate:modelValue":a[27]||(a[27]=e=>l.search.hostname=e),"fetch-suggestions":(e,S)=>o.querySearchAsync(e,S,"hostname"),placeholder:"请输入主机名",clearable:"",class:"form-control",onSelect:o.handleSelect},null,8,["modelValue","fetch-suggestions","onSelect"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"功能用途"},{default:s(()=>[t(P,{modelValue:l.search.function_purpose,"onUpdate:modelValue":a[28]||(a[28]=e=>l.search.function_purpose=e),"fetch-suggestions":(e,S)=>o.querySearchAsync(e,S,"function_purpose"),placeholder:"请输入功能用途",clearable:"",class:"form-control",onSelect:o.handleSelect},null,8,["modelValue","fetch-suggestions","onSelect"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"设备管理员"},{default:s(()=>[t(u,{modelValue:l.search.server_admin1,"onUpdate:modelValue":a[29]||(a[29]=e=>l.search.server_admin1=e),placeholder:"请选择设备管理员",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[(n(!0),g(v,null,V(l.usersList,e=>(n(),y(d,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"系统管理员"},{default:s(()=>[t(u,{modelValue:l.search.system_administrator,"onUpdate:modelValue":a[30]||(a[30]=e=>l.search.system_administrator=e),placeholder:"请选择系统管理员",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[(n(!0),g(v,null,V(l.usersList,e=>(n(),y(d,{key:e.id,label:e.real_name,value:e.real_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"设备生命周期"},{default:s(()=>[t(u,{modelValue:l.search.operation_status,"onUpdate:modelValue":a[31]||(a[31]=e=>l.search.operation_status=e),placeholder:"请选择设备生命周期",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[(n(!0),g(v,null,V(l.operationstatuses,e=>(n(),y(d,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"系统运行状态"},{default:s(()=>[t(u,{modelValue:l.search.system_operation_status,"onUpdate:modelValue":a[32]||(a[32]=e=>l.search.system_operation_status=e),placeholder:"请选择系统运行状态",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[(n(!0),g(v,null,V(l.systemoperationstatuses,e=>(n(),y(d,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"PING状态"},{default:s(()=>[t(u,{modelValue:l.search.machine_usage_status,"onUpdate:modelValue":a[33]||(a[33]=e=>l.search.machine_usage_status=e),placeholder:"请选择PING状态",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[t(d,{label:"在线",value:"在线"}),t(d,{label:"离线",value:"离线"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"所属机房"},{default:s(()=>[t(u,{modelValue:l.search.data_center,"onUpdate:modelValue":a[34]||(a[34]=e=>l.search.data_center=e),clearable:"",filterable:"",placeholder:"请选择所属机房",class:"form-control"},{default:s(()=>[(n(!0),g(v,null,V(l.datacenters,e=>(n(),y(d,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"归属业务系统"},{default:s(()=>[t(u,{modelValue:l.search.business_system_name,"onUpdate:modelValue":a[35]||(a[35]=e=>l.search.business_system_name=e),placeholder:"请选择归属业务系统",filterable:"",clearable:"",class:"form-control"},{default:s(()=>[(n(!0),g(v,null,V(l.businessSystems,e=>(n(),y(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"系统分级"},{default:s(()=>[t(u,{modelValue:l.search.system_classification,"onUpdate:modelValue":a[36]||(a[36]=e=>l.search.system_classification=e),placeholder:"请选择系统分级",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[(n(!0),g(v,null,V(l.systemclassifications,e=>(n(),y(d,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"需要监控"},{default:s(()=>[t(u,{modelValue:l.search.monitoring_requirement,"onUpdate:modelValue":a[37]||(a[37]=e=>l.search.monitoring_requirement=e),placeholder:"请选择需要监控",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[t(d,{label:"是",value:"是"}),t(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"监控状态"},{default:s(()=>[t(u,{modelValue:l.search.is_monitored,"onUpdate:modelValue":a[38]||(a[38]=e=>l.search.is_monitored=e),placeholder:"请选择监控状态",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[t(d,{label:"是",value:"是"}),t(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"生产属性"},{default:s(()=>[t(u,{modelValue:l.search.production_attributes,"onUpdate:modelValue":a[39]||(a[39]=e=>l.search.production_attributes=e),placeholder:"请选择生产属性",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[(n(!0),g(v,null,V(l.productionattributes,e=>(n(),y(d,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"操作系统"},{default:s(()=>[t(u,{modelValue:l.search.operating_system,"onUpdate:modelValue":a[40]||(a[40]=e=>l.search.operating_system=e),placeholder:"请选择操作系统",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[(n(!0),g(v,null,V(l.operatingsystems,e=>(n(),y(d,{key:e.dict_code,label:e.dict_name,value:e.dict_name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:6},{default:s(()=>[t(i,{label:"是否虚拟机"},{default:s(()=>[t(u,{modelValue:l.search.is_virtual_machine,"onUpdate:modelValue":a[41]||(a[41]=e=>l.search.is_virtual_machine=e),placeholder:"请选择是否虚拟机",clearable:"",filterable:"",class:"form-control"},{default:s(()=>[t(d,{label:"是",value:"是"}),t(d,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:24,class:"search-buttons-col"},{default:s(()=>[t(i,{label:" ",class:"form-item-with-label search-buttons"},{default:s(()=>[q("div",ae,[t(D,{type:"primary",onClick:o.loadData},{default:s(()=>[t(I,null,{default:s(()=>[t(A)]),_:1}),f("查询 ")]),_:1},8,["onClick"]),t(D,{onClick:o.resetSearch},{default:s(()=>[f("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),q("div",le,[q("div",se,[t(D,{type:"success",disabled:!l.hasInsertPermission,onClick:o.handleAdd},{default:s(()=>[t(I,null,{default:s(()=>[t(T)]),_:1}),f("新增资产 ")]),_:1},8,["disabled","onClick"])]),q("div",re,[t(D,{type:"info",onClick:o.exportData},{default:s(()=>[t(I,null,{default:s(()=>[t(z)]),_:1}),f(" 导出数据 ")]),_:1},8,["onClick"])])]),t(F,{class:"table-card"},{default:s(()=>[H((n(),y(O,{data:l.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:o.handleSortChange},{default:s(()=>[J("",!0),t(m,{prop:"management_ip",label:"管理IP",sortable:""}),t(m,{prop:"hostname",label:"主机名",sortable:""}),t(m,{prop:"function_purpose",label:"功能用途",sortable:""}),t(m,{prop:"server_admin1",label:"设备管理员",sortable:""}),t(m,{prop:"system_administrator",label:"系统管理员",sortable:""}),t(m,{prop:"operation_status",label:"设备生命周期",sortable:""},{default:s(e=>[t(k,{type:o.getLifecycleTagType(e.row.operation_status)},{default:s(()=>[f(w(e.row.operation_status),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"system_operation_status",label:"系统运行状态",sortable:""},{default:s(e=>[t(k,{type:o.getSystemOperationStatusTagType(e.row.system_operation_status)},{default:s(()=>[f(w(e.row.system_operation_status),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"machine_usage_status",label:"PING状态",sortable:""},{default:s(e=>[t(k,{type:e.row.machine_usage_status==="在线"?"success":"danger"},{default:s(()=>[f(w(e.row.machine_usage_status),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"remarks",label:"离线备注",sortable:""}),t(m,{prop:"data_center",label:"所属机房",sortable:""}),t(m,{prop:"business_system_name",label:"归属业务系统",sortable:""}),t(m,{prop:"system_classification",label:"系统分级",sortable:""}),t(m,{prop:"monitoring_requirement",label:"需要监控",sortable:""},{default:s(e=>[t(k,{type:e.row.monitoring_requirement==="是"?"success":"warning"},{default:s(()=>[f(w(e.row.monitoring_requirement),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"monitoring_requirement_description",label:"不监控原因",sortable:""}),t(m,{prop:"is_monitored",label:"监控状态",sortable:""},{default:s(e=>[t(k,{type:e.row.is_monitored==="是"?"success":"danger"},{default:s(()=>[f(w(e.row.is_monitored),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"production_attributes",label:"生产属性",sortable:""}),t(m,{prop:"master_slave_role",label:"主从角色",sortable:""}),t(m,{prop:"related_master_slave_ips",label:"关联主从机IP",sortable:""}),t(m,{prop:"internet_ip",label:"互联网IP",sortable:""}),t(m,{prop:"internet_port",label:"互联网端口",sortable:""}),t(m,{prop:"operating_system",label:"操作系统",sortable:""}),t(m,{prop:"is_virtual_machine",label:"是否虚拟机",sortable:""},{default:s(e=>[t(k,{type:e.row.is_virtual_machine==="是"?"success":"info"},{default:s(()=>[f(w(e.row.is_virtual_machine),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"created_at",label:"创建时间",sortable:""}),t(m,{prop:"created_by",label:"创建人",sortable:""}),t(m,{prop:"updated_at",label:"更新时间",sortable:""}),t(m,{prop:"updated_by",label:"更新人",sortable:""}),t(m,{label:"操作",align:"center",fixed:"right"},{default:s(e=>[q("div",oe,[t(D,{size:"small",type:"warning",disabled:!l.hasUpdatePermission,onClick:S=>o.handleEdit(e.$index,e.row)},{default:s(()=>[f("编辑")]),_:2},1032,["disabled","onClick"]),t(D,{size:"small",type:"danger",disabled:!l.hasDeletePermission,onClick:S=>o.handleDelete(e.$index,e.row)},{default:s(()=>[f("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[L,l.loading]]),q("div",ie,[t(E,{background:"","current-page":l.search.currentPage,"page-size":l.search.pageSize,total:l.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:o.handlePageSizeChange,onCurrentChange:o.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const _e=B(X,[["render",ne],["__scopeId","data-v-fad055e0"]]);export{_e as default};
