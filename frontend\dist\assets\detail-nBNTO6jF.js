const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./request-DGO27LS-.js","./index-MGgq8mV5.js","./index-BDzhwb3b.css"])))=>i.map(i=>d[i]);
import{_ as ve,L as Ve,A as Ne,ab as Ae,ac as Re,r as V,S as pe,a0 as ia,o as ye,ad as qe,m as O,c as E,x as A,f as d,t as J,a as t,w as l,b as x,h as S,a5 as de,ae as Y,af as ca,p as we,q as xe,e as g,k as da,M as ua,G as ga,O as We,ag as ma,ah as fa,R as oe,F as le,l as se,n as ce,C as ha,D as _a,E as _,Q as Be,ai as pa,Z as va,X as ya,aj as wa,Y as xa}from"./index-MGgq8mV5.js";import{g as Oe,s as re}from"./request-DGO27LS-.js";import{X as ba,v as Da,r as ka,u as me}from"./xlsx-DH6WiNtO.js";import{s as Ca}from"./dateUtils-BmBarIng.js";const Sa={name:"SimpleImageViewer",components:{ZoomIn:Re,ZoomOut:Ae,Download:Ne,Close:Ve},props:{visible:{type:Boolean,default:!1},imageUrl:{type:String,required:!0},fileName:{type:String,default:"图片"}},emits:["update:visible","close","download"],setup(r,{emit:a}){const n=V(1),e=V(0),h=V(0),v=V(!1),m=V({x:0,y:0}),c=V({x:0,y:0}),o=pe(()=>({transform:`scale(${n.value}) translate(${e.value}px, ${h.value}px)`,transition:v.value?"none":"transform 0.3s ease",cursor:v.value?"grabbing":"grab",maxWidth:"none",maxHeight:"none",userSelect:"none"})),i=()=>{n.value<3&&(n.value=Math.min(n.value*1.2,3))},k=()=>{n.value>.5&&(n.value=Math.max(n.value/1.2,.5))},L=()=>{n.value=1,e.value=0,h.value=0},q=M=>{M.preventDefault();const w=1+(M.deltaY>0?-1:1)*.1,H=Math.max(.5,Math.min(3,n.value*w));H!==n.value&&(n.value=H)},F=M=>{M.button===0&&(M.preventDefault(),v.value=!0,m.value={x:M.clientX,y:M.clientY},c.value={x:e.value,y:h.value},document.addEventListener("mousemove",N),document.addEventListener("mouseup",z))},N=M=>{if(!v.value)return;const G=M.clientX-m.value.x,w=M.clientY-m.value.y;e.value=c.value.x+G/n.value,h.value=c.value.y+w/n.value},z=()=>{v.value=!1,document.removeEventListener("mousemove",N),document.removeEventListener("mouseup",z)},B=M=>{M.target===M.currentTarget&&P()},P=()=>{a("update:visible",!1),a("close"),L()},X=M=>{if(r.visible)switch(M.key){case"Escape":P();break;case"+":case"=":i();break;case"-":k();break;case"0":L();break}};return ia(()=>r.visible,M=>{M&&L()}),ye(()=>{document.addEventListener("keydown",X)}),qe(()=>{document.removeEventListener("keydown",X),document.removeEventListener("mousemove",N),document.removeEventListener("mouseup",z)}),{zoom:n,imageStyle:o,zoomIn:i,zoomOut:k,resetZoom:L,handleWheel:q,handleMouseDown:F,handleBackgroundClick:B,close:P}}},La=r=>(we("data-v-6e408141"),r=r(),xe(),r),Ea={class:"viewer-toolbar"},Ia={class:"toolbar-left"},za={class:"image-title"},Ma={class:"toolbar-right"},Ta=["src","alt"],Oa=La(()=>d("div",{class:"viewer-tips"},[d("span",null,"滚轮缩放 | 拖拽移动 | ESC键关闭")],-1));function Fa(r,a,n,e,h,v){const m=x("ZoomOut"),c=x("el-icon"),o=x("el-button"),i=x("ZoomIn"),k=x("el-button-group"),L=x("Download"),q=x("Close");return g(),O(ca,{to:"body"},[n.visible?(g(),E("div",{key:0,class:"simple-image-viewer",onClick:a[4]||(a[4]=(...F)=>e.handleBackgroundClick&&e.handleBackgroundClick(...F))},[d("div",Ea,[d("div",Ia,[d("span",za,J(n.fileName),1)]),d("div",Ma,[t(k,{size:"small"},{default:l(()=>[t(o,{onClick:e.zoomOut,disabled:e.zoom<=.5},{default:l(()=>[t(c,null,{default:l(()=>[t(m)]),_:1})]),_:1},8,["onClick","disabled"]),t(o,{onClick:e.resetZoom},{default:l(()=>[S(J(Math.round(e.zoom*100))+"% ",1)]),_:1},8,["onClick"]),t(o,{onClick:e.zoomIn,disabled:e.zoom>=3},{default:l(()=>[t(c,null,{default:l(()=>[t(i)]),_:1})]),_:1},8,["onClick","disabled"])]),_:1}),t(o,{size:"small",onClick:a[0]||(a[0]=F=>r.$emit("download")),style:{"margin-left":"10px"}},{default:l(()=>[t(c,null,{default:l(()=>[t(L)]),_:1}),S(" 下载 ")]),_:1}),t(o,{size:"small",onClick:e.close,style:{"margin-left":"10px"}},{default:l(()=>[t(c,null,{default:l(()=>[t(q)]),_:1}),S(" 关闭 ")]),_:1},8,["onClick"])])]),d("div",{class:"viewer-content",onClick:a[3]||(a[3]=Y(()=>{},["stop"]))},[d("img",{src:n.imageUrl,alt:n.fileName,style:de(e.imageStyle),onWheel:a[1]||(a[1]=(...F)=>e.handleWheel&&e.handleWheel(...F)),onMousedown:a[2]||(a[2]=(...F)=>e.handleMouseDown&&e.handleMouseDown(...F)),draggable:"false"},null,44,Ta)]),Oa])):A("",!0)])}const Ua=ve(Sa,[["render",Fa],["__scopeId","data-v-6e408141"]]),Va=async(r,a)=>{try{if(console.log("🚀 开始Excel预览处理:",{containerId:a,arrayBufferSize:r.byteLength,timestamp:new Date().toISOString()}),typeof ba>"u")throw console.error("❌ XLSX库未加载"),new Error("XLSX库未加载，请检查依赖");console.log("✅ XLSX库版本:",Da);const n=ka(r,{type:"array"});console.log("✅ Excel工作簿解析成功:",{sheetNames:n.SheetNames,sheetCount:n.SheetNames.length});const e=document.getElementById(a);if(!e){console.error("❌ 预览容器未找到:",a);const c=document.querySelectorAll('[id*="excel"]');throw console.log("🔍 找到的Excel相关容器:",Array.from(c).map(o=>({id:o.id,className:o.className}))),new Error(`预览容器未找到: ${a}`)}if(console.log("✅ 找到预览容器:",{id:e.id,className:e.className,offsetWidth:e.offsetWidth,offsetHeight:e.offsetHeight,isVisible:e.offsetParent!==null}),e.innerHTML="",!n.SheetNames||n.SheetNames.length===0)return e.innerHTML=`
        <div style="text-align: center; padding: 40px; color: #f56c6c;">
          <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
          <h3>Excel文件解析失败</h3>
          <p>未找到有效的工作表</p>
        </div>
      `,{success:!1,message:"未找到有效的工作表"};const h=document.createElement("div");h.style.cssText=`
      height: 100%;
      display: flex;
      flex-direction: column;
      background: #fff;
    `;let v=null;n.SheetNames.length>1&&(v=document.createElement("div"),v.className="excel-tabs",v.style.cssText=`
        border-bottom: 1px solid #dcdfe6;
        margin-bottom: 10px;
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        padding: 10px;
        background: #f8f9fa;
      `,h.appendChild(v));const m=document.createElement("div");return m.className="excel-content",m.style.cssText=`
      flex: 1;
      overflow: auto;
      padding: 10px;
    `,h.appendChild(m),e.appendChild(h),n.SheetNames.length>1&&v&&n.SheetNames.forEach((c,o)=>{const i=document.createElement("button");i.textContent=c,i.className=`excel-tab ${o===0?"active":""}`,i.style.cssText=`
          padding: 8px 16px;
          border: 1px solid #dcdfe6;
          background: ${o===0?"#409eff":"#fff"};
          color: ${o===0?"#fff":"#606266"};
          cursor: pointer;
          border-radius: 4px;
          font-size: 12px;
          transition: all 0.3s;
          margin-right: 5px;
        `,i.addEventListener("mouseenter",()=>{i.classList.contains("active")||(i.style.background="#ecf5ff",i.style.borderColor="#409eff")}),i.addEventListener("mouseleave",()=>{i.classList.contains("active")||(i.style.background="#fff",i.style.borderColor="#dcdfe6")}),i.addEventListener("click",()=>{console.log("切换到工作表:",c),document.querySelectorAll(".excel-tab").forEach(k=>{k.style.background="#fff",k.style.color="#606266",k.style.borderColor="#dcdfe6",k.classList.remove("active")}),i.style.background="#409eff",i.style.color="#fff",i.style.borderColor="#409eff",i.classList.add("active"),Fe(n.Sheets[c],m,c)}),v.appendChild(i)}),n.SheetNames.length>0&&(console.log("显示默认工作表:",n.SheetNames[0]),Fe(n.Sheets[n.SheetNames[0]],m,n.SheetNames[0])),console.log("Excel预览处理完成"),{success:!0,sheetCount:n.SheetNames.length,sheetNames:n.SheetNames}}catch(n){console.error("Excel预览失败:",n);const e=document.getElementById(a);throw e&&(e.innerHTML=`
        <div style="text-align: center; padding: 40px; color: #f56c6c;">
          <div style="font-size: 48px; margin-bottom: 20px;">❌</div>
          <h3>Excel预览失败</h3>
          <p>${n.message}</p>
          <div style="margin-top: 20px; padding: 15px; background: #fef0f0; border-radius: 4px; text-align: left;">
            <h4 style="margin-bottom: 10px; color: #f56c6c;">错误详情：</h4>
            <pre style="font-size: 12px; color: #909399; white-space: pre-wrap;">${n.stack||n.message}</pre>
          </div>
        </div>
      `),n}};function Fe(r,a,n=""){try{if(console.log("显示工作表内容:",{sheetName:n,worksheet:r}),!r||Object.keys(r).length===0){he(a,n,"工作表为空");return}const e=Object.keys(r).filter(i=>i.match(/^[A-Z]+[0-9]+$/)&&r[i]&&r[i].v!==void 0);if(e.length===0){he(a,n,"工作表没有数据");return}console.log("找到数据单元格:",e.length,"个");let h=null,v=r["!ref"];try{v&&typeof v=="string"&&v.trim()?(h=me.decode_range(v),console.log("工作表范围解析成功:",v,h)):(console.log("工作表缺少范围引用，尝试计算范围"),h=Ue(e),console.log("计算得到的范围:",h))}catch(i){console.warn("范围解析失败:",i.message),h=Ue(e),console.log("使用计算范围:",h)}if(!h||h.e.r<h.s.r||h.e.c<h.s.c){he(a,n,"工作表范围无效");return}let m="";try{m=me.sheet_to_html(r,{id:`excel-table-${Date.now()}`,editable:!1,header:1}),console.log("HTML表格生成成功，长度:",m.length)}catch(i){console.warn("标准HTML转换失败，尝试备用方法:",i.message);try{m=Wa(r,h),console.log("手动构建表格成功，长度:",m.length)}catch(k){console.error("手动构建表格也失败:",k.message),qa(a,n,"表格生成失败",k.message);return}}if(!m||m.trim().length===0){he(a,n,"表格内容为空");return}const c=`
      <style>
        .excel-table, .excel-table-manual {
          border-collapse: collapse;
          width: auto;
          min-width: 100%;
          font-size: 13px;
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          background: #fff;
          table-layout: auto;
        }
        .excel-table td, .excel-table th,
        .excel-table-manual td, .excel-table-manual th {
          border: 1px solid #dcdfe6;
          padding: 8px 12px;
          text-align: left;
          white-space: pre-wrap;
          word-wrap: break-word;
          min-width: 80px;
          max-width: none;
          width: auto;
          overflow: visible;
          vertical-align: top;
          line-height: 1.4;
        }
        .excel-table th, .excel-table-manual th {
          background-color: #f5f7fa;
          font-weight: bold;
          position: sticky;
          top: 0;
          z-index: 10;
        }
        .excel-table tr:nth-child(even),
        .excel-table-manual tr:nth-child(even) {
          background-color: #fafafa;
        }
        .excel-table tr:hover, .excel-table-manual tr:hover {
          background-color: #ecf5ff;
        }
        .excel-table td:hover, .excel-table-manual td:hover {
          background-color: #e6f7ff;
          cursor: pointer;
        }
      </style>
      <div style="overflow: auto; max-height: 100%; border: 1px solid #e4e7ed; border-radius: 4px;">
        ${m.replace(/id="[^"]*"/g,'class="excel-table"')}
      </div>
    `;a.innerHTML=c;const o=a.querySelector(".excel-table, .excel-table-manual");o&&(Ba(o,r,h),o.addEventListener("click",i=>{const k=i.target.closest("td");if(k&&k.textContent.trim()){const L=document.createElement("div");L.style.cssText=`
            position: fixed;
            background: #303133;
            color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            max-width: 300px;
            word-wrap: break-word;
            z-index: 9999;
            pointer-events: none;
          `,L.textContent=k.textContent,document.body.appendChild(L);const q=k.getBoundingClientRect();L.style.left=`${q.left}px`,L.style.top=`${q.bottom+5}px`,setTimeout(()=>{L.parentNode&&L.parentNode.removeChild(L)},3e3)}})),console.log("工作表内容显示完成")}catch(e){console.error("显示工作表内容失败:",e),a.innerHTML=`
      <div style="text-align: center; padding: 40px; color: #f56c6c;">
        <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
        <h3>工作表显示失败</h3>
        <p style="margin-bottom: 10px;">工作表 "${n}" 解析出错</p>
        <p style="font-size: 12px; color: #909399;">${e.message}</p>
        <div style="margin-top: 20px; padding: 15px; background: #fef0f0; border-radius: 4px; text-align: left;">
          <h4 style="margin-bottom: 10px; color: #f56c6c;">错误详情：</h4>
          <pre style="font-size: 11px; color: #909399; white-space: pre-wrap; max-height: 100px; overflow: auto;">${e.stack||e.message}</pre>
        </div>
      </div>
    `}}const Na=async(r,a)=>{try{const n=document.getElementById(a);if(!n)throw new Error("预览容器未找到");return n.innerHTML=`
      <div style="text-align: center; padding: 40px; color: #909399;">
        <div style="font-size: 48px; margin-bottom: 20px;">📄</div>
        <h3 style="margin-bottom: 10px; color: #606266;">Word文档预览</h3>
        <p style="margin-bottom: 20px;">纯前端Word预览功能正在开发中</p>
        <p style="font-size: 14px; color: #909399;">
          建议使用LibreOffice Online或下载文件查看完整内容
        </p>
        <div style="margin-top: 30px; padding: 15px; background: #f0f9ff; border-radius: 4px; text-align: left;">
          <h4 style="margin-bottom: 10px; color: #409eff;">💡 替代方案：</h4>
          <ul style="margin: 0; padding-left: 20px; color: #606266;">
            <li>使用"LibreOffice Online"预览方案</li>
            <li>使用"ONLYOFFICE"预览方案</li>
            <li>下载文件后使用本地Office软件查看</li>
          </ul>
        </div>
      </div>
    `,{success:!0,message:"Word文档预览功能开发中"}}catch(n){throw console.error("Word预览失败:",n),n}},Aa=async r=>{try{const a=await fetch(r);if(!a.ok)throw new Error(`文件下载失败: ${a.status}`);return await a.arrayBuffer()}catch(a){throw console.error("获取文件数据失败:",a),a}},Ra=r=>[".xlsx",".xls"].includes(r.toLowerCase());function he(r,a,n){r.innerHTML=`
    <div style="text-align: center; padding: 40px; color: #909399;">
      <div style="font-size: 48px; margin-bottom: 20px;">📋</div>
      <h3>${n}</h3>
      <p>工作表 "${a}" 没有可显示的内容</p>
      <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; color: #606266;">
        <p style="margin: 0; font-size: 14px;">💡 这可能是因为：</p>
        <ul style="margin: 10px 0 0 0; padding-left: 20px; text-align: left; display: inline-block;">
          <li>工作表确实为空</li>
          <li>数据格式特殊无法解析</li>
          <li>包含仅格式化信息</li>
        </ul>
      </div>
    </div>
  `}function qa(r,a,n,e){r.innerHTML=`
    <div style="text-align: center; padding: 40px; color: #f56c6c;">
      <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
      <h3>${n}</h3>
      <p style="margin-bottom: 10px;">工作表 "${a}" 解析出错</p>
      <div style="margin-top: 20px; padding: 15px; background: #fef0f0; border-radius: 4px; text-align: left;">
        <h4 style="margin-bottom: 10px; color: #f56c6c;">错误信息：</h4>
        <p style="font-size: 12px; color: #909399; word-wrap: break-word;">${e}</p>
        <div style="margin-top: 15px; padding: 10px; background: #fff; border-radius: 4px; border: 1px solid #f5c6cb;">
          <p style="margin: 0; font-size: 14px; color: #721c24;">💡 建议解决方案：</p>
          <ul style="margin: 10px 0 0 0; padding-left: 20px; color: #721c24;">
            <li>尝试重新保存Excel文件</li>
            <li>检查文件是否损坏</li>
            <li>使用其他预览方案</li>
            <li>直接下载文件查看</li>
          </ul>
        </div>
      </div>
    </div>
  `}function Ue(r){if(!r||r.length===0)return{s:{r:0,c:0},e:{r:0,c:0}};let a=1/0,n=-1,e=1/0,h=-1;return r.forEach(v=>{try{const m=me.decode_cell(v);a=Math.min(a,m.r),n=Math.max(n,m.r),e=Math.min(e,m.c),h=Math.max(h,m.c)}catch{console.warn("无法解析单元格引用:",v)}}),a===1/0||n===-1||e===1/0||h===-1?{s:{r:0,c:0},e:{r:0,c:0}}:{s:{r:a,c:e},e:{r:n,c:h}}}function Wa(r,a){try{console.log("开始手动构建表格，范围:",a);let n='<table class="excel-table-manual">';for(let e=a.s.r;e<=a.e.r;e++){n+="<tr>";for(let h=a.s.c;h<=a.e.c;h++){const v=me.encode_cell({r:e,c:h}),m=r[v];let c="";m&&(m.v!==void 0?c=String(m.v):m.w!==void 0?c=String(m.w):m.t==="s"&&m.r&&(c=m.r)),c=c.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;");const o=e===a.s.r?"th":"td";n+=`<${o}>${c||""}</${o}>`}n+="</tr>"}return n+="</table>",console.log("手动构建表格完成"),n}catch(n){throw console.error("手动构建表格失败:",n),new Error(`手动构建表格失败: ${n.message}`)}}function Ba(r,a,n){try{if(console.log("开始智能调整列宽"),!r||!a||!n){console.warn("调整列宽参数不完整");return}const e=r.querySelectorAll("tr");if(e.length===0){console.warn("表格没有行数据");return}const h=n.e.c-n.s.c+1,v=new Array(h).fill(0);for(let m=n.s.c;m<=n.e.c;m++){let c=80;const o=m-n.s.c;for(let i=n.s.r;i<=n.e.r;i++){const k=me.encode_cell({r:i,c:m}),L=a[k];if(L){let q="";L.v!==void 0?q=String(L.v):L.w!==void 0?q=String(L.w):L.t==="s"&&L.r&&(q=L.r);const F=q.split(/\r?\n/);let N=0;F.forEach(z=>{let B=0;for(let P=0;P<z.length;P++){const X=z.charAt(P);X.match(/[\u4e00-\u9fa5]/)?B+=14:X.match(/[A-Z]/)?B+=9:B+=8}N=Math.max(N,B)}),N+=24,c=Math.max(c,N)}}c=Math.min(c,500),v[o]=c}console.log("计算得到的列宽:",v),e.forEach((m,c)=>{m.querySelectorAll("td, th").forEach((i,k)=>{k<v.length&&(i.style.minWidth=`${Math.max(80,v[k])}px`,i.style.width="auto",i.style.maxWidth="none",i.style.whiteSpace="pre-wrap",i.style.wordWrap="break-word",i.style.overflow="visible")})}),r.style.width="auto",r.style.minWidth="100%",r.style.tableLayout="auto",console.log("列宽调整完成")}catch(e){console.error("调整列宽失败:",e),r.querySelectorAll("tr").forEach(v=>{v.querySelectorAll("td, th").forEach(c=>{c.style.width="auto",c.style.minWidth="80px",c.style.maxWidth="none",c.style.whiteSpace="pre-wrap",c.style.wordWrap="break-word",c.style.overflow="visible"})})}}function Pa(){const r=V(!1),a=V(""),n=V(""),e=V(1),h=V(!1),v=V({x:0,y:0}),m=V({x:0,y:0}),c=V({x:0,y:0}),o=w=>{w&&w.previewUrl&&[".jpg",".jpeg",".png",".gif"].includes(w.fileType)&&(a.value=w.previewUrl,n.value=w.title.replace("文件预览 - ",""),r.value=!0)},i=()=>{r.value=!1,a.value="",n.value="",e.value=1,h.value=!1,m.value={x:0,y:0},c.value={x:0,y:0}},k=()=>{e.value<3&&(e.value=Math.min(e.value*1.2,3))},L=()=>{e.value>.5&&(e.value=Math.max(e.value/1.2,.5))},q=()=>{e.value=1,m.value={x:0,y:0},c.value={x:0,y:0}},F=w=>{w.preventDefault(),h.value=!0,v.value={x:w.clientX-m.value.x,y:w.clientY-m.value.y},document.addEventListener("mousemove",N),document.addEventListener("mouseup",z)},N=w=>{if(!h.value)return;w.preventDefault();const H=w.clientX-v.value.x,b=w.clientY-v.value.y;m.value={x:H,y:b}},z=w=>{h.value&&(w.preventDefault(),h.value=!1,c.value={...m.value},document.removeEventListener("mousemove",N),document.removeEventListener("mouseup",z))};return{imageViewerVisible:r,imageViewerUrl:a,imageViewerFileName:n,imageZoom:e,isDragging:h,imagePosition:m,openImageViewer:o,closeImageViewer:i,zoomIn:k,zoomOut:L,resetImageZoom:q,handleImageWheel:w=>{w.preventDefault(),w.deltaY<0?k():L()},handleMouseDown:F,handleTouchStart:w=>{if(w.touches.length===1){w.preventDefault();const H=w.touches[0];h.value=!0,v.value={x:H.clientX-m.value.x,y:H.clientY-m.value.y}}},handleTouchMove:w=>{if(!h.value||w.touches.length!==1)return;w.preventDefault();const H=w.touches[0],b=H.clientX-v.value.x,Q=H.clientY-v.value.y;m.value={x:b,y:Q}},handleTouchEnd:w=>{h.value&&(w.preventDefault(),h.value=!1,c.value={...m.value})},downloadCurrentImage:w=>{w&&typeof w=="function"&&w()}}}const ja={name:"FileAttachments",components:{Document:fa,Download:Ne,Upload:ma,Delete:We,Refresh:ga,View:ua,Warning:da,ZoomIn:Re,ZoomOut:Ae,Close:Ve,SimpleImageViewer:Ua},props:{changeData:{type:Object,required:!0},refreshChangeData:{type:Function,default:null}},emits:["update:changeData"],setup(r,{emit:a}){const n="/api/upload_ops_change_file_simple",e={Authorization:`Bearer ${Oe()}`},h=oe({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),v=oe({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),m=oe({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),c=oe({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),o=oe({visible:!1,title:"",fileType:"",previewUrl:"",isSupported:!1,message:"",currentFileType:"",previewType:null,previewOptions:null,isFullscreen:!1,excelInfo:null}),i=V(!1),{imageViewerVisible:k,imageViewerUrl:L,imageViewerFileName:q,imageZoom:F,isDragging:N,imagePosition:z,openImageViewer:B,closeImageViewer:P,zoomIn:X,zoomOut:M,resetImageZoom:G,handleImageWheel:w,handleMouseDown:H,handleTouchStart:b,handleTouchMove:Q,handleTouchEnd:D,downloadCurrentImage:I}=Pa(),$=()=>{B(o)},te=()=>{I(()=>{o.currentFileType&&Ce(o.currentFileType)})},ee=pe(()=>{const s=z.value||{x:0,y:0};return`scale(${F.value||1}) translate(${s.x}px, ${s.y}px)`}),u=pe(()=>N.value?"none":"transform 0.3s ease"),K=oe({oa_process:!1,signed_archive:!1,operation_sheet:!1,supplementary_material:!1}),j=oe({oa_process:0,signed_archive:0,operation_sheet:0,supplementary_material:0}),R=oe({oa_process:null,signed_archive:null,operation_sheet:null,supplementary_material:null}),ae=V(""),ue=V([]),_e=V(!1),je=s=>({changeId:r.changeData.change_id,fileType:s,username:localStorage.getItem("loginUsername")||"unknown"}),be=s=>s?s.split("/").pop():"",De=s=>{if(!["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","image/jpeg","image/png"].includes(s.type))return _.error("文件类型不支持，请上传PDF、Word、Excel或图片文件"),!1;const p=20*1024*1024;return s.size>p?(_.error("文件大小不能超过20MB"),!1):!0},ne=async(s=!1,f=!1)=>{try{if(console.log(`开始${s?"重试":""}刷新变更详情数据...${f?"(强制更新模式)":""}`),console.log("当前变更ID:",r.changeData.id),!r.changeData.id)return console.log("变更ID为空，无法刷新数据"),!1;console.log("发送请求获取最新变更数据...");const p=new Date().getTime(),y={id:r.changeData.id,_t:p};console.log("请求参数:",y);const{default:C}=await pa(async()=>{const{default:U}=await import("./request-DGO27LS-.js").then(W=>W.r);return{default:U}},__vite__mapDeps([0,1,2]),import.meta.url),T=await C({url:"/api/get_ops_change_management",method:"post",data:y,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(console.log("获取变更数据响应:",JSON.stringify(T,null,2)),T.code===0&&T.msg&&T.msg.length>0){const U=T.msg[0];console.log("获取到的最新数据:",JSON.stringify(U,null,2)),console.log("附件相关字段:","附件1(OA流程):",U.oa_process,U.oa_process_file,"附件2(签字存档):",U.signed_archive,U.signed_archive_file,"附件3(操作表):",U.operation_sheet,"附件4(补充资料):",U.supplementary_material);const W={...r.changeData};W.oa_process=U.oa_process,W.oa_process_file=U.oa_process_file,W.signed_archive=U.signed_archive,W.signed_archive_file=U.signed_archive_file,W.operation_sheet=U.operation_sheet,W.supplementary_material=U.supplementary_material,console.log("更新前的数据:",{oa_process:r.changeData.oa_process,oa_process_file:r.changeData.oa_process_file,signed_archive:r.changeData.signed_archive,signed_archive_file:r.changeData.signed_archive_file,operation_sheet:r.changeData.operation_sheet,supplementary_material:r.changeData.supplementary_material}),console.log("更新后的数据:",{oa_process:W.oa_process,oa_process_file:W.oa_process_file,signed_archive:W.signed_archive,signed_archive_file:W.signed_archive_file,operation_sheet:W.operation_sheet,supplementary_material:W.supplementary_material});const fe=r.changeData.oa_process!==W.oa_process||r.changeData.oa_process_file!==W.oa_process_file||r.changeData.signed_archive!==W.signed_archive||r.changeData.signed_archive_file!==W.signed_archive_file||r.changeData.operation_sheet!==W.operation_sheet||r.changeData.supplementary_material!==W.supplementary_material;if(console.log("数据是否有变化:",fe),console.log("是否强制更新:",f),fe||f){console.log(fe?"数据有变化，更新组件数据":"强制更新模式，更新组件数据");const ra=JSON.parse(JSON.stringify(W));return a("update:changeData",ra),s||_.success(fe?"附件状态已更新":"刷新成功"),!0}else return console.log("数据没有变化，无需更新"),!1}else return console.log("获取变更数据失败或数据为空"),!1}catch(p){return console.error("刷新变更详情数据失败:",p),!1}},He=async(s,f)=>{try{if(console.log("上传成功处理函数被调用"),console.log("上传响应:",s),s.code===0){_.success("文件上传成功");const p={...r.changeData};f==="oa_process"?(p.oa_process=!0,p.oa_process_file=s.msg.path):f==="signed_archive"?(p.signed_archive=!0,p.signed_archive_file=s.msg.path):f==="operation_sheet"?p.operation_sheet=s.msg.path:f==="supplementary_material"&&(p.supplementary_material=s.msg.path),console.log("更新前的数据:",r.changeData),console.log("更新后的数据:",p),a("update:changeData",p),console.log("组件数据已更新，准备刷新变更详情数据..."),_.info("正在更新附件状态，请稍候..."),setTimeout(async()=>{console.log("开始延迟刷新...");try{if(r.refreshChangeData&&typeof r.refreshChangeData=="function")console.log("使用父组件的刷新方法"),await r.refreshChangeData()?console.log("父组件刷新成功"):(console.log("父组件刷新失败或无变化，尝试第二次刷新"),setTimeout(async()=>{console.log("开始第二次刷新...");try{const C=await r.refreshChangeData();console.log("第二次刷新完成，结果:",C),C||(console.log("两次刷新均未获取到更新的数据，可能需要手动刷新"),_.info("附件状态可能需要手动刷新查看"))}catch(C){console.error("第二次刷新出错:",C)}},2e3));else{console.log("使用组件内部的刷新方法");const y=await ne(!1,!1);console.log("第一次刷新完成，结果:",y),y||setTimeout(async()=>{console.log("开始第二次刷新...");try{const C=await ne(!0,!1);console.log("第二次刷新完成，结果:",C),C||setTimeout(async()=>{console.log("开始第三次刷新(强制更新模式)...");try{const T=await ne(!0,!0);console.log("第三次刷新完成，结果:",T),T||(console.log("三次刷新均未获取到更新的数据，可能需要手动刷新页面"),_.info("附件状态可能需要手动刷新查看"),setTimeout(()=>{const U=JSON.parse(JSON.stringify(r.changeData));a("update:changeData",U)},100))}catch(T){console.error("第三次刷新出错:",T)}},3e3)}catch(C){console.error("第二次刷新出错:",C)}},2e3)}}catch(y){console.error("第一次刷新出错:",y)}},3e3)}else _.error(`上传失败: ${s.msg}`)}catch(p){console.error("上传成功处理函数出错:",p)}finally{h[f]=!1}},ke=async(s,f)=>{try{if(h[f]=!0,console.log("开始上传文件:",f),console.log("文件信息:",s.file),console.log("变更ID:",r.changeData.change_id),!r.changeData.change_id)throw new Error("变更ID不能为空，请先保存变更信息");const p=new FormData;p.append("file",s.file);const y=localStorage.getItem("loginUsername")||"unknown",C=new URLSearchParams({changeId:r.changeData.change_id,fileType:f,username:y}).toString();console.log("表单数据:",{changeId:r.changeData.change_id,fileType:f,username:y});const T=await fetch(`${n}?${C}`,{method:"POST",headers:{Authorization:e.Authorization},body:p}),U=await T.json();if(T.ok)He(U,f);else throw new Error(U.msg||"上传失败")}catch(p){console.error("上传错误:",p),_.error(`上传失败: ${p.message}`),h[f]=!1}},Ye=s=>{h[s]||(R[s]&&(clearTimeout(R[s]),R[s]=null),j[s]++,K[s]||(K[s]=!0))},Ze=s=>{j[s]--,R[s]=setTimeout(()=>{j[s]<=0&&(K[s]=!1,j[s]=0)},30)},Xe=s=>{h[s]||(R[s]&&(clearTimeout(R[s]),R[s]=null),j[s]++,K[s]=!0)},Je=async(s,f)=>{if(K[f]=!1,j[f]=0,R[f]&&(clearTimeout(R[f]),R[f]=null),h[f]){_.warning("正在上传中，请稍候...");return}const p=s.dataTransfer.files;if(p.length===0){_.warning("未检测到文件");return}if(p.length>1){_.warning("一次只能上传一个文件");return}const y=p[0];if(console.log("拖拽上传文件:",y.name,"类型:",y.type,"大小:",y.size),!De(y))return;const C={file:y,filename:y.name};await ke(C,f)},Ce=async s=>{try{v[s]=!0;const f=`/api/download_ops_change_file?changeId=${r.changeData.change_id}&fileType=${s}&direct=true`,p=document.createElement("a");p.href=f,p.target="_blank",document.body.appendChild(p),p.click(),document.body.removeChild(p),_.success("文件下载成功")}catch(f){console.error("下载错误:",f),_.error(`文件下载失败: ${f.message}`)}finally{v[s]=!1}},Ge=async s=>{try{c[s]=!0;const f=await fetch(`/api/preview_ops_change_file?changeId=${r.changeData.change_id}&fileType=${s}`);if(f.ok){const p=f.headers.get("content-type");if(p&&p.includes("application/json")){const y=await f.json();y.code===0?(o.visible=!0,o.title=`文件预览 - ${y.msg.fileName}`,o.fileType=y.msg.fileType,o.isSupported=y.msg.previewSupported,o.message=y.msg.message,o.previewUrl=y.msg.previewUrl||"",o.currentFileType=s,o.previewType=y.msg.previewType||null,o.previewOptions=y.msg.previewOptions||null,o.excelInfo=y.msg.excelInfo||null,y.msg.previewType==="client_side"&&(console.log("后端选择了纯前端预览方案，自动执行预览"),setTimeout(()=>{Te("client_side")},100))):_.error(y.msg||"获取文件信息失败")}else{const y=be(Se(s)),C=await f.blob(),T=URL.createObjectURL(C);o.visible=!0,o.title=`文件预览 - ${y}`,o.fileType=Le(y),o.isSupported=!0,o.previewUrl=T,o.message="",o.currentFileType=s,o.previewType=null}}else _.error("文件预览失败")}catch(f){console.error("预览错误:",f),_.error(`文件预览失败: ${f.message}`)}finally{c[s]=!1}},Ke=()=>{o.previewUrl&&URL.revokeObjectURL(o.previewUrl),o.visible=!1,o.title="",o.fileType="",o.previewUrl="",o.isSupported=!1,o.message="",o.currentFileType="",o.previewType=null,o.isFullscreen=!1,o.excelInfo=null},Qe=()=>{o.isFullscreen=!o.isFullscreen},Se=s=>{switch(s){case"oa_process":return r.changeData.oa_process_file;case"signed_archive":return r.changeData.signed_archive_file;case"operation_sheet":return r.changeData.operation_sheet;case"supplementary_material":return r.changeData.supplementary_material;default:return""}},Le=s=>{if(!s)return"";const f=s.lastIndexOf(".");return f!==-1?s.substring(f).toLowerCase():""},$e=s=>{Be.confirm("确定要删除此文件吗？此操作不可恢复。","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{m[s]=!0;const f=await fetch("/api/remove_ops_change_file",{method:"POST",headers:{...e,"Content-Type":"application/json"},body:JSON.stringify({changeId:r.changeData.change_id,fileType:s,username:localStorage.getItem("loginUsername")||"unknown"})});if(!f.ok){const y=await f.json();throw new Error(y.msg||"删除失败")}const p=await f.json();if(p.code===0){_.success("文件删除成功"),console.log("文件删除成功，准备更新组件数据");const y={...r.changeData};s==="oa_process"?(y.oa_process=!1,y.oa_process_file=null):s==="signed_archive"?(y.signed_archive=!1,y.signed_archive_file=null):s==="operation_sheet"?y.operation_sheet=null:s==="supplementary_material"&&(y.supplementary_material=null),console.log("更新前的数据:",r.changeData),console.log("更新后的数据:",y),a("update:changeData",y),console.log("组件数据已更新，准备刷新变更详情数据..."),_.info("正在更新附件状态，请稍候..."),setTimeout(async()=>{console.log("开始延迟刷新...");try{if(r.refreshChangeData&&typeof r.refreshChangeData=="function")console.log("使用父组件的刷新方法"),await r.refreshChangeData()?console.log("父组件刷新成功"):(console.log("父组件刷新失败或无变化，尝试第二次刷新"),setTimeout(async()=>{console.log("开始第二次刷新...");try{const T=await r.refreshChangeData();console.log("第二次刷新完成，结果:",T),T||(console.log("两次刷新均未获取到更新的数据，可能需要手动刷新"),_.info("附件状态可能需要手动刷新查看"))}catch(T){console.error("第二次刷新出错:",T)}},2e3));else{console.log("使用组件内部的刷新方法");const C=await ne(!1,!1);console.log("第一次刷新完成，结果:",C),C||setTimeout(async()=>{console.log("开始第二次刷新...");try{const T=await ne(!0,!1);console.log("第二次刷新完成，结果:",T),T||setTimeout(async()=>{console.log("开始第三次刷新(强制更新模式)...");try{const U=await ne(!0,!0);console.log("第三次刷新完成，结果:",U),U||(console.log("三次刷新均未获取到更新的数据，可能需要手动刷新页面"),_.info("附件状态可能需要手动刷新查看"),setTimeout(()=>{const W=JSON.parse(JSON.stringify(r.changeData));a("update:changeData",W)},100))}catch(U){console.error("第三次刷新出错:",U)}},3e3)}catch(T){console.error("第二次刷新出错:",T)}},2e3)}}catch(C){console.error("第一次刷新出错:",C)}},3e3)}else throw new Error(p.msg||"删除失败")}catch(f){console.error("删除错误:",f),_.error(`文件删除失败: ${f.message}`)}finally{m[s]=!1}}).catch(()=>{})},ea=async()=>{try{i.value=!0,_.info("正在刷新附件状态..."),r.refreshChangeData&&typeof r.refreshChangeData=="function"?(console.log("使用父组件的刷新方法"),await r.refreshChangeData()?console.log("父组件刷新成功"):(console.log("父组件刷新失败或无变化"),_.info("刷新完成，未检测到附件状态变化"))):(console.log("使用组件内部的刷新方法"),await ne(!1,!0)||_.info("刷新完成，未检测到附件状态变化"),setTimeout(()=>{const f=JSON.parse(JSON.stringify(r.changeData));a("update:changeData",f)},100))}catch(s){console.error("手动刷新出错:",s),_.error("刷新失败，请稍后重试")}finally{i.value=!1}},Ee=async()=>{try{const s=await re({url:"/api/get_ops_change_templates",method:"post",data:{currentPage:1,pageSize:100}});if(s.code===0){ue.value=s.msg||[],console.log("获取模板列表成功:",ue.value);const f=ue.value.find(p=>p.is_default===!0);f&&(ae.value=f.id,console.log("自动选择默认模板:",f.template_name))}else console.error("获取模板列表失败:",s.msg)}catch(s){console.error("获取模板列表失败:",s)}},aa=async()=>{if(!ae.value){_.warning("请先选择要下载的模板");return}try{_e.value=!0,console.log("开始下载模板，ID:",ae.value);const s=ue.value.find(C=>C.id===ae.value);if(!s){_.error("未找到选中的模板");return}const f=`/api/download_ops_change_template?id=${ae.value}&direct=true`,p=document.createElement("a");p.href=f,p.target="_blank",p.download=s.original_filename||s.template_name,Oe()?window.open(f,"_blank"):(document.body.appendChild(p),p.click(),document.body.removeChild(p)),_.success(`模板 "${s.template_name}" 下载成功`),ae.value=""}catch(s){console.error("下载模板失败:",s),_.error("模板下载失败，请稍后重试")}finally{_e.value=!1}},Ie=()=>{Object.keys(R).forEach(s=>{R[s]&&(clearTimeout(R[s]),R[s]=null)})},ie=()=>{Object.keys(K).forEach(s=>{K[s]=!1,j[s]=0}),Ie()},ta=s=>{setTimeout(()=>{h[s]||(K[s]=!1,j[s]=0)},100)},ze=()=>{setTimeout(()=>{ie()},100)},Me=()=>{setTimeout(()=>{ie()},200)},ge=V(!1),Te=async s=>{if(!o.previewOptions)return;if(s==="fallback"){o.previewType=s,o.previewUrl="",o.isSupported=!1,o.excelInfo&&o.excelInfo.isMultiSheet?o.message=`该Excel文件包含${o.excelInfo.sheetCount}个工作表，需要下载后查看以完整浏览所有工作表`:o.message="Office文档需要下载后查看",_.info("已切换到下载查看模式");return}if(s==="client_side"){ge.value=!0,o.previewType=s,o.isSupported=!0;try{if(!Ra(o.fileType))throw new Error("该文件格式暂不支持纯前端预览");const C=`/api/download_ops_change_file?changeId=${r.changeData.change_id}&fileType=${o.currentFileType}&direct=true`,T=await Aa(C);[".xlsx",".xls"].includes(o.fileType)?(await Va(T,"excel-preview-container"),_.success("Excel文件预览加载成功，支持多工作表切换")):[".docx"].includes(o.fileType)&&(await Na(T,"word-preview-container"),_.info("Word文档预览功能开发中，建议使用其他预览方案")),o.message="正在使用纯前端预览，无需第三方服务支持"}catch(C){console.error("纯前端预览失败:",C),_.error(`纯前端预览失败: ${C.message}`);const T=[".xlsx",".xls"].includes(o.fileType)?"excel-preview-container":"word-preview-container",U=document.getElementById(T);U&&(U.innerHTML=`
              <div style="text-align: center; padding: 40px; color: #f56c6c;">
                <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
                <h3 style="margin-bottom: 10px;">预览失败</h3>
                <p style="margin-bottom: 20px;">${C.message}</p>
                <p style="font-size: 14px; color: #909399;">
                  请尝试其他预览方案或下载文件查看
                </p>
              </div>
            `)}finally{ge.value=!1}return}const f=o.previewOptions[s];if(!f){_.warning("该预览方案暂不可用，请联系管理员配置相关服务");return}ge.value=!0,o.previewType=s,o.previewUrl=f,o.isSupported=!0;const p={office_online:"Microsoft Office Online",google_docs:"Google Docs Viewer",onlyoffice:"ONLYOFFICE Document Server",libreoffice_online:"LibreOffice Online"};let y=`正在使用${p[s]}预览...`;if(o.excelInfo&&o.excelInfo.isMultiSheet&&(y+=`
该Excel文件包含${o.excelInfo.sheetCount}个工作表：${o.excelInfo.sheetNames.join(", ")}`),o.message=y,s==="office_online"){let C=`已切换到 ${p[s]} 预览方案（推荐）`;o.excelInfo&&o.excelInfo.isMultiSheet&&(C+=`，可完整查看所有${o.excelInfo.sheetCount}个工作表`),_.success(C)}else if(s==="google_docs"){let C=`已切换到 ${p[s]} 预览方案（备选）`;o.excelInfo&&o.excelInfo.isMultiSheet&&(C+="，支持基本预览功能"),_.success(C)}else if(s==="onlyoffice"){let C=`已切换到 ${p[s]} 预览方案（本地部署）`;o.excelInfo&&o.excelInfo.isMultiSheet&&(C+=`，可完整查看所有${o.excelInfo.sheetCount}个工作表`),_.success(C)}else if(s==="libreoffice_online"){let C=`已切换到 ${p[s]} 预览方案（本地部署）`;o.excelInfo&&o.excelInfo.isMultiSheet&&(C+=`，可完整查看所有${o.excelInfo.sheetCount}个工作表`),_.success(C)}else _.info(`已切换到 ${p[s]||"下载查看"} 模式`);setTimeout(()=>{ge.value=!1},1500)},oa=()=>({libreoffice_online:"LibreOffice Online 本地预览",onlyoffice:"ONLYOFFICE 本地预览",client_side:"纯前端预览 - 无需第三方服务"})[o.previewType]||"Office文档预览",la=()=>({libreoffice_online:"success",onlyoffice:"success",client_side:"success"})[o.previewType]||"info",na=()=>({libreoffice_online:"正在使用LibreOffice Online服务预览文档，开源可靠的预览方案，完整支持Excel多工作表显示。",onlyoffice:"正在使用ONLYOFFICE Document Server预览文档，商业级的文档预览体验。",client_side:"正在使用纯前端预览技术，无需第三方服务支持，完整支持Excel多工作表切换和数据查看。"})[o.previewType]||"正在预览Office文档...",sa=s=>({libreoffice_online:"success",onlyoffice:"warning",client_side:"success",fallback:"info"})[s]||"info";return ye(()=>{Ee(),document.addEventListener("dragend",ze),document.addEventListener("mouseleave",Me),window.addEventListener("blur",ie);const s=f=>{f.key==="Escape"&&ie()};document.addEventListener("keydown",s),window.fileAttachmentEventHandlers={handleGlobalDragEnd:ze,handleMouseLeave:Me,clearAllDragStates:ie,handleEscKey:s}}),qe(()=>{Ie(),ie(),window.fileAttachmentEventHandlers&&(document.removeEventListener("dragend",window.fileAttachmentEventHandlers.handleGlobalDragEnd),document.removeEventListener("mouseleave",window.fileAttachmentEventHandlers.handleMouseLeave),window.removeEventListener("blur",window.fileAttachmentEventHandlers.clearAllDragStates),document.removeEventListener("keydown",window.fileAttachmentEventHandlers.handleEscKey),delete window.fileAttachmentEventHandlers)}),{uploadLoading:h,downloadLoading:v,removeLoading:m,previewLoading:c,previewDialog:o,refreshLoading:i,dragStates:K,selectedTemplate:ae,templateOptions:ue,templateDownloadLoading:_e,officePreviewLoading:ge,uploadUrl:n,headers:e,getUploadData:je,getFileName:be,beforeUpload:De,customUpload:ke,downloadFile:Ce,previewFile:Ge,closePreviewDialog:Ke,toggleFullscreen:Qe,getFileFieldByType:Se,getFileExtension:Le,removeFile:$e,handleDragEnter:Xe,handleDragOver:Ye,handleDragLeave:Ze,handleDrop:Je,handleCardMouseLeave:ta,handleManualRefresh:ea,getTemplateList:Ee,downloadTemplate:aa,switchPreviewMethod:Te,getPreviewServiceTitle:oa,getPreviewServiceType:la,getPreviewServiceDescription:na,getPreviewMethodType:sa,imageViewerVisible:k,imageViewerUrl:L,imageViewerFileName:q,imageZoom:F,isDragging:N,imagePosition:z,imageTransform:ee,imageTransition:u,openImageViewer:$,closeImageViewer:P,zoomIn:X,zoomOut:M,resetImageZoom:G,handleImageWheel:w,handleMouseDown:H,handleTouchStart:b,handleTouchMove:Q,handleTouchEnd:D,downloadCurrentImage:te}}},Z=r=>(we("data-v-b5db7a64"),r=r(),xe(),r),Ha={class:"file-attachments-container"},Ya={class:"attachments-header"},Za=Z(()=>d("span",{class:"divider-title"},"附件管理",-1)),Xa={class:"template-download-section"},Ja={class:"template-content"},Ga={class:"template-selector"},Ka=Z(()=>d("span",{class:"template-title"},"变更操作表模板下载",-1)),Qa={class:"card-header"},$a=Z(()=>d("span",null,"OA流程",-1)),et={class:"card-content"},at={key:0,class:"drag-overlay"},tt=Z(()=>d("span",{class:"drag-text"},"释放文件进行上传",-1)),ot={key:1,class:"file-info"},lt={class:"file-name"},nt={key:2,class:"file-placeholder"},st=Z(()=>d("span",null,"请上传OA流程文件",-1)),rt={class:"drag-hint"},it={class:"action-buttons"},ct={class:"button-row"},dt={class:"card-header"},ut=Z(()=>d("span",null,"签字存档",-1)),gt={class:"card-content"},mt={key:0,class:"drag-overlay"},ft=Z(()=>d("span",{class:"drag-text"},"释放文件进行上传",-1)),ht={key:1,class:"file-info"},_t={class:"file-name"},pt={key:2,class:"file-placeholder"},vt=Z(()=>d("span",null,"请上传签字存档文件",-1)),yt={class:"drag-hint"},wt={class:"action-buttons"},xt={class:"button-row"},bt={class:"card-header"},Dt=Z(()=>d("span",null,"变更操作表",-1)),kt={class:"card-content"},Ct={key:0,class:"drag-overlay"},St=Z(()=>d("span",{class:"drag-text"},"释放文件进行上传",-1)),Lt={key:1,class:"file-info"},Et={class:"file-name"},It={key:2,class:"file-placeholder"},zt=Z(()=>d("span",null,"请上传变更操作表文件",-1)),Mt={class:"drag-hint"},Tt={class:"action-buttons"},Ot={class:"button-row"},Ft={class:"card-header"},Ut=Z(()=>d("span",null,"补充资料",-1)),Vt={class:"card-content"},Nt={key:0,class:"drag-overlay"},At=Z(()=>d("span",{class:"drag-text"},"释放文件进行上传",-1)),Rt={key:1,class:"file-info"},qt={class:"file-name"},Wt={key:2,class:"file-placeholder"},Bt=Z(()=>d("span",null,"请上传补充资料文件",-1)),Pt={class:"drag-hint"},jt={class:"action-buttons"},Ht={class:"button-row"},Yt={class:"image-viewer-container"},Zt={class:"image-viewer-toolbar"},Xt={class:"toolbar-left"},Jt={class:"image-title"},Gt={class:"toolbar-right"},Kt={class:"zoom-controls"},Qt={class:"action-controls"},$t=Z(()=>d("span",{class:"btn-text"},"下载",-1)),eo=Z(()=>d("span",{class:"btn-text"},"关闭",-1)),ao=["src","alt"],to={key:0,class:"floating-toolbar"},oo={key:0,viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},lo=Z(()=>d("path",{d:"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"},null,-1)),no=[lo],so={key:1,viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},ro=Z(()=>d("path",{d:"M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7"},null,-1)),io=[ro],co={key:1,class:"preview-container"},uo=["src"],go={key:1,class:"office-preview"},mo={"element-loading-text":"正在加载预览...",class:"preview-content-wrapper"},fo={key:2,class:"image-preview"},ho=["src"],_o={class:"image-preview-overlay"},po={key:2,class:"unsupported-preview"},vo={style:{"margin-top":"20px"}},yo={style:{"font-size":"16px",color:"#606266","margin-bottom":"15px"}};function wo(r,a,n,e,h,v){const m=x("el-divider"),c=x("Refresh"),o=x("el-icon"),i=x("el-button"),k=x("el-tag"),L=x("el-option"),q=x("el-select"),F=x("Download"),N=x("el-card"),z=x("Upload"),B=x("Document"),P=x("el-text"),X=x("View"),M=x("el-upload"),G=x("Delete"),w=x("el-col"),H=x("el-row"),b=x("ZoomOut"),Q=x("el-tooltip"),D=x("ZoomIn"),I=x("Close"),$=x("el-empty"),te=x("el-dialog"),ee=_a("loading");return g(),E(le,null,[d("div",Ha,[d("div",Ya,[t(m,{"content-position":"left"},{default:l(()=>[Za]),_:1}),t(i,{type:"primary",size:"small",class:"refresh-button",loading:e.refreshLoading,onClick:e.handleManualRefresh},{default:l(()=>[t(o,null,{default:l(()=>[t(c)]),_:1}),S(" 刷新附件状态 ")]),_:1},8,["loading","onClick"])]),d("div",Xa,[t(N,{shadow:"hover",class:"template-card"},{default:l(()=>[d("div",Ja,[d("div",Ga,[Ka,t(q,{modelValue:e.selectedTemplate,"onUpdate:modelValue":a[0]||(a[0]=u=>e.selectedTemplate=u),placeholder:"请选择变更操作表模板",style:{width:"50%"},size:"small",filterable:"",clearable:""},{default:l(()=>[(g(!0),E(le,null,se(e.templateOptions,u=>(g(),O(L,{key:u.id,label:u.is_default?`${u.template_name} (默认)`:u.template_name,value:u.id},{default:l(()=>[d("span",null,J(u.template_name),1),u.is_default?(g(),O(k,{key:0,type:"success",size:"small",style:{"margin-left":"8px"}},{default:l(()=>[S(" 默认 ")]),_:1})):A("",!0)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"]),t(i,{type:"primary",size:"small",onClick:e.downloadTemplate,disabled:!e.selectedTemplate,loading:e.templateDownloadLoading,style:{"margin-left":"10px"}},{default:l(()=>[t(o,null,{default:l(()=>[t(F)]),_:1}),S(" 下载模板 ")]),_:1},8,["onClick","disabled","loading"])])])]),_:1})]),t(H,{gutter:20},{default:l(()=>[t(w,{xs:24,sm:12,md:6,lg:6,xl:6},{default:l(()=>[t(N,{shadow:"hover",class:ce(["attachment-card",{"drag-over":e.dragStates.oa_process,"has-file":n.changeData.oa_process,uploading:e.uploadLoading.oa_process}]),onDragenter:a[4]||(a[4]=Y(u=>e.handleDragEnter("oa_process"),["prevent"])),onDragover:a[5]||(a[5]=Y(u=>e.handleDragOver("oa_process"),["prevent"])),onDragleave:a[6]||(a[6]=Y(u=>e.handleDragLeave("oa_process"),["prevent"])),onDrop:a[7]||(a[7]=Y(u=>e.handleDrop(u,"oa_process"),["prevent"])),onMouseleave:a[8]||(a[8]=u=>e.handleCardMouseLeave("oa_process"))},{header:l(()=>[d("div",Qa,[$a,n.changeData.oa_process?(g(),O(k,{key:0,type:"success",size:"small"},{default:l(()=>[S("已上传")]),_:1})):(g(),O(k,{key:1,type:"info",size:"small"},{default:l(()=>[S("未上传")]),_:1}))])]),default:l(()=>[d("div",et,[e.dragStates.oa_process?(g(),E("div",at,[t(o,{class:"drag-icon"},{default:l(()=>[t(z)]),_:1}),tt])):A("",!0),n.changeData.oa_process?(g(),E("div",ot,[t(o,null,{default:l(()=>[t(B)]),_:1}),d("span",lt,J(e.getFileName(n.changeData.oa_process_file)),1)])):(g(),E("div",nt,[t(o,null,{default:l(()=>[t(z)]),_:1}),st,d("div",rt,[t(P,{type:"info",size:"small"},{default:l(()=>[S("支持拖拽文件到此区域")]),_:1})])])),d("div",it,[d("div",ct,[n.changeData.oa_process?(g(),O(i,{key:0,type:"primary",size:"small",onClick:a[1]||(a[1]=u=>e.downloadFile("oa_process")),loading:e.downloadLoading.oa_process},{default:l(()=>[t(o,null,{default:l(()=>[t(F)]),_:1}),S(" 下载 ")]),_:1},8,["loading"])):A("",!0),n.changeData.oa_process?(g(),O(i,{key:1,type:"info",size:"small",onClick:a[2]||(a[2]=u=>e.previewFile("oa_process")),loading:e.previewLoading.oa_process},{default:l(()=>[t(o,null,{default:l(()=>[t(X)]),_:1}),S(" 预览 ")]),_:1},8,["loading"])):A("",!0),t(M,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("oa_process"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":u=>e.customUpload(u,"oa_process")},{default:l(()=>[t(i,{type:"success",size:"small",loading:e.uploadLoading.oa_process},{default:l(()=>[t(o,null,{default:l(()=>[t(z)]),_:1}),S(" "+J((n.changeData.oa_process,"上传")),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),n.changeData.oa_process?(g(),O(i,{key:2,type:"danger",size:"small",onClick:a[3]||(a[3]=u=>e.removeFile("oa_process")),loading:e.removeLoading.oa_process},{default:l(()=>[t(o,null,{default:l(()=>[t(G)]),_:1}),S(" 删除 ")]),_:1},8,["loading"])):A("",!0)])])])]),_:1},8,["class"])]),_:1}),t(w,{xs:24,sm:12,md:6,lg:6,xl:6},{default:l(()=>[t(N,{shadow:"hover",class:ce(["attachment-card",{"drag-over":e.dragStates.signed_archive,"has-file":n.changeData.signed_archive,uploading:e.uploadLoading.signed_archive}]),onDragenter:a[12]||(a[12]=Y(u=>e.handleDragEnter("signed_archive"),["prevent"])),onDragover:a[13]||(a[13]=Y(u=>e.handleDragOver("signed_archive"),["prevent"])),onDragleave:a[14]||(a[14]=Y(u=>e.handleDragLeave("signed_archive"),["prevent"])),onDrop:a[15]||(a[15]=Y(u=>e.handleDrop(u,"signed_archive"),["prevent"])),onMouseleave:a[16]||(a[16]=u=>e.handleCardMouseLeave("signed_archive"))},{header:l(()=>[d("div",dt,[ut,n.changeData.signed_archive?(g(),O(k,{key:0,type:"success",size:"small"},{default:l(()=>[S("已上传")]),_:1})):(g(),O(k,{key:1,type:"info",size:"small"},{default:l(()=>[S("未上传")]),_:1}))])]),default:l(()=>[d("div",gt,[e.dragStates.signed_archive?(g(),E("div",mt,[t(o,{class:"drag-icon"},{default:l(()=>[t(z)]),_:1}),ft])):A("",!0),n.changeData.signed_archive?(g(),E("div",ht,[t(o,null,{default:l(()=>[t(B)]),_:1}),d("span",_t,J(e.getFileName(n.changeData.signed_archive_file)),1)])):(g(),E("div",pt,[t(o,null,{default:l(()=>[t(z)]),_:1}),vt,d("div",yt,[t(P,{type:"info",size:"small"},{default:l(()=>[S("支持拖拽文件到此区域")]),_:1})])])),d("div",wt,[d("div",xt,[n.changeData.signed_archive?(g(),O(i,{key:0,type:"primary",size:"small",onClick:a[9]||(a[9]=u=>e.downloadFile("signed_archive")),loading:e.downloadLoading.signed_archive},{default:l(()=>[t(o,null,{default:l(()=>[t(F)]),_:1}),S(" 下载 ")]),_:1},8,["loading"])):A("",!0),n.changeData.signed_archive?(g(),O(i,{key:1,type:"info",size:"small",onClick:a[10]||(a[10]=u=>e.previewFile("signed_archive")),loading:e.previewLoading.signed_archive},{default:l(()=>[t(o,null,{default:l(()=>[t(X)]),_:1}),S(" 预览 ")]),_:1},8,["loading"])):A("",!0),t(M,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("signed_archive"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":u=>e.customUpload(u,"signed_archive")},{default:l(()=>[t(i,{type:"success",size:"small",loading:e.uploadLoading.signed_archive},{default:l(()=>[t(o,null,{default:l(()=>[t(z)]),_:1}),S(" "+J((n.changeData.signed_archive,"上传")),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),n.changeData.signed_archive?(g(),O(i,{key:2,type:"danger",size:"small",onClick:a[11]||(a[11]=u=>e.removeFile("signed_archive")),loading:e.removeLoading.signed_archive},{default:l(()=>[t(o,null,{default:l(()=>[t(G)]),_:1}),S(" 删除 ")]),_:1},8,["loading"])):A("",!0)])])])]),_:1},8,["class"])]),_:1}),t(w,{xs:24,sm:12,md:6,lg:6,xl:6},{default:l(()=>[t(N,{shadow:"hover",class:ce(["attachment-card",{"drag-over":e.dragStates.operation_sheet,"has-file":n.changeData.operation_sheet,uploading:e.uploadLoading.operation_sheet}]),onDragenter:a[20]||(a[20]=Y(u=>e.handleDragEnter("operation_sheet"),["prevent"])),onDragover:a[21]||(a[21]=Y(u=>e.handleDragOver("operation_sheet"),["prevent"])),onDragleave:a[22]||(a[22]=Y(u=>e.handleDragLeave("operation_sheet"),["prevent"])),onDrop:a[23]||(a[23]=Y(u=>e.handleDrop(u,"operation_sheet"),["prevent"])),onMouseleave:a[24]||(a[24]=u=>e.handleCardMouseLeave("operation_sheet"))},{header:l(()=>[d("div",bt,[Dt,n.changeData.operation_sheet?(g(),O(k,{key:0,type:"success",size:"small"},{default:l(()=>[S("已上传")]),_:1})):(g(),O(k,{key:1,type:"info",size:"small"},{default:l(()=>[S("未上传")]),_:1}))])]),default:l(()=>[d("div",kt,[e.dragStates.operation_sheet?(g(),E("div",Ct,[t(o,{class:"drag-icon"},{default:l(()=>[t(z)]),_:1}),St])):A("",!0),n.changeData.operation_sheet?(g(),E("div",Lt,[t(o,null,{default:l(()=>[t(B)]),_:1}),d("span",Et,J(e.getFileName(n.changeData.operation_sheet)),1)])):(g(),E("div",It,[t(o,null,{default:l(()=>[t(z)]),_:1}),zt,d("div",Mt,[t(P,{type:"info",size:"small"},{default:l(()=>[S("支持拖拽文件到此区域")]),_:1})])])),d("div",Tt,[d("div",Ot,[n.changeData.operation_sheet?(g(),O(i,{key:0,type:"primary",size:"small",onClick:a[17]||(a[17]=u=>e.downloadFile("operation_sheet")),loading:e.downloadLoading.operation_sheet},{default:l(()=>[t(o,null,{default:l(()=>[t(F)]),_:1}),S(" 下载 ")]),_:1},8,["loading"])):A("",!0),n.changeData.operation_sheet?(g(),O(i,{key:1,type:"info",size:"small",onClick:a[18]||(a[18]=u=>e.previewFile("operation_sheet")),loading:e.previewLoading.operation_sheet},{default:l(()=>[t(o,null,{default:l(()=>[t(X)]),_:1}),S(" 预览 ")]),_:1},8,["loading"])):A("",!0),t(M,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("operation_sheet"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":u=>e.customUpload(u,"operation_sheet")},{default:l(()=>[t(i,{type:"success",size:"small",loading:e.uploadLoading.operation_sheet},{default:l(()=>[t(o,null,{default:l(()=>[t(z)]),_:1}),S(" "+J((n.changeData.operation_sheet,"上传")),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),n.changeData.operation_sheet?(g(),O(i,{key:2,type:"danger",size:"small",onClick:a[19]||(a[19]=u=>e.removeFile("operation_sheet")),loading:e.removeLoading.operation_sheet},{default:l(()=>[t(o,null,{default:l(()=>[t(G)]),_:1}),S(" 删除 ")]),_:1},8,["loading"])):A("",!0)])])])]),_:1},8,["class"])]),_:1}),t(w,{xs:24,sm:12,md:6,lg:6,xl:6},{default:l(()=>[t(N,{shadow:"hover",class:ce(["attachment-card",{"drag-over":e.dragStates.supplementary_material,"has-file":n.changeData.supplementary_material,uploading:e.uploadLoading.supplementary_material}]),onDragenter:a[28]||(a[28]=Y(u=>e.handleDragEnter("supplementary_material"),["prevent"])),onDragover:a[29]||(a[29]=Y(u=>e.handleDragOver("supplementary_material"),["prevent"])),onDragleave:a[30]||(a[30]=Y(u=>e.handleDragLeave("supplementary_material"),["prevent"])),onDrop:a[31]||(a[31]=Y(u=>e.handleDrop(u,"supplementary_material"),["prevent"])),onMouseleave:a[32]||(a[32]=u=>e.handleCardMouseLeave("supplementary_material"))},{header:l(()=>[d("div",Ft,[Ut,n.changeData.supplementary_material?(g(),O(k,{key:0,type:"success",size:"small"},{default:l(()=>[S("已上传")]),_:1})):(g(),O(k,{key:1,type:"info",size:"small"},{default:l(()=>[S("未上传")]),_:1}))])]),default:l(()=>[d("div",Vt,[e.dragStates.supplementary_material?(g(),E("div",Nt,[t(o,{class:"drag-icon"},{default:l(()=>[t(z)]),_:1}),At])):A("",!0),n.changeData.supplementary_material?(g(),E("div",Rt,[t(o,null,{default:l(()=>[t(B)]),_:1}),d("span",qt,J(e.getFileName(n.changeData.supplementary_material)),1)])):(g(),E("div",Wt,[t(o,null,{default:l(()=>[t(z)]),_:1}),Bt,d("div",Pt,[t(P,{type:"info",size:"small"},{default:l(()=>[S("支持拖拽文件到此区域")]),_:1})])])),d("div",jt,[d("div",Ht,[n.changeData.supplementary_material?(g(),O(i,{key:0,type:"primary",size:"small",onClick:a[25]||(a[25]=u=>e.downloadFile("supplementary_material")),loading:e.downloadLoading.supplementary_material},{default:l(()=>[t(o,null,{default:l(()=>[t(F)]),_:1}),S(" 下载 ")]),_:1},8,["loading"])):A("",!0),n.changeData.supplementary_material?(g(),O(i,{key:1,type:"info",size:"small",onClick:a[26]||(a[26]=u=>e.previewFile("supplementary_material")),loading:e.previewLoading.supplementary_material},{default:l(()=>[t(o,null,{default:l(()=>[t(X)]),_:1}),S(" 预览 ")]),_:1},8,["loading"])):A("",!0),t(M,{class:"upload-button",action:e.uploadUrl,headers:e.headers,data:e.getUploadData("supplementary_material"),"show-file-list":!1,"before-upload":e.beforeUpload,"http-request":u=>e.customUpload(u,"supplementary_material")},{default:l(()=>[t(i,{type:"success",size:"small",loading:e.uploadLoading.supplementary_material},{default:l(()=>[t(o,null,{default:l(()=>[t(z)]),_:1}),S(" "+J((n.changeData.supplementary_material,"上传")),1)]),_:1},8,["loading"])]),_:1},8,["action","headers","data","before-upload","http-request"]),n.changeData.supplementary_material?(g(),O(i,{key:2,type:"danger",size:"small",onClick:a[27]||(a[27]=u=>e.removeFile("supplementary_material")),loading:e.removeLoading.supplementary_material},{default:l(()=>[t(o,null,{default:l(()=>[t(G)]),_:1}),S(" 删除 ")]),_:1},8,["loading"])):A("",!0)])])])]),_:1},8,["class"])]),_:1})]),_:1})]),e.imageViewerVisible?(g(),E("div",{key:0,class:"image-viewer-overlay",onClick:a[39]||(a[39]=(...u)=>e.closeImageViewer&&e.closeImageViewer(...u))},[d("div",Yt,[d("div",Zt,[d("div",Xt,[d("span",Jt,J(e.imageViewerFileName),1)]),d("div",Gt,[d("div",Kt,[t(Q,{content:"缩小",placement:"bottom"},{default:l(()=>[t(i,{class:"toolbar-btn zoom-btn",onClick:e.zoomOut,disabled:e.imageZoom<=.5},{default:l(()=>[t(o,{size:"16"},{default:l(()=>[t(b)]),_:1})]),_:1},8,["onClick","disabled"])]),_:1}),t(Q,{content:"重置缩放",placement:"bottom"},{default:l(()=>[t(i,{class:"toolbar-btn zoom-display",onClick:e.resetImageZoom},{default:l(()=>[S(J(Math.round(e.imageZoom*100))+"% ",1)]),_:1},8,["onClick"])]),_:1}),t(Q,{content:"放大",placement:"bottom"},{default:l(()=>[t(i,{class:"toolbar-btn zoom-btn",onClick:e.zoomIn,disabled:e.imageZoom>=3},{default:l(()=>[t(o,{size:"16"},{default:l(()=>[t(D)]),_:1})]),_:1},8,["onClick","disabled"])]),_:1})]),d("div",Qt,[t(Q,{content:"下载图片",placement:"bottom"},{default:l(()=>[t(i,{class:"toolbar-btn download-btn",onClick:e.downloadCurrentImage},{default:l(()=>[t(o,{size:"16"},{default:l(()=>[t(F)]),_:1}),$t]),_:1},8,["onClick"])]),_:1}),t(Q,{content:"关闭查看器",placement:"bottom"},{default:l(()=>[t(i,{class:"toolbar-btn close-btn",onClick:e.closeImageViewer},{default:l(()=>[t(o,{size:"16"},{default:l(()=>[t(I)]),_:1}),eo]),_:1},8,["onClick"])]),_:1})])])]),d("div",{class:"image-viewer-content",onClick:a[38]||(a[38]=Y(()=>{},["stop"]))},[d("img",{src:e.imageViewerUrl,alt:e.imageViewerFileName,class:ce({dragging:e.isDragging}),style:de(`transform: ${e.imageTransform}; transition: ${e.imageTransition}; max-width: none; max-height: none;`),onWheel:a[33]||(a[33]=(...u)=>e.handleImageWheel&&e.handleImageWheel(...u)),onMousedown:a[34]||(a[34]=(...u)=>e.handleMouseDown&&e.handleMouseDown(...u)),onTouchstart:a[35]||(a[35]=(...u)=>e.handleTouchStart&&e.handleTouchStart(...u)),onTouchmove:a[36]||(a[36]=(...u)=>e.handleTouchMove&&e.handleTouchMove(...u)),onTouchend:a[37]||(a[37]=(...u)=>e.handleTouchEnd&&e.handleTouchEnd(...u)),draggable:"false"},null,46,ao)])])])):A("",!0),t(te,{modelValue:e.previewDialog.visible,"onUpdate:modelValue":a[43]||(a[43]=u=>e.previewDialog.visible=u),title:"",width:e.previewDialog.isFullscreen?"100%":"90%",fullscreen:e.previewDialog.isFullscreen,"before-close":e.closePreviewDialog,"show-close":!1,"append-to-body":"","destroy-on-close":"",class:ce(["file-preview-dialog",{"fullscreen-preview":e.previewDialog.isFullscreen,"no-header-preview":!0}])},{default:l(()=>[e.previewDialog.isSupported?(g(),E("div",to,[t(Q,{content:e.previewDialog.isFullscreen?"退出全屏":"全屏预览",placement:"bottom"},{default:l(()=>[t(i,{class:"toolbar-btn fullscreen-btn",onClick:e.toggleFullscreen,size:"small"},{default:l(()=>[t(o,{size:"16"},{default:l(()=>[e.previewDialog.isFullscreen?(g(),E("svg",oo,no)):(g(),E("svg",so,io))]),_:1})]),_:1},8,["onClick"])]),_:1},8,["content"]),t(Q,{content:"下载文件",placement:"bottom"},{default:l(()=>[t(i,{class:"toolbar-btn download-btn",onClick:a[40]||(a[40]=u=>e.downloadFile(e.previewDialog.currentFileType)),size:"small"},{default:l(()=>[t(o,{size:"16"},{default:l(()=>[t(F)]),_:1})]),_:1})]),_:1}),t(Q,{content:"关闭预览",placement:"bottom"},{default:l(()=>[t(i,{class:"toolbar-btn close-btn",onClick:e.closePreviewDialog,size:"small"},{default:l(()=>[t(o,{size:"16"},{default:l(()=>[t(I)]),_:1})]),_:1},8,["onClick"])]),_:1})])):A("",!0),e.previewDialog.isSupported?(g(),E("div",co,[e.previewDialog.fileType===".pdf"?(g(),E("iframe",{key:0,src:e.previewDialog.previewUrl,style:de(`width: 100%; height: ${e.previewDialog.isFullscreen?"100vh":"70vh"}; border: none;`),title:"PDF预览"},null,12,uo)):[".xlsx",".xls",".docx"].includes(e.previewDialog.fileType)?(g(),E("div",go,[ha((g(),E("div",mo,[[".xlsx",".xls"].includes(e.previewDialog.fileType)?(g(),E("div",{key:0,id:"excel-preview-container",style:de(`width: 100%; height: ${e.previewDialog.isFullscreen?"100vh":"70vh"}; border: none; border-radius: 0; overflow: auto; background: white;`)},null,4)):[".docx"].includes(e.previewDialog.fileType)?(g(),E("div",{key:1,id:"word-preview-container",style:de(`width: 100%; height: ${e.previewDialog.isFullscreen?"100vh":"70vh"}; border: none; border-radius: 0; overflow: auto; padding: 20px; background: white;`)},null,4)):A("",!0)])),[[ee,e.officePreviewLoading]])])):[".jpg",".jpeg",".png",".gif"].includes(e.previewDialog.fileType)?(g(),E("div",fo,[d("div",{class:"image-preview-container",onClick:a[41]||(a[41]=(...u)=>e.openImageViewer&&e.openImageViewer(...u))},[d("img",{src:e.previewDialog.previewUrl,style:de(`max-width: 100%; max-height: ${e.previewDialog.isFullscreen?"100vh":"70vh"}; display: block; margin: 0 auto; cursor: pointer;`),alt:"图片预览"},null,12,ho),d("div",_o,[t(i,{type:"primary",size:"large"},{default:l(()=>[t(o,null,{default:l(()=>[t(D)]),_:1}),S(" 点击放大查看 ")]),_:1})])])])):A("",!0)])):(g(),E("div",po,[t($,{description:" "},{image:l(()=>[t(o,{size:"60",color:"#409eff"},{default:l(()=>[t(B)]),_:1})]),default:l(()=>[d("div",vo,[d("p",yo,J(e.previewDialog.message),1),t(i,{type:"primary",onClick:a[42]||(a[42]=u=>e.downloadFile(e.previewDialog.currentFileType))},{default:l(()=>[t(o,null,{default:l(()=>[t(F)]),_:1}),S(" 立即下载 ")]),_:1})])]),_:1})]))]),_:1},8,["modelValue","width","fullscreen","before-close","class"])],64)}const xo=ve(ja,[["render",wo],["__scopeId","data-v-b5db7a64"]]),bo={name:"ChangeManagementDetail",components:{FileAttachments:xo,Delete:We,InfoFilled:va},setup(){const r=ya(),a=xa(),n=V(null),e=V(!1),h=V(!1),v=V(!1),m=()=>{const D=localStorage.getItem("role_code"),I=localStorage.getItem("loginUsername");v.value=I==="admin"||D&&D.includes("D"),console.log("删除权限检查:",{username:I,roleCode:D,hasDeletePermission:v.value})},c=oe({id:null,change_id:"",title:"",system:"",change_level:"",planned_change_time:"",requester:"",implementers:"",oa_process:!1,oa_process_file:null,signed_archive:!1,signed_archive_file:null,operation_sheet:null,supplementary_material:null}),o=V([]),i=V([]),k=D=>{c.implementers=D.join(",")},L=D=>{c.system=D.join(",")},q=D=>{if(!D)return"未知用户";const I=B.value.find($=>$.username===D);return I?I.real_name:D},F={title:[{required:!0,message:"请输入变更名称",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],system:[{required:!0,message:"请选择变更系统",trigger:"change"},{max:100,message:"变更系统长度不能超过100个字符",trigger:"blur"}],change_level:[{required:!0,message:"请选择变更级别",trigger:"change"}],planned_change_time:[{required:!0,message:"请选择计划变更时间",trigger:"change"}],requester:[{required:!0,message:"请选择变更负责人",trigger:"change"}],implementers:[{required:!0,message:"请选择变更实施人",trigger:"change"}]},N=V([]),z=V([]),B=V([]),P=async()=>{try{const D=await re({url:"/api/get_system_list",method:"post"});D.code===0&&(N.value=D.msg.filter(I=>I&&I.system_abbreviation!==null&&I.system_abbreviation!==void 0))}catch(D){console.error("获取系统列表失败:",D),_.error("获取系统列表失败")}},X=async()=>{try{const D=await re({url:"/api/get_cmdb_data_dictionary",method:"post",data:{dict_type:"P"}});D.code===0&&(z.value=D.msg.filter(I=>I&&I.dict_code!==null&&I.dict_name!==null))}catch(D){console.error("获取变更级别列表失败:",D),_.error("获取变更级别列表失败")}},M=async()=>{try{const D=await re({url:"/api/get_user_list",method:"post"});D.code===0&&(B.value=D.msg.filter(I=>I&&I.username!==null&&I.username!==void 0&&I.real_name!==null&&I.real_name!==void 0))}catch(D){console.error("获取用户列表失败:",D),_.error("获取用户列表失败")}},G=async(D,I=!0,$=!0)=>{let te=null;I&&(te=wa.service({lock:!0,text:"加载中...",background:"rgba(0, 0, 0, 0.7)"}));try{console.log("获取变更详情，ID:",D);const ee=new Date().getTime(),u=await re({url:"/api/get_ops_change_management",method:"post",data:{id:D,_t:ee},headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(console.log("获取变更详情响应:",u),u.code===0&&u.msg.length>0){const K=u.msg[0];console.log("获取到的变更数据:",K);const j={};return Object.keys(c).forEach(R=>{if(K[R]!==void 0)if(R==="planned_change_time"&&K[R]){const ae=K[R];console.log("处理计划时间字段:",ae,"类型:",typeof ae),j[R]=Ca(ae),console.log("时区安全转换结果:",j[R])}else j[R]=K[R];else j[R]=c[R]}),console.log("更新前的数据:",JSON.stringify({oa_process:c.oa_process,oa_process_file:c.oa_process_file,signed_archive:c.signed_archive,signed_archive_file:c.signed_archive_file,operation_sheet:c.operation_sheet})),console.log("更新后的数据:",JSON.stringify({oa_process:j.oa_process,oa_process_file:j.oa_process_file,signed_archive:j.signed_archive,signed_archive_file:j.signed_archive_file,operation_sheet:j.operation_sheet})),Object.assign(c,j),c.implementers&&(o.value=c.implementers.split(",")),c.system&&(i.value=c.system.split(",")),$&&_.success("数据已刷新"),!0}else return $&&_.error("未找到变更记录"),a.push("/ops_change_management"),!1}catch(ee){return console.error("获取变更详情失败:",ee),$&&_.error("获取变更详情失败"),a.push("/ops_change_management"),!1}finally{te&&te.close()}},w=async()=>(console.log("刷新变更详情数据"),c.id?await G(c.id,!1,!0):(console.log("变更ID为空，无法刷新数据"),!1)),H=async()=>{n.value&&await n.value.validate(async D=>{if(!D){_.error("请检查表单填写是否正确");return}e.value=!0;try{const I=c.id?"/api/update_ops_change_management":"/api/add_ops_change_management",$=localStorage.getItem("loginUsername")||"admin",te={...c,changeLevel:c.change_level,plannedChangeTime:c.planned_change_time,username:$,created_by:$,updated_by:$};console.log("发送请求数据:",te);const ee=await re({url:I,method:"post",data:te});ee.code===0?(_.success("保存成功"),c.id||(c.id=ee.msg.id,c.change_id=ee.msg.change_id)):_.error(`保存失败: ${ee.msg}`)}catch(I){console.error("保存变更失败:",I),_.error("保存变更失败")}finally{e.value=!1}})},b=()=>{a.push("/ops_change_management")},Q=()=>{if(!v.value){_.error("您没有删除权限");return}if(!c.id){_.error("无法删除：变更记录ID不存在");return}Be.confirm(`确定要删除变更记录 "${c.change_id}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}).then(async()=>{h.value=!0;try{const D=await re({url:"/api/del_ops_change_management",method:"post",data:{id:c.id,username:localStorage.getItem("loginUsername")||"admin"}});D.code===0?(_.success("删除成功"),a.push("/ops_change_management")):_.error(`删除失败: ${D.msg}`)}catch(D){console.error("删除变更记录失败:",D),_.error("删除变更记录失败")}finally{h.value=!1}}).catch(()=>{console.log("用户取消删除操作")})};return ye(async()=>{m(),await Promise.all([P(),X(),M()]);const D=r.params.id;if(D&&D!=="new")await G(D);else{const I=localStorage.getItem("loginUsername");I&&(c.requester=I)}}),{formRef:n,changeData:c,implementersArray:o,systemArray:i,rules:F,systemOptions:N,changeLevelOptions:z,userOptions:B,saveLoading:e,deleteLoading:h,hasDeletePermission:v,handleImplementersChange:k,handleSystemChange:L,getUserRealName:q,saveChange:H,goBack:b,handleDelete:Q,refreshChangeData:w}}},Pe=r=>(we("data-v-d6f837cb"),r=r(),xe(),r),Do={class:"app-container"},ko={class:"card-header"},Co=Pe(()=>d("span",null,"变更详情",-1)),So={class:"header-buttons"},Lo={class:"change-id-section"},Eo={class:"change-id-text"},Io={class:"change-info-card"},zo=Pe(()=>d("div",{class:"card-title"},"变更信息",-1)),Mo={class:"card-content"},To={key:0,class:"tag-display"},Oo={key:0,class:"tag-display"},Fo={key:0,class:"delete-section"},Uo={class:"delete-button-container"},Vo={key:0,class:"permission-tip"};function No(r,a,n,e,h,v){const m=x("el-button"),c=x("el-input"),o=x("el-form-item"),i=x("el-col"),k=x("el-option"),L=x("el-select"),q=x("el-row"),F=x("el-date-picker"),N=x("el-tag"),z=x("el-form"),B=x("file-attachments"),P=x("el-divider"),X=x("Delete"),M=x("el-icon"),G=x("InfoFilled"),w=x("el-text"),H=x("el-card");return g(),E("div",Do,[t(H,{class:"box-card"},{header:l(()=>[d("div",ko,[Co,d("div",So,[t(m,{type:"primary",onClick:e.goBack},{default:l(()=>[S("返回列表")]),_:1},8,["onClick"]),t(m,{type:"success",onClick:e.saveChange,loading:e.saveLoading},{default:l(()=>[S("保存")]),_:1},8,["onClick","loading"])])])]),default:l(()=>[d("div",Lo,[d("span",Eo,"变更编号："+J(e.changeData.change_id||"系统自动生成"),1)]),d("div",Io,[zo,d("div",Mo,[t(z,{ref:"formRef",model:e.changeData,rules:e.rules,"label-width":"100px","label-position":"right"},{default:l(()=>[t(q,{gutter:16},{default:l(()=>[t(i,{xs:24,sm:16,md:16,lg:16,xl:16},{default:l(()=>[t(o,{label:"变更名称",prop:"title"},{default:l(()=>[t(c,{modelValue:e.changeData.title,"onUpdate:modelValue":a[0]||(a[0]=b=>e.changeData.title=b),placeholder:"请输入变更名称",size:"small"},null,8,["modelValue"])]),_:1})]),_:1}),t(i,{xs:24,sm:8,md:8,lg:8,xl:8},{default:l(()=>[t(o,{label:"变更级别",prop:"change_level"},{default:l(()=>[t(L,{modelValue:e.changeData.change_level,"onUpdate:modelValue":a[1]||(a[1]=b=>e.changeData.change_level=b),filterable:"",placeholder:"请选择变更级别",style:{width:"100%"},size:"small"},{default:l(()=>[(g(!0),E(le,null,se(e.changeLevelOptions,b=>(g(),O(k,{key:b.dict_code,label:b.dict_name,value:b.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(q,{gutter:16},{default:l(()=>[t(i,{xs:24,sm:12,md:12,lg:12,xl:12},{default:l(()=>[t(o,{label:"变更时间",prop:"planned_change_time"},{default:l(()=>[t(F,{modelValue:e.changeData.planned_change_time,"onUpdate:modelValue":a[2]||(a[2]=b=>e.changeData.planned_change_time=b),type:"date",placeholder:"选择日期",style:{width:"100%"},"value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",size:"small"},null,8,["modelValue"])]),_:1})]),_:1}),t(i,{xs:24,sm:12,md:12,lg:12,xl:12},{default:l(()=>[t(o,{label:"变更负责人",prop:"requester"},{default:l(()=>[t(L,{modelValue:e.changeData.requester,"onUpdate:modelValue":a[3]||(a[3]=b=>e.changeData.requester=b),filterable:"",placeholder:"请选择负责人",style:{width:"100%"},size:"small"},{default:l(()=>[(g(!0),E(le,null,se(e.userOptions,b=>(g(),O(k,{key:b.username,label:b.real_name,value:b.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(q,{gutter:16},{default:l(()=>[t(i,{xs:24,sm:12,md:12,lg:12,xl:12},{default:l(()=>[t(o,{label:"变更系统",prop:"system"},{default:l(()=>[t(L,{modelValue:e.systemArray,"onUpdate:modelValue":a[4]||(a[4]=b=>e.systemArray=b),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",filterable:"",placeholder:"请选择变更系统",style:{width:"100%"},size:"small",onChange:e.handleSystemChange},{default:l(()=>[(g(!0),E(le,null,se(e.systemOptions,b=>(g(),O(k,{key:b.system_abbreviation,label:b.system_abbreviation,value:b.system_abbreviation},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),e.systemArray.length>0?(g(),E("div",To,[(g(!0),E(le,null,se(e.systemArray,b=>(g(),O(N,{key:b,class:"display-tag",type:"primary",effect:"light",size:"small"},{default:l(()=>[S(J(b),1)]),_:2},1024))),128))])):A("",!0)]),_:1})]),_:1}),t(i,{xs:24,sm:12,md:12,lg:12,xl:12},{default:l(()=>[t(o,{label:"变更实施人",prop:"implementers"},{default:l(()=>[t(L,{modelValue:e.implementersArray,"onUpdate:modelValue":a[5]||(a[5]=b=>e.implementersArray=b),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",filterable:"",placeholder:"请选择实施人",style:{width:"100%"},size:"small",onChange:e.handleImplementersChange},{default:l(()=>[(g(!0),E(le,null,se(e.userOptions,b=>(g(),O(k,{key:b.username,label:b.real_name,value:b.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),e.implementersArray.length>0?(g(),E("div",Oo,[(g(!0),E(le,null,se(e.implementersArray,b=>(g(),O(N,{key:b,class:"display-tag",type:"primary",effect:"light",size:"small"},{default:l(()=>[S(J(e.getUserRealName(b)),1)]),_:2},1024))),128))])):A("",!0)]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])])]),t(B,{changeData:e.changeData,"onUpdate:changeData":a[6]||(a[6]=b=>e.changeData=b),refreshChangeData:e.refreshChangeData},null,8,["changeData","refreshChangeData"]),e.changeData.id?(g(),E("div",Fo,[t(P),d("div",Uo,[t(m,{type:"danger",disabled:!e.hasDeletePermission,loading:e.deleteLoading,onClick:e.handleDelete,class:"delete-button"},{default:l(()=>[t(M,null,{default:l(()=>[t(X)]),_:1}),S(" 删除变更记录 ")]),_:1},8,["disabled","loading","onClick"]),e.hasDeletePermission?A("",!0):(g(),E("div",Vo,[t(w,{type:"info",size:"small"},{default:l(()=>[t(M,null,{default:l(()=>[t(G)]),_:1}),S(" 您没有删除权限 ")]),_:1})]))])])):A("",!0)]),_:1})])}const Bo=ve(bo,[["render",No],["__scopeId","data-v-d6f837cb"]]);export{Bo as default};
