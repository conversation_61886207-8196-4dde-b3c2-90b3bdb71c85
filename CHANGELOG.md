## [2.2.5.1] - 2025年8月19日

### 更新
- 

### 优化
- 

### 修复
- 

## [*******] - 2025年8月15日

### 新增功能
- **🔄 VMware虚拟化信息自动更新功能**：新增VMware虚拟机信息的自动同步和管理功能，位于自动发现菜单下
  - **功能定位**：归属于自动发现模块，页面名称为"虚拟化信息更新管理"
  - **自动定时同步**：每小时自动从指定接口获取VMware虚拟机信息并更新到数据库
    - 数据源：`http://**************:8081/config_files?file=vmware_hosts.json`
    - 同步间隔：可配置，默认每小时执行一次（0 * * * *）
    - 支持启用/禁用自动同步功能
  - **数据库设计**：
    - 主表：`cmdb_vmware_info_auto_update` - 存储VMware虚拟机基本信息
    - 日志表：`cmdb_vmware_sync_logs` - 记录同步过程和结果
    - 视图：`v_cmdb_vmware_info_auto_update` - 用于查询显示
  - **数据字段简化**：只保留接口提供的核心字段
    - 虚拟机名称、vCenter IP、ESXi主机IP、虚拟机IP
    - 数据源时间、表更新时间、同步状态等管理字段
  - **前端管理界面**：位于自动发现菜单下的"虚拟化信息更新管理"
    - 📊 统计卡片：显示虚拟机总数、有IP虚拟机、vCenter数量、ESXi主机数等
    - 🔄 同步状态：实时显示服务运行状态、最后同步时间、数据源地址
    - 🔍 搜索过滤：支持按虚拟机名称、vCenter IP、ESXi主机IP、虚拟机IP、同步状态等条件筛选
    - 📋 数据表格：支持排序、分页，显示虚拟机基本信息
    - 👁️ 详情查看：显示虚拟机完整信息和同步状态
    - 🗑️ 数据管理：支持单个删除和批量删除操作
    - ⚡ 手动同步：支持用户手动触发数据同步
    - 📤 数据导出：支持导出Excel格式数据
  - **后端服务架构**：
    - 同步服务：`VmwareSyncService` - 负责数据获取和处理
    - 控制器：`VmwareHostsController` - 提供API接口
    - 定时任务：基于node-cron实现定时同步
  - **数据处理逻辑**：
    - 解析接口返回的JSON数据，提取虚拟机数组和更新时间
    - 智能IP地址解析，支持IPv4和IPv6格式验证
    - 基于虚拟机名称的唯一性约束，避免重复数据
  - **API接口完整**：
    - 数据查询：获取虚拟机列表、详情、统计信息
    - 数据管理：删除、批量删除、导出
    - 同步管理：手动同步、状态查询、历史记录
  - **监控和维护**：
    - 同步状态实时监控
    - 详细的同步日志记录
    - 自动清理过期日志（默认30天）
    - 错误处理和重试机制
  - **环境配置**：
    - `VMWARE_DATA_SOURCE_URL`：数据源接口地址
    - `VMWARE_SYNC_INTERVAL`：同步间隔（cron表达式）
    - `VMWARE_SYNC_ENABLED`：是否启用自动同步
    - `VMWARE_SYNC_TIMEOUT`：接口请求超时时间
  - **部署支持**：
    - 提供数据库表创建脚本
    - 自动化部署脚本
    - 完整的部署和使用文档
  - **相关文件**：
    - 数据库：`sql/*******/create_vmware_hosts_table.sql`
    - 后端服务：`backend/services/vmware/vmware-sync-service.js`
    - 后端控制器：`backend/controllers/vmware/vmware-hosts-controller.js`
    - API路由：`backend/api/vmware-hosts-api.js`
    - 前端页面：`frontend/src/views/cmdb_vmware_hosts_management.vue`
    - API接口：`frontend/src/api/vmware.js`
    - 部署脚本：`backend/scripts/deploy-vmware-tables.js`
    - 使用文档：`docs/vmware_hosts_auto_update_guide.md`

## [*******] - 2025年8月14日

### 新增功能
- **🔗 应用系统信息页面虚拟机关联字段**：为 `cmdb_application_system_info` 页面新增"是否虚拟机"关联字段
  - **关联逻辑**：通过管理IP与 `cmdb_vm_registry` 表进行自动关联
  - **显示规则**：如果管理IP存在于虚拟机表中则显示"是"，否则显示"否"
  - **前端实现**：在数据表格中新增"是否虚拟机"列，使用标签样式显示
    - "是"：绿色成功标签（success）
    - "否"：灰色信息标签（info）
  - **后端实现**：在SQL查询中使用CASE WHEN EXISTS子查询实现关联判断
    - 检查 `cmdb_vm_registry` 表中是否存在相同的管理IP
    - 确保只关联有效记录（del_flag = '0'）
  - **用户价值**：
    - 🔍 快速识别哪些应用系统部署在虚拟机上
    - 📊 便于统计虚拟化部署情况
    - 🎯 支持基于部署方式的分类管理
    - 📈 为虚拟化迁移规划提供数据支持
  - **相关文件**：
    - 前端：`frontend/src/views/cmdb_application_system_info.vue`
    - 后端：`backend/index.js`
- **🔍 应用系统信息页面虚拟机筛选功能**：为 `cmdb_application_system_info` 页面新增"是否虚拟机"筛选条件
  - **前端筛选**：在搜索表单中新增"是否虚拟机"下拉选择框
    - 支持筛选"是"（虚拟机）或"否"（物理机）
    - 支持清空筛选条件显示全部记录
    - 与其他筛选条件联合使用，支持复合查询
  - **后端查询优化**：完善API查询参数和WHERE条件
    - 新增 `is_virtual_machine` 查询参数处理
    - 在主查询和总数查询中都添加虚拟机筛选条件
    - 使用高效的EXISTS子查询实现筛选逻辑
  - **用户价值**：
    - 🎯 精准筛选：快速找到所有虚拟机或物理机部署的应用系统
    - 📊 分类统计：便于按部署方式进行数据统计和分析
    - 🔄 运维管理：支持针对不同部署方式的批量操作和管理
    - 📈 决策支持：为虚拟化迁移和资源规划提供准确的筛选数据
- **🔍 服务器管理页面操作系统查询条件**：为 `cmdb_server_management` 页面新增操作系统筛选功能
  - **查询条件增强**：在搜索表单中新增"操作系统"下拉选择框，支持按操作系统类型进行精确筛选
  - **数据源集成**：从数据字典 `operatingsystems`（dict_type='K'）动态加载操作系统选项
  - **搜索逻辑优化**：在 `search` 对象中添加 `operating_system` 字段，支持与其他搜索条件组合使用
  - **重置功能完善**：重置搜索时正确清空操作系统选择条件
  - **位置布局**：操作系统查询条件放置在PING状态和带外管理IP之间，界面布局合理
  - **相关文件**：`frontend/src/views/cmdb_server_management.vue`

- **🔍 虚拟机管理页面操作系统查询条件**：为 `cmdb_vm_registry` 页面新增操作系统筛选功能
  - **查询条件增强**：在搜索表单中新增"操作系统"下拉选择框，支持按操作系统类型进行精确筛选
  - **数据源集成**：从数据字典 `operatingsystems`（dict_type='K'）动态加载操作系统选项
  - **搜索逻辑优化**：在 `search` 对象中添加 `operating_system` 字段，支持与其他搜索条件组合使用
  - **重置功能完善**：重置搜索时正确清空操作系统选择条件
  - **位置布局**：操作系统查询条件放置在所属机房和需要监控之间，界面布局合理
  - **筛选功能完善**：所有下拉选择框都已具备 `filterable` 属性，提供一致的搜索体验
  - **相关文件**：`frontend/src/views/cmdb_vm_registry.vue`

- **🔧 虚拟机所属系统字段优化**：优化 `cmdb_vm_registry` 页面中的"虚拟机所属系统"字段
  - **搜索功能优化**：将搜索区域的输入框改为下拉选择框，支持从业务系统列表中选择
  - **自动关联保持**：新增和编辑表单中保持原有的自动关联方式，无需用户手动填写
  - **数据源集成**：从系统管理员责任表 `cmdb_system_admin_responsibility_company` 获取业务系统选项
  - **用户体验提升**：
    - 🔍 搜索区域支持 `filterable` 功能，用户可输入关键词快速定位业务系统
    - � 自动去`重，确保业务系统选项的唯一性
    - 🎯 统一的选择体验，与其他下拉框保持一致
    - 🧹 支持清空功能，方便用户重新选择
    - � 表单清中保持自动关联，减少用户操作负担
  - **技术实现**：
    - 新增 `businessSystems` 数据数组存储业务系统选项
    - 新增 `getBusinessSystems()` 方法获取业务系统列表
    - 在 `mounted` 生命周期中自动加载业务系统数据
    - 表单中的 `app_system_id` 字段保持后端自动处理
  - **设计理念**：搜索时提供便利的选择功能，表单中保持原有的自动关联逻辑
  - **相关文件**：`frontend/src/views/cmdb_vm_registry.vue`

### 更新
- **🔍 应用系统信息页面预输入功能增强**：为 `cmdb_application_system_info` 页面的所有筛选框添加预输入功能
  - **自动完成输入框**：将"管理IP"、"主机名"、"功能用途"三个输入框升级为 `el-autocomplete` 组件
    - 支持动态数据源：从当前页面数据中提取唯一值作为建议选项
    - 智能过滤：支持实时输入过滤，大小写不敏感匹配
    - 性能优化：限制显示前10个匹配项，避免选项过多影响性能
  - **下拉框过滤增强**：为所有下拉选择框添加 `filterable` 属性
    - ✅ 设备管理员、系统管理员 - 已有 `filterable`
    - ✅ 设备生命周期、系统运行状态、PING状态 - 新增 `filterable`
    - ✅ 所属机房、归属业务系统 - 已有 `filterable`
    - ✅ 系统分级、需要监控、监控状态 - 新增 `filterable`
    - ✅ 生产属性、操作系统 - 新增 `filterable`
  - **数据管理优化**：
    - 新增 `autoCompleteOptions` 数据结构存储自动完成选项
    - 新增 `updateAutoCompleteOptions()` 方法从当前数据更新建议
    - 新增 `querySearchAsync()` 方法处理异步搜索
    - 新增 `initAutoCompleteOptions()` 方法初始化建议数据
    - 新增 `handleSelect()` 方法处理选择事件
  - **用户体验提升**：
    - 🔍 预输入功能 - 用户可以看到并选择之前输入过的值
    - ⚡ 快速筛选 - 所有下拉框都支持输入搜索
    - 📝 智能建议 - 基于历史数据提供相关建议
    - 🧹 一键清空 - 所有字段都支持快速清空

### 优化
- **🔍 服务器管理页面筛选功能全面优化**：为所有下拉选择框添加 `filterable` 功能，提升用户搜索体验
  - **筛选功能增强**：为以下所有搜索下拉框添加 `filterable` 属性
    - ✅ 服务器类型 - 新增 `filterable`
    - ✅ 管理员1、管理员2 - 已有 `filterable`
    - ✅ 生产属性、所属机房 - 新增 `filterable`
    - ✅ 生命周期、是否信创 - 新增 `filterable`
    - ✅ 需要监控、监控状态 - 新增 `filterable`
    - ✅ PING状态、操作系统 - 新增 `filterable`
  - **用户体验提升**：
    - 🔍 智能搜索 - 用户可以在下拉框中输入关键词快速定位选项
    - ⚡ 快速筛选 - 支持拼音和中文搜索，提高查询效率
    - 📝 预输入提示 - 实时显示匹配的选项，减少用户操作步骤
    - 🎯 精确匹配 - 支持模糊匹配和精确选择，满足不同查询需求
  - **界面一致性**：所有筛选下拉框现在都具有统一的交互体验和功能特性

### 修复
- 

## [2.2.4.7] - 2025年8月11日

### 重要修复
- **🔧 数据字典字段编辑回显问题修复**：解决了所有CMDB表中数据字典字段在编辑时的回显错误问题
  - **问题描述**：编辑记录时，数据字典字段（如所属机房、设备类型等）会错误地使用显示名称而非字典代码进行回显，导致更新其他字段时这些字典字段被错误地保存为显示名称
  - **修复范围**：涵盖以下5个核心表的所有数据字典字段
    - `cmdb_vm_registry`：所属机房、操作系统、生命周期状态
    - `cmdb_device_management`：设备类型、生产属性、所属机房、生命周期
    - `cmdb_server_management`：服务器类型、生产属性、所属机房、生命周期、操作系统、操作系统分类
    - `cmdb_system_admin_responsibility_company`：系统属性、团队线条、业务系统大类、系统分级、运行状态、备份标准、信创分类等
    - `cmdb_application_system_info`：所属机房、生产属性、系统分级、操作系统

### 技术实现
- **后端API增强**：为所有查询API添加原始字典代码字段（如 `data_center_code`、`device_type_code` 等）
  - 通过子查询从原始表中获取字典代码，确保编辑时能获取到正确的原始值
  - 保持向后兼容性，同时返回显示名称和原始代码
- **前端回显逻辑优化**：修改编辑表单的数据回显逻辑
  - 优先使用原始字典代码字段进行回显（如 `row.data_center_code || row.data_center`）
  - 确保表单中存储的是字典代码而非显示名称
  - 保证提交时发送给后端的仍然是标准的字典代码

### 数据一致性保障
- **存储标准化**：确保数据库中始终存储字典代码，维护数据的标准化和一致性
- **显示友好化**：通过视图层自动转换，用户界面显示友好的中文名称
- **编辑准确性**：编辑时正确回显对应的字典选项，避免数据污染

### 影响范围
- 解决了长期存在的数据字典字段编辑问题，提升了数据质量
- 所有涉及数据字典的CMDB模块现在都能正确处理编辑操作
- 为后续的数据分析和报表功能提供了可靠的数据基础

## [2.2.4.6] - 2025年8月8日

### 更新
- **🔧 虚拟机管理表单验证增强**：将虚拟机登记表中的"所属机房"字段设置为必填项
  - 新增和编辑虚拟机时必须选择所属机房
  - 添加表单验证规则，提供友好的错误提示："请选择所属机房"
  - 确保虚拟机资产数据的完整性和规范性
  - 与其他资产管理模块保持一致的必填字段要求

### 优化
- **📋 数据完整性提升**：通过必填验证确保所有虚拟机记录都具有明确的机房归属信息
- **🎯 用户体验优化**：表单验证在用户选择变更时触发，及时提供反馈

### 修复
- 

## [*******] - 2025年8月6日

### 新增
- 自动用户字段中间件
  - 新增资产时，创建人和更新人默认都使用当前登录用户的username
  - 编辑已有资产时，更新人默认使用当前登录用户的username
  - 自动从JWT token中提取用户信息，提升安全性
  - 保持向后兼容性，前端代码无需修改

### 修复
- 修复cmdb_vm_registry新增数据时更新人默认填写admin的问题
- 修复多个CMDB新增API中缺少updated_by字段的问题
  - cmdb_device_management
  - cmdb_server_management
  - cmdb_application_system_info
  - cmdb_system_admin_responsibility_company
  - cmdb_system_admin_responsibility
  - cmdb_data_dictionary
- 修复cmdb_system_admin_responsibility_company新增和编辑时created_by和updated_by字段未正确获取username的问题
  - 前端代码使用了错误的localStorage键名('username'而不是'loginUsername')
  - 修复后现在能正确获取当前登录用户的用户名
- 统一所有CMDB新增API的用户字段处理逻辑

### 更新
- 服务器管理模块新增管理IP重复性检查功能
  - 生命周期状态为"正常"的服务器管理IP不能重复
  - 新增时自动检查IP重复性，编辑时检查IP重复性（排除当前记录）
  - 前端实时验证，提供友好的错误提示
  - 新增后端API `/api/check_server_management_ip` 用于IP重复性检查
- 虚拟机管理模块新增管理IP重复性检查功能
  - 生命周期状态为"正常"的虚拟机管理IP不能重复
  - 新增时自动检查IP重复性，编辑时检查IP重复性（排除当前记录）
  - 前端实时验证，提供友好的错误提示
  - 新增后端API `/api/check_vm_management_ip` 用于IP重复性检查

### 优化
- 改进服务器管理表单验证逻辑，提升用户体验
- 使用LEFT JOIN关联数据字典表获取准确的生命周期状态
- 兼容直接存储状态值和数据字典存储的情况
- 优化创建人和更新人逻辑，统一使用当前登录用户的username
- 改进删除API的SQL参数化查询，提升安全性

### 修复
- 修复删除服务器管理记录时SQL注入风险问题
- 修复管理IP重复性检查逻辑错误：现在只有当前要添加/编辑的资源生命周期状态为"正常"时，才进行IP重复性检查
- 修复了非"正常"状态的资源无法添加重复IP的问题

## [*******] - 2025年8月5日

### 更新
- 

### 优化
- 

### 修复
- **🐛 修复Vue前端警告问题**：全面修复前端Vue组件中的各种警告，提升开发体验和代码质量
  - **修复响应式对象警告**：解决 `global_search.vue` 中 `Search` 组件被错误转换为响应式对象的问题
    - 使用 `markRaw()` API 避免组件被响应式包装，消除性能开销警告
    - 修复模板中的组件引用，从 `:prefix-icon="Search"` 改为 `:prefix-icon="SearchIcon"`
  - **修复ElTag type属性警告**：解决多个页面中 `ElTag` 组件接收空字符串导致的类型验证失败
    - 修复文件：`cmdb_device_management.vue`、`cmdb_server_management.vue`、`cmdb_vm_registry.vue`、`cmdb_application_system_info.vue`、`cmdb_system_admin_responsibility_company.vue`
    - 将 `getLifecycleTagType` 函数中返回的空字符串 `''` 改为有效的 `'info'` 类型
    - 修复 `getOperationStatusColor` 函数中的空字符串返回值
    - 确保所有标签类型符合Element Plus规范：`'success'`, `'info'`, `'warning'`, `'danger'`
  - **修复Element Plus按钮废弃警告**：解决 `type="text"` 即将在3.0.0版本中废弃的问题
    - 修复文件：`cmdb_schedule_task_detail.vue`、`cmdb_discovery_cleanup.vue`
    - 将所有 `type="text"` 按钮改为使用 `link` 属性
    - 修复前：`<el-button type="text">` → 修复后：`<el-button link>`
    - 保持原有的链接样式视觉效果，为Element Plus 3.0.0升级做好准备
- **🎯 技术要点总结**：
  - 使用 `markRaw()` 标记不需要响应式的组件对象，避免不必要的性能开销
  - Element Plus标签组件的 `type` 属性只接受特定的有效值，不能使用空字符串
  - Element Plus按钮的 `link` 属性是替代 `type="text"` 的现代化方案
  - 所有修复都保持了原有的视觉效果和功能，只是使用了更规范的API

## [2.2.4.3] - 2025年8月4日

### 新增
- **� 虚变更管理"未规范"查询条件**：在ops_change_management页面新增"未规范"筛选条件，快速识别需要补充附件的变更记录
  - **筛选逻辑**：选择"是"时显示未上传OA流程或未上传签字存档的变更记录（OR逻辑）
  - **默认设置**：页面加载时默认选择"未规范"为"是"，直接显示需要处理的记录
  - **位置优化**：筛选条件放置在"变更实施人"之后，"OA流程"之前，便于用户快速使用
  - **重置保持**：重置搜索时保持默认值为"是"，符合用户使用习惯
  - **后端支持**：完整的后端API支持，使用数据库OR条件查询实现精准筛选

### 更新
- **🔧 虚拟机注册表编辑功能修复**：解决虚拟机注册表页面编辑生命周期状态后数据无变化的问题
  - **问题根因**：数据库视图返回的是显示名称（如"正常"），但后端期望接收字典代码（如"D00035"）
  - **数据转换修复**：添加 `getOperationStatusCode` 方法，实现显示名称到字典代码的正确转换
  - **数据提交修复**：在 `submitEdit` 方法中添加缺失的 `operation_status` 字段提交
  - **编辑逻辑优化**：修复 `handleEdit` 方法中的数据映射，确保编辑时正确加载当前状态

### 优化
- **🎨 生命周期状态颜色统一**：统一虚拟机注册表、服务器管理、设备管理三个页面的生命周期颜色显示
  - **颜色方案标准化**：基于5个生命周期状态（正常、故障、闲置、报废、预报废）制定统一颜色方案
    - 🟢 **正常**：绿色 (success) - 设备正常运行
    - 🔴 **故障**：红色 (danger) - 设备出现故障，需要立即处理
    - 🔵 **闲置**：蓝色 (info) - 设备暂时不使用但仍可用
    - ⚫ **报废**：灰色 ('') - 设备已报废，不再使用
    - 🟠 **预报废**：橙色 (warning) - 设备即将报废，处于过渡状态
  - **智能兼容性**：支持包含关键词的其他状态表述自动识别
  - **视觉一致性**：三个页面的生命周期状态显示完全统一，提升用户体验

### 修复
- **🐛 修复虚拟机编辑数据不更新问题**：解决点击编辑选择生命周期状态后数据库数据不变更的核心问题
  - 修复数据转换逻辑：编辑时正确将显示名称转换为数据库存储的字典代码
  - 修复数据提交逻辑：确保 `operation_status` 字段正确包含在更新请求中
  - 修复数据映射逻辑：编辑对话框正确显示当前选中的生命周期状态
- **🔧 修复CORS跨域请求问题**：解决前端访问后端API时的跨域请求被阻止问题
  - **问题原因**：后端CORS配置中缺少浏览器自动发送的缓存控制相关请求头
  - **解决方案**：在后端`.env`文件中更新CORS_HEADERS配置，添加Cache-Control、Pragma等常见HTTP缓存头
  - **配置优化**：支持Cache-Control、Pragma、Expires、If-Modified-Since、If-None-Match等请求头
  - **兼容性提升**：确保浏览器在处理HTTP缓存时发送的所有请求头都被正确允许

## [2.2.4.2] - 2025年8月1日

### 新增
- **🔄 虚拟机登记表生命周期字段**：为 `cmdb_vm_registry` 表添加生命周期管理功能
  - **数据库更新**：执行迁移脚本 `sql/2.2.4.2/add_vm_registry_lifecycle_field.sql`
    - 添加 `operation_status` 字段（VARCHAR(50) NOT NULL），用于存储生命周期状态
    - 添加字段注释：生命周期（系统管理员填写）
  - **视图更新**：更新 `v_cmdb_vm_registry` 视图以包含生命周期字段
    - 通过数据字典关联显示中文名称
    - 更新视图注释和权限设置
  - **前端功能增强**：
    - 在新增和编辑对话框中添加生命周期选择器
    - 在搜索表单中添加生命周期筛选功能
    - 在数据表格中添加生命周期列，使用彩色标签显示状态
    - 添加 `getLifecycleTagType` 方法，根据状态显示不同颜色的标签
    - 实现表单验证，生命周期为必填字段
  - **后端API更新**：
    - 更新 `/api/add_cmdb_vm_registry` 接口支持生命周期字段
    - 更新 `/api/update_cmdb_vm_registry` 接口支持生命周期字段
    - 更新 `/api/get_cmdb_vm_registry` 接口支持生命周期搜索和显示
    - 在查询参数中添加 `operation_status` 支持
  - **参考实现**：基于 `cmdb_server_management` 表的生命周期实现，保持功能一致性

### 优化
- **🎯 功能统一性**：虚拟机管理与服务器管理、设备管理的生命周期功能保持一致
- **📊 数据完整性**：确保所有虚拟机记录都具有明确的生命周期状态
- **🔍 搜索增强**：支持按生命周期状态筛选虚拟机，提高管理效率

## [*******] - 2025年7月31日

### 更新
- **🌐 互联网线路管理功能增强**：为互联网线路管理添加网关IP和防火墙IP字段支持
  - **新增字段**：添加"网关IP"和"防火墙IP"字段，支持IPv4和IPv6格式
  - **数据库更新**：执行迁移脚本 `sql/*******/add_gateway_firewall_ip_fields.sql`
    - 添加 `gateway_ip` 字段（VARCHAR(50)），支持网关IP地址存储
    - 添加 `firewall_ip` 字段（VARCHAR(50)），支持防火墙IP地址存储
    - 为新字段添加索引以提高查询性能
  - **前端界面更新**：
    - 在新增和编辑对话框中添加网关IP和防火墙IP输入框
    - 在数据表格中添加网关IP和防火墙IP显示列
    - 实现前端实时验证，支持IPv4和IPv6格式检查
  - **后端API增强**：
    - 更新添加、编辑、查询接口以支持新字段
    - 添加 `validateGatewayIp` 和 `validateFirewallIp` 验证函数
    - 完整的IPv4和IPv6格式验证逻辑

### 优化
- **🎨 按钮样式统一**：统一互联网线路管理页面的按钮风格
  - 参考 `cmdb_application_system_info` 页面的设计规范
  - 使用统一的颜色方案：新增(绿色)、查询(蓝色)、导出(灰色)、编辑(橙色)、删除(红色)
  - 添加图标支持：Plus、Search、Download图标
  - 实现响应式布局，支持不同屏幕尺寸
  - 修复表格操作按钮鼠标悬停时的移动问题
- **📝 IP地址段功能完善**：继续完善IP地址段功能
  - 修复前端更新功能的400错误问题
  - 解决前后端数据映射不一致的问题
  - 统一前端验证规则与后端验证逻辑
- **🗑️ 界面简化**：移除批量删除功能，简化用户界面
  - 移除批量删除按钮和相关逻辑
  - 移除表格选择列，专注于单个线路管理
  - 优化表格布局，提供更清晰的操作体验

### 修复
- **🐛 修复前端更新功能错误**：解决互联网线路更新时的400错误
  - **问题根因**：前端发送代码值（如 `inet_telecom`），但后端验证期望中文名称（如 `中国电信`）
  - **解决方案**：更新前端API调用，使用 `getProviderName()` 和 `getLineTypeName()` 转换数据格式
  - **验证逻辑统一**：统一 `backend/services/validation.js` 和 `backend/services/internet_line_validation.js` 中的IP验证逻辑
- **🔧 修复图标显示问题**：解决按钮图标不显示的问题
  - 正确注册图标组件：Plus、Search、Download
  - 在组件的 `components` 选项中添加图标引用
- **✅ 验证功能增强**：完善IP地址验证功能
  - 扩展 `validateCIDR` 函数以支持IPv6格式
  - 添加网关IP和防火墙IP的专用验证函数
  - 提供清晰的错误提示信息

## [*******] - 2025年7月29日

### 更新
- 新增互联网线路管理
- 新增互联网IP映射管理

### 优化
- 

### 修复
- 

## [********] - 2025年7月28日

### 更新
- **🔧 生产环境配置优化**：完善生产环境的配置文件和部署脚本
  - 更新后端 `.env_pro` 配置，修正 BASE_URL 为 `https://dop.cjfco.com.cn/`
  - 启用 FILE_URL_PREFIX 配置，支持 HTTPS 文件服务
  - 优化前端生产环境变量配置，添加 `VITE_APP_BASE_API` 支持

### 优化
- **📁 文件上传功能全面优化**：提升变更模板管理的文件上传和覆盖上传功能
  - **环境变量配置优化**：修复开发环境和生产环境的 API 基础 URL 配置不匹配问题
    - 开发环境：添加 `VITE_APP_BASE_API='http://localhost:3000'` 配置
    - 生产环境：添加 `VITE_APP_BASE_API=''` 配置，使用相对路径
  - **Nginx 配置优化**：完善生产环境的反向代理和文件服务配置
    - 修复 HTTPS (443端口) API 代理配置，确保正确指向后端服务器 `127.0.0.1:3000`
    - 添加 `/files/` 路径的静态文件服务支持，支持中文文件名
    - 优化文件上传大小限制和超时设置，支持最大 50MB 文件上传
  - **代码质量优化**：清理前端代码中的重复参数和冗余配置
    - 移除上传和覆盖上传功能中重复的 `username` 参数
    - 优化文件上传的错误处理和用户提示
  - **诊断工具增强**：添加多个环境诊断和故障排查脚本
    - `debug-production.js`：生产环境专用诊断脚本
    - `analyze-env-diff.js`：开发与生产环境差异分析
    - `fix-production-issue.sh`：自动化生产环境问题修复脚本

### 修复
- **🐛 修复覆盖上传模板 500 错误**：解决生产环境中覆盖上传变更模板功能报错的问题
  - **根本原因分析**：生产环境 HTTPS 请求的 API 代理配置错误，请求被错误地代理到前端服务器而非后端 API 服务器
  - **Nginx 配置修复**：修正 443 端口服务器配置中的 API 代理目标
    - 修复前：`proxy_pass http://cmdb-web/api/;` (错误指向前端服务器)
    - 修复后：`proxy_pass http://127.0.0.1:3000/api/;` (正确指向后端 API 服务器)
  - **CORS 配置完善**：为 HTTPS API 代理添加完整的跨域请求支持
  - **文件服务配置**：添加 `/files/` 路径的静态文件访问支持，确保上传文件可正常访问
- **🔧 修复环境变量配置问题**：解决前端请求工具无法正确获取 API 基础 URL 的问题
  - 前端 `request.js` 使用 `VITE_APP_BASE_API` 环境变量
  - 但配置文件中只定义了 `VITE_API_BASE_URL`，导致变量名不匹配
  - 统一环境变量命名，确保开发和生产环境都能正确加载 API 配置
- **📂 修复文件上传目录权限问题**：确保生产环境上传目录的正确创建和权限设置
  - 自动创建必要的上传目录结构
  - 设置正确的文件和目录权限 (775)
  - 支持按年月组织的子目录结构

## [2.2.3.13] - 2025年7月24日

### 更新
- **🧹 代码清理优化**：删除未使用的 `monitoringRequirementMixin.js` 文件，该文件未被任何地方引用且功能已在具体组件中实现
- **🔍 应用系统信息管理搜索功能增强**：在搜索栏中新增"系统管理员"搜索条件，放置在"系统分级"搜索条件前面
  - 支持下拉选择和筛选功能，数据源使用用户列表
  - 前端添加搜索字段和重置逻辑
  - 后端API增加 `system_administrator` 参数支持，使用模糊匹配查询
  - 同时修改主查询和总记录数查询的SQL语句，确保搜索功能完整可用

### 优化
- **📂 项目结构优化**：移除冗余的mixin文件，保持代码库整洁
- **🔍 搜索体验优化**：系统管理员搜索条件支持可筛选和可清空功能，提升用户体验
- **🎯 搜索逻辑优化**：使用LIKE模糊匹配，与其他管理员搜索条件保持一致的实现方式

### 修复
- **🐛 修复系统管理员筛选无效问题**：解决选择系统管理员后无法查询到对应数据的问题
  - 根本原因：后端API缺少对 `system_administrator` 参数的处理
  - 修复方案：在后端添加参数提取、SQL查询条件和参数数组处理
  - 确保前后端数据传递和查询逻辑完整匹配

## [2.2.3.12] - 2025年7月23日

### 更新
- 变更详情的附件预览可以支持excel在线预览了

### 优化
- **🎨 变更管理附件预览界面优化**：全面提升变更详情中附件预览功能的用户体验
  - **预览对话框按钮美化**：重新设计右上角操作按钮，采用现代化圆角设计和颜色编码
    - 全屏/退出全屏按钮：绿色主题 (#67c23a)，图标尺寸18px，添加悬停效果
    - 关闭预览按钮：红色主题 (#f56c6c)，独立设计，功能更直观
    - 移除Element Plus默认关闭按钮，避免重复显示
  - **图片查看器工具栏优化**：重新设计图片查看器的工具栏按钮布局和样式
    - 分组设计：缩放控制组和操作控制组分离，布局更清晰
    - 毛玻璃效果：工具栏背景使用 `backdrop-filter: blur(10px)` 实现现代感
    - 按钮颜色系统：缩放显示(蓝色)、下载(绿色)、关闭(红色)，功能区分明确
    - 悬停动画：添加平滑的变换和阴影效果，使用 `cubic-bezier(0.4, 0, 0.2, 1)` 缓动函数
  - **工具提示增强**：为所有操作按钮添加 `el-tooltip` 组件，提供清晰的功能说明
  - **响应式设计优化**：
    - 移动端按钮尺寸调整：32px × 32px，适合触摸操作
    - 小屏幕下隐藏按钮文字，只显示图标，节省空间
    - 工具栏采用垂直布局，更适合移动端使用
  - **视觉效果提升**：
    - 统一使用6-8px圆角，符合现代设计趋势
    - 添加适当的阴影和过渡动画，增强立体感
    - 按钮状态反馈：悬停时轻微上移和阴影变化
    - 禁用状态样式优化，视觉反馈更清晰

### 修复
- **🐛 修复预览对话框重复关闭按钮问题**：通过设置 `:show-close="false"` 隐藏Element Plus默认关闭按钮，避免界面上出现两个关闭按钮的问题

## [********] - 2025年7月22日

### 更新
- **🧹 新增发现结果清理功能**：为自动发现模块添加过期数据清理功能，提升系统性能和数据管理效率
  - **自动清理机制**：系统每天凌晨2点自动清理超过1天的发现结果数据（可配置）
  - **手动清理功能**：支持用户手动触发清理操作，可自定义过期天数（1-365天）
  - **数据预览功能**：清理前可预览将要清理的数据，包括IP地址、主机名、设备类型、发现时间等
  - **清理历史记录**：完整记录所有清理操作的历史，包括操作人、操作时间、清理数量等
  - **统计信息展示**：实时显示过期数据、有效数据、已清理数据、总数据量等统计信息
  - **配置管理界面**：支持自定义清理参数，包括启用/禁用自动清理、默认过期天数、执行时间等
  - **安全机制**：采用软删除方式（del_flag=1），清理前二次确认，所有操作记录日志
  - **导航菜单集成**：在"自动发现"子菜单下新增"发现结果清理"选项
  - **权限控制**：基于用户权限的访问控制，确保只有授权用户可执行清理操作
  - **API接口完整**：提供完整的REST API接口，支持统计查询、执行清理、配置管理等
  - **响应式设计**：清理管理页面支持PC和移动端访问，界面美观易用

### 优化
- **🎯 定时任务优化**：使用node-cron实现稳定的定时清理任务，支持Cron表达式自定义执行时间
- **📊 数据可视化优化**：清理管理页面采用卡片式统计展示，直观显示各类数据状态
- **🔧 系统集成优化**：清理服务在系统启动时自动启动，无需手动配置
- **💾 数据库性能优化**：清理操作使用批量处理，避免对系统性能造成影响
- **🎨 用户界面优化**：采用Element Plus组件库，界面风格与系统其他页面保持一致

### 修复
- **🐛 修复发现结果数据累积问题**：解决长期运行导致的发现结果数据过多影响系统性能的问题
- **🔧 修复数据查询效率问题**：通过定期清理过期数据，提升发现结果查询和展示的响应速度

## [2.2.3.10] - 2025年7月15日

### 更新

- 更新了部分cmdb_application_system_info表的字段名称

### 优化

- **🔒 表单对话框关闭逻辑统一优化**：对所有页面的表单对话框应用统一的关闭控制策略
  - **防误关闭机制**：为所有el-dialog表单对话框添加 `:close-on-click-modal="false"`和 `:close-on-press-escape="false"`属性
  - **覆盖范围**：包括所有CMDB相关页面的新增、编辑、删除确认对话框
    - cmdb_application_system_info.vue - 应用系统信息管理
    - cmdb_vm_registry.vue - 虚拟机登记管理
    - cmdb_device_management.vue - 网络设备管理
    - cmdb_data_dictionary.vue - 数据字典管理
    - cmdb_discovery_results.vue - 发现结果管理
    - cmdb_discovery_tasks.vue - 发现任务管理
    - cmdb_host_scan_results.vue - 主机扫描结果
    - cmdb_issue_collection.vue - 需求问题记录
    - cmdb_monitored_ip_list.vue - 监控IP列表
    - cmdb_page_permissions.vue - 页面权限管理
    - cmdb_schedule_tasks.vue - 调度任务管理
    - cmdb_schedule_task_detail.vue - 调度任务详情
    - cmdb_server_management.vue - 服务器管理
    - cmdb_system_admin_responsibility.vue - 系统管理员责任表
    - cmdb_system_admin_responsibility_company.vue - 公司系统管理员责任表
    - home.vue - 首页密码修改对话框
    - ops_calendar.vue - 运维日历相关对话框
    - report_server_age.vue - 服务器使用年限报表
    - SidebarVersionInfo.vue - 版本信息组件（排除，不做修改）
  - **用户体验改进**：防止用户误操作导致已填写的表单数据丢失，只能通过明确的按钮操作关闭表单
  - **操作一致性**：确保所有表单对话框具有统一的交互行为和用户体验

### 修复

## [*******] - 2025年6月30日

### 更新

- **📋 事件记录操作日志功能增强**：将事件管理的增删改操作记录到用户日志中，实现完整的操作审计
  - **操作日志记录**：为事件管理的新增、更新、删除操作添加用户日志记录功能
    - 新增事件记录时：记录事件编号、标题、系统、优先级等关键信息
    - 更新事件记录时：记录修改的事件编号、标题、系统等信息
    - 删除事件记录时：记录被删除的事件编号、标题、系统信息
  - **用户日志视图优化**：更新用户日志视图以支持事件管理操作的特殊显示
    - 添加事件管理操作的专门显示逻辑，格式化为易读的操作描述
    - 显示格式：`用户名 + 操作动作 + [事件编号] + 事件标题`
    - 示例：`admin新增了事件记录 [SJ-20250102-001] 服务器宕机处理`
  - **操作类型标识**：为不同类型的事件操作分配不同的显示标签
    - 新增操作：显示为"primary"类型标签
    - 更新操作：显示为"success"类型标签
    - 删除操作：显示为"warning"类型标签
  - **数据库结构优化**：为事件管理操作在用户日志表中创建结构化存储
    - 记录操作方法（POST）、API地址、详细参数、用户名、操作类型
    - 支持完整的操作追溯和审计功能
  - **错误处理机制**：日志记录失败不影响主要业务功能的正常执行
- **🔧 IT资产管理操作日志功能增强**：为IT资产管理的增删改操作添加详细的用户日志记录
  - **详细操作日志记录**：为IT资产管理的新增、更新和删除操作添加结构化日志记录功能
    - 网络设备管理：记录管理IP、主机名、功能用途、管理员、设备类型、数据中心等关键信息
    - 实体服务器管理：记录管理IP、主机名、功能用途、管理员、服务器类型、数据中心等关键信息
    - 虚拟机登记：记录管理IP、主机名、功能用途、管理员、宿主机IP、数据中心等关键信息
    - 应用系统信息：记录管理IP、主机名、功能用途、服务器管理员、业务系统名称、数据中心等关键信息
  - **用户日志视图优化**：更新用户日志视图以支持IT资产管理操作的特殊显示
    - 添加IT资产管理操作的专门显示逻辑，格式化为易读的操作描述
    - 显示格式：`用户名 + 操作动作 + [管理IP] + 主机名 + 功能用途`
    - 新增示例：`admin新增了实体服务器 [*************] web-server-01 - Web服务器`
    - 更新示例：`admin更新了实体服务器 [*************] web-server-01 - Web服务器`
    - 删除示例：`admin删除了实体服务器 [*************] web-server-01 - Web服务器`
    - 智能处理缺失信息：当主机名或功能用途为空时显示合适的默认文本
    - 支持应用系统信息的业务系统名称显示
  - **操作审计追踪**：完整记录IT资产管理的增删改操作，便于问题排查和合规审计
    - 记录操作的关键字段信息
    - 支持按用户、时间、资产类型等维度进行操作历史查询
    - 为IT资产变更提供完整的审计轨迹
    - 覆盖新增、更新、删除三种操作类型
- **📊 变更管理默认排序优化**：将变更管理页面的默认排序改为按计划变更时间降序排序
  - 数据默认按照计划变更时间从最新到最早排序
  - 页面首次加载和重置搜索后都按此规则排序
  - 便于用户快速查看即将到来的变更计划

### 优化

- **🧹 用户日志重复记录优化**：优化用户日志视图，排除已有详细记录的操作的通用表注释记录，避免重复记录，提升日志查看体验
  - 对于已有详细记录的操作（变更管理、事件管理、IT资产管理、值班排班），排除通用的"用户名 更新了 xxx表数据"记录
  - 避免详细记录和通用记录的重复显示，保持日志记录的简洁性和有效性
  - 用户现在只会看到有实际意义的详细日志记录，提升操作追溯的准确性
- ✨优化消息推送页面搜索栏布局

### 修复

## [2.2.3.8] - 2025年6月27日

### 更新

- **✨ 消息模板变量功能增强**：优化变更通知推送中的变量处理，提供更丰富和实用的信息
  - **变更人员微信@功能**：从cmdb_users表获取用户的wechat_id和real_name字段，实现智能用户显示
    - 支持逗号分隔的多个用户名处理
    - 优先级处理逻辑：wechat_id > real_name > username
    - 微信ID存在时：自动生成@{wechat_id}格式
    - 微信ID为空时：使用real_name（真实姓名）字段
    - 最终降级处理：当以上都为空时使用username
    - 示例1：`jinyh1,zhuangzx` → `@jinyh1_wechat @zhuangzx_wechat`（有微信ID）
    - 示例2：`yanzx1,wangzg5` → `严子旭 王正刚`（无微信ID但有真实姓名）
    - 示例3：`jinyh1,yanzx1` → `@jinyh1_wechat 严子旭`（混合情况）
  - **变更级别名称转换**：从cmdb_data_dictionary表获取dict_code对应的dict_name
    - 自动将字典代码转换为友好的名称显示
    - 降级处理机制：当找不到对应名称时显示原代码
    - 示例：`P00002` → `二级变更`
  - **模板变量优化**：
    - `${change_personnel}`：显示带微信@的实施人员信息
    - `${change_level}`：显示变更级别名称而非代码
    - `${planned_time}`：只显示日期格式YYYY/MM/DD，不显示具体时间
    - 保持其他变量不变：change_id、change_name、change_system
  - **数据库关联查询**：使用高效的SQL查询获取关联信息，确保性能
  - **错误处理机制**：完善的异常处理，确保推送功能稳定性
- **📋 推送任务过滤条件优化**：优化推送任务中的过滤条件选项，提供更丰富的筛选功能
  - **变更级别优化**：从数据字典（dict_type='P'）获取变更级别选项，显示友好名称
    - 支持的级别：一级变更、二级变更、三级变更、紧急变更
    - 多选支持：可同时选择多个变更级别进行过滤
    - 显示格式：P00001显示为"一级变更"，提升用户体验
  - **变更系统优化**：从系统责任视图获取变更系统选项，支持多选功能
    - 数据源：v_cmdb_system_admin_responsibility_company视图
    - 字段：system_abbreviation系统缩写
    - 功能：支持多选、搜索过滤、折叠标签显示
    - 覆盖：150+个系统选项，包含所有主要业务系统
  - **UI交互增强**：
    - 多选控件：支持collapse-tags折叠显示，tooltip提示
    - 搜索功能：变更系统支持实时搜索过滤
    - 帮助提示：每个字段都有清晰的使用说明
    - 响应式设计：适配不同屏幕尺寸
  - **后端API增强**：
    - 新增get_change_level_options接口获取变更级别选项
    - 新增get_change_system_options接口获取变更系统选项
    - 数据缓存优化，提升加载性能
- **🔔 优化变更通知推送业务逻辑**：重构变更通知推送机制，实现更精准的当日变更推送
  - **查询逻辑优化**：修改SQL查询条件从 `planned_start_time >= CURRENT_TIMESTAMP`改为 `DATE(planned_start_time) = CURRENT_DATE`
  - **推送范围调整**：只推送当日计划的变更，不再推送未来所有变更
  - **数量限制移除**：移除 `LIMIT 10`限制，推送当日所有变更计划
  - **空数据处理**：当日无变更时返回成功状态，而不是错误状态
  - **日志记录增强**：添加详细的执行日志，记录变更数量和执行状态
  - **执行方式说明**：
    - 手动执行：立即推送当日变更
    - 自动调度：按设定的调度值时间推送当日变更
    - 多个变更：依次推送多条消息，确保每个变更都有独立通知
  - **业务逻辑改进**：符合实际运维场景，避免重复推送和无关推送
  - **界面优化**：移除过滤条件中的"提前通知时间"设置，简化配置流程
  - **🔧 修复数据库字段映射错误**：解决变更通知推送中的字段名不匹配问题
    - **错误原因**：推送逻辑中使用了不存在的数据库字段名
    - **字段修复**：
      - `change_name` → `title` (变更名称字段)
      - `planned_start_time` → `planned_change_time` (计划变更时间字段)
    - **SQL查询修复**：更新WHERE条件和ORDER BY子句中的字段名
    - **变量映射修复**：更新模板变量替换中的字段引用
    - **基于实际表结构**：根据ops_change_management表的真实字段结构进行修复
- **🎉 新增推送任务定时调度器**：支持每分钟自动检查并执行到期任务

### 优化

### 修复

- **🔧 修复监控IP列表唯一约束SQL脚本语法错误**：解决PostgreSQL循环语法中缺少RECORD变量声明的问题
  - 修复DO块中FOR循环变量声明错误，添加 `duplicate_rec RECORD;`声明
  - 统一循环变量命名，确保PostgreSQL语法正确性
  - SQL脚本现在可以正常执行，成功为IP地址字段添加唯一约束和索引
- **🔧 修复Vue 3兼容性问题**：解决消息推送管理页面中 `this.$set is not a function`错误
  - 修复TaskManagement.vue中handleExecute方法的Vue 3兼容性
  - 修复HistoryManagement.vue中handleRetry方法的Vue 3兼容性
  - 将 `this.$set(obj, key, value)`替换为直接赋值 `obj.key = value`
  - 适配Vue 3的响应式系统，不再需要 `$set`方法
- **🐛 修复消息推送管理页面图标渲染问题**：解决Vue前端图标组件无法正确识别的问题
  - 修复TemplateManagement.vue中Search、Refresh、Plus、Edit、View、Delete等图标渲染错误
  - 修复TaskManagement.vue中Search、Refresh、Plus、Edit、Delete、VideoPlay图标渲染错误
  - 修复ConfigManagement.vue中Search、Refresh、Plus、Edit、Delete、Connection图标渲染错误
  - 修复HistoryManagement.vue中Search、Refresh、DataAnalysis、View、RefreshRight图标渲染错误
  - 为所有图标在computed属性中添加正确的返回函数，确保Vue能正确识别图标组件
  - 统一图标引用方式，避免直接在模板中使用导入的图标变量
- **🐛 修复消息推送配置数据获取失败问题**：解决新增推送配置后看不到记录的问题
  - 修复后端API中搜索条件处理逻辑，正确处理空字符串和null值
  - 确保只有有效的搜索条件才被添加到SQL查询中
  - 修复config_name和config_type空字符串导致查询结果为空的问题
  - 修复is_active为null时导致查询失败的问题
  - 添加字符串trim()处理，避免空白字符影响查询结果
- **🐛 修复消息推送任务页面数据不显示问题**：解决推送任务页面中数据无法显示的问题
  - **根本原因**：后端 `get_msg_push_tasks` API的搜索逻辑存在缺陷，错误处理空字符串和null值
  - **问题分析**：前端发送的搜索条件包含空字符串（如 `task_name: ''`、`task_type: ''`、`status: ''`、`is_active: null`）
  - **修复内容**：优化搜索条件判断逻辑
    - 对字符串字段添加 `.trim()`处理，只有非空且trim后不为空的值才作为搜索条件
    - 对 `is_active`字段同时检查 `!== undefined`和 `!== null`，避免null值被错误处理
    - 确保只有有效的搜索条件才被添加到SQL查询中
  - **测试验证**：修复后API测试通过，正确返回数据（dataCount: 1, total: 1）
- **🔧 修复消息推送管理页面axios配置问题**：解决"获取数据失败"的根本原因
  - **根本原因**：消息推送管理页面使用了错误的axios实例配置
  - **技术分析**：设备管理等其他页面使用 `this.$axios`（全局配置），消息推送页面使用 `import axios from '@/utils/request'`（独立配置）
  - **修复内容**：统一所有消息推送管理页面使用全局 `this.$axios`实例
    - ConfigManagement.vue：修复5个API调用
    - TaskManagement.vue：修复6个API调用
    - TemplateManagement.vue：修复3个API调用
    - HistoryManagement.vue：修复4个API调用
  - **配置差异**：全局axios包含正确的baseURL、认证token、拦截器等配置
  - **验证结果**：后端API接口测试完全正常，数据库有14条记录，问题确实出现在前端axios配置
- **🚨 修复推送任务调度值设置后无法自动执行的问题**：解决消息推送管理页面中"新增推送配置"保存失败的问题
  - 统一所有推送管理组件使用认证的axios实例（@/utils/request）替代原始axios
  - 解决API请求缺少JWT认证头导致的"未找到有效的token"错误
- **🎨 优化消息推送管理页面操作按钮布局**：改进所有推送管理页面的操作列按钮显示效果
  - **推送配置管理页面**：优化测试、编辑、删除按钮的布局，确保在一行中整齐显示
  - **推送任务管理页面**：优化执行、编辑、删除按钮的布局，增加操作列宽度至280px
  - **模板管理页面**：优化预览、编辑、删除按钮的布局，增加操作列宽度至280px
  - **历史管理页面**：优化详情、重试按钮的布局，增加操作列宽度至200px
  - **统一样式设计**：为所有操作按钮添加flex布局和8px间距，使用white-space: nowrap防止换行
  - **用户体验提升**：确保所有操作按钮在一行中显示，提高界面的整洁度和可用性

## [2.2.3.7] - 2025年6月26日

### 更新

- **📝 侧边栏菜单名称优化**：将"资产管理"菜单名称修改为"IT资产管理"，使功能描述更加准确和专业

### 优化

- **🎨 左侧侧边栏背景色优化**：将左侧侧边栏的背景色修改为与登录界面一致的渐变背景
- **✨ 侧边栏系统标题美化优化**：优化"IT资源管理系统"标题的视觉效果，提升整体界面品质
- **🎨 左侧边栏深蓝灰主题统一优化**：将整个左侧边栏背景统一调整为深蓝灰主色调，提升视觉一致性
- **🚀 左侧边栏科技感配色重构**：将边栏和菜单背景调整为更具科技感的深蓝黑色系，营造未来感氛围
- **🔧 阴影效果柔化优化**：调整侧边栏阴影效果，让视觉效果更加平缓自然
- **✨ Logo图标对比度优化**：解决logo图标在深蓝灰背景下不够突出的问题，提升品牌识别度
- **📐 Logo位置和尺寸微调优化**：调整logo图标的位置和大小，实现更佳的视觉平衡

### 修复

- **🐛 修复侧边栏折叠时系统标题显示问题**：解决折叠左侧边栏时"IT资源管理系统"文字没有完全隐藏的问题

## [*******] - 2025年6月26日

### 更新

### 优化

- **🧹 代码清理和优化**：清理项目中的测试代码和调试信息，提升代码质量
  - 删除临时测试文件：移除 `backend/test_wopi.js`和 `scripts/test-libreoffice-online.ps1`等测试脚本
  - 清理调试日志：移除前端页面中的调试console.log代码，包括VM注册、运维日历等页面
  - 移除测试数据：清理发现任务页面中的测试扫描相关表单和变量定义
  - 优化注释内容：将测试相关注释改为更规范的描述
  - 保留生产功能：确保所有清理不影响生产环境的正常功能

### 修复

## [*******] - 2025年6月25日

### 更新

- 📱  **暂时隐藏变更详情中，附件的预览按钮**
- **📋 应用系统信息管理新增监控需求字段**：为应用系统信息表添加监控需求相关字段，增强系统监控需求管理能力
  - 新增"监控需求"字段：值为"是"或"否"，必填项，默认为"是"
  - 新增"监控需求说明"字段：当监控需求为"否"时为必填项，支持多行文本输入
  - 字段位置：放置在"系统分级"字段后面，便于用户查看和填写
  - 搜索支持：在搜索条件中添加监控需求筛选功能
  - 表格显示：监控需求列采用颜色标签区分（绿色="是"，橙色="否"）
  - 表单验证：前后端双重验证，确保数据完整性和业务逻辑正确性
  - 智能联动：当监控需求选择"是"时，自动禁用说明输入框并清空内容
  - 数据存储：数据库存储布尔值，视图层转换为中文"是"/"否"显示

### 优化

- **数据库性能优化**：为监控需求字段添加索引，提升查询效率
- **用户体验优化**：表单字段智能联动，减少用户不必要的操作
- **业务逻辑优化**：条件必填验证，确保监控需求说明的完整性

### 修复

- **🐛 修复监控需求说明字段红色星号显示问题**：解决当监控需求为"否"时，监控需求说明字段无法显示红色必填标识的问题
  - 修复服务器管理页面新增和编辑表单中监控需求说明字段的 `:required`属性
  - 修复虚拟机管理页面新增和编辑表单中监控需求说明字段的 `:required`属性
  - 统一表单验证显示逻辑：当监控需求为"否"时，监控需求说明字段显示红色星号
  - 设备管理页面已正确实现该功能，无需修复
- **🔧 统一监控需求字段数据类型处理**：解决各页面监控需求字段数据类型不一致的问题
  - 后端API增强：支持同时接收字符串（"是"/"否"）和布尔值（true/false）两种格式
  - 应用系统信息管理页面：开始使用布尔值格式（true/false）
  - 服务器管理、虚拟机管理页面：暂时保持字符串格式，后续将逐步统一
  - 设备管理页面：已使用布尔值格式，无需修改
  - 数据库存储统一：所有表都使用布尔值存储，视图层转换为中文显示
- **🔐 优化JWT Token过期处理机制**：改进用户认证体验，减少因token过期导致的操作中断
  - 后端：增强token过期错误处理，返回更具体的错误信息和状态码
  - 前端：改进响应拦截器，更好地处理token过期情况，提供友好的用户提示
  - 配置：将JWT过期时间从6小时延长到8小时，前端预警时间调整为7小时
  - 新增：token刷新接口，支持在token过期后24小时内进行自动刷新（可选功能）
  - 安全：保持原有的安全级别，token过期后自动跳转到登录页面

## [2.2.3.4] - 2025年6月20日

### 更新

- **🔧 Office文档预览方案重构**：优化LibreOffice Online集成，移除外部在线服务依赖
- **🎯 事件记录模板样例布局重构**：将事件详情中的事件记录模板样例从折叠选择改为竖向平铺展示
- **📝 事件记录模板内容简化优化**：对事件记录模板内容进行简化，移除冗余条目，提高实用性
- **🔧 事件记录模板内容统一管理重构**：解决模板内容维护困难的问题，实现统一管理

### 优化

- **用户体验优化**：事件模板查看更加直观，无需切换即可查看所有模板内容
- **维护效率提升**：模板内容统一管理，维护成本大幅降低
- **代码结构优化**：模板内容集中管理，代码结构更清晰，扩展性更好
- **响应式设计**：模板展示在移动端友好，自动适配屏幕尺寸

### 修复

## [2.2.3.3] - 2025年6月19日

### 更新

- **📱 新增附件在线预览功能**：在变更详情页面的附件管理中添加在线预览功能
  - 支持PDF文件的直接在线预览，使用iframe嵌入显示
  - 支持图片文件（JPG、JPEG、PNG、GIF）的在线预览，自动适配尺寸
  - Word和Excel文档显示友好提示信息，引导用户下载后查看
  - 新增预览按钮，采用蓝色info类型按钮，与下载、上传、删除按钮形成完整操作组
  - 预览对话框支持80%宽度显示，提供良好的预览体验
  - 自动检测文件类型，根据不同格式提供相应的预览或下载建议
  - 优化移动端显示，预览按钮在小屏幕下正确换行显示

### 优化

### 修复

## [2.2.3.2] - 2025年6月18日

### 更新

- **🚮 移除事件管理关闭时间字段**：从事件管理界面中移除"关闭时间"字段显示，简化事件管理流程

  - 移除事件列表页面中的关闭时间列
  - 移除事件详情页面中的关闭时间输入框
  - 移除后端API返回和处理中的close_time字段
  - 移除Word导出中的关闭时间字段
  - 数据库中的close_time字段予以保留
- **🚮 移除事件管理事件状态字段**：从事件管理界面中移除"事件状态"字段显示，进一步简化界面

  - 移除事件列表页面中的事件状态搜索条件和显示列
  - 移除事件详情页面中的事件状态输入框和验证规则
  - 移除后端API返回和处理中的status字段
  - 移除Word导出中的事件状态字段，优化表格布局
  - 数据库中的status字段予以保留
- **📝 事件管理字段名称优化**：将"发生时间"字段名称改为"首次发生时间"，更准确地描述时间字段的含义

  - 更新事件列表页面表头显示
  - 更新事件详情页面表单标签
  - 更新Word导出中的字段标题
  - 更新事件模板示例中的时间描述

### 优化

- **界面简化**：精简事件管理界面，提升用户操作体验
- **数据处理优化**：减少不必要的字段处理，提高系统性能
- **字段语义优化**：改进字段命名，提升业务理解的准确性
- **🎨 事件管理附件功能布局优化**：重新设计事件详情页面的附件管理区域布局

  - 将"事件分析报告附件上传"和"下载事件分析报告模板"功能移动到页面底部
  - 采用左右分栏布局：左侧为附件上传功能，右侧为模板下载功能
  - 优化视觉设计：左侧面板采用灰色背景，右侧面板采用白色边框设计
  - 改进用户体验：功能分区更加清晰，操作流程更加直观
  - 添加响应式设计：移动端自动调整为垂直布局
- **📐 附件管理区域紧凑化优化**：优化附件管理区域的尺寸和布局，提升页面空间利用率

  - 减少面板最小高度：从400px降至250px，移动端从300px降至200px
  - 压缩内边距和间距：优化各元素的间距，让布局更紧凑
  - 缩小上传区域高度：拖拽上传区域从150px减至120px
  - 优化字体和图标尺寸：适当调整文字和图标大小，保持视觉平衡
  - 精简文件列表显示：优化已上传文件的显示密度
- **✨ 已上传文件显示效果优化**：显著提升已上传文件的视觉突出效果，增强用户体验

  - **背景色优化**：文件列表区域使用淡蓝色背景，文件项使用白色渐变背景
  - **视觉元素增强**：为每个文件项添加左侧蓝色边条和圆形图标背景
  - **交互效果**：文件项支持悬停上浮动画和阴影变化效果
  - **颜色主题统一**：使用蓝色主题色系，提升整体视觉一致性
  - **标题强化**：文件列表标题使用深蓝色和下划线，更加醒目
  - **按钮优化**：文件操作按钮使用自定义样式，提升点击体验
- **🔳 已上传文件卡片边框效果增强**：为已上传文件卡片添加更突出的边框效果，使其更加醒目

  - **移除外层边框**：去掉最外层的蓝色边框和阴影，保持简洁
  - **内部效果保留**：保留文件项的卡片效果、左侧边条和悬停动画
  - **下载按钮绿色主题**：下载按钮使用绿色渐变背景和白色字体，更符合下载操作的视觉习惯
  - **视觉层次优化**：通过内部元素的颜色和效果提升文件卡片的视觉重要性
  - **保持模板区域简洁**：下载模板区域保持原有简洁设计，形成对比突出重点

### 修复

- **🐛 修复文件名重复事件编号问题**：解决刷新页面后文件名显示重复事件编号的问题（如：SJ-20250616-0001_SJ-20250616-0001_分析报告.xlsx）
- - 添加文件名前缀检查逻辑，避免重复添加事件编号
  - 只有当原始文件名不包含事件编号时才添加事件编号前缀
  - 确保文件显示名称的唯一性和可读性

## [2.2.3.1] - 2025年6月17日

### 修复

- **🐛 修复用户管理admin权限问题**
  - 修复admin用户登录后只能看到自己用户信息的问题
  - 解决axios请求拦截器中字段名冲突导致的搜索条件被覆盖问题
  - 将拦截器中的 `username` 字段改为 `loginUsername`，避免与前端搜索条件冲突
  - 现在admin用户可以正常查看和管理所有用户信息

### 技术改进

- **代码清理**：删除所有调试代码，保持代码简洁性
- **权限验证优化**：改进前后端权限验证逻辑，确保admin用户拥有完整管理权限
- **请求拦截器优化**：修复axios拦截器中的字段命名冲突问题

### 新增功能

- **🎯 变更模板管理新增"是否默认"标志功能**
  - 在变更模板管理页面新增"是否默认"列，直观显示哪个模板被设为默认
  - 新增"设为默认"操作按钮，支持快速设置任意模板为默认模板
  - 上传新模板时支持直接设置为默认模板的开关
  - 确保系统中只能有一个默认模板，设置新默认时自动清除原有默认状态
  - 变更详情页面的"变更操作表模板下载"现在会自动选择默认模板
  - 模板选择下拉框中会显示默认标识，方便用户识别
  - 默认模板具有删除保护，不能被意外删除

### 技术改进

- **数据库优化**：为变更模板表添加 `is_default` 字段和相应索引
- **事务安全**：设置默认模板时使用数据库事务确保数据一致性
- **后端API增强**：新增设置默认模板和获取默认模板的API接口
- **前端用户体验**：优化表格布局，添加默认状态可视化标识
- **权限保护**：实现默认模板的删除保护机制，防止误操作

### 修复

- **🐛 修复消息推送配置保存失败问题**：解决前端保存推送配置时"未找到有效的token"错误
- **🔧 修复前端认证头配置问题**：修正所有消息推送管理页面的axios导入，使用带认证拦截器的axios实例
- **🛡️ 修复API认证中间件问题**：解决推送配置API需要JWT认证但前端未正确发送认证头的问题
- **🚨 修复表单验证器错误警告**：解决wechat_webhook等字段验证时出现的debugWarn错误，加强验证器错误处理机制
- **⚠️ 修复TemplateManagement组件Delete图标警告**：解决"Property Delete was accessed during render but is not defined"的Vue警告，通过computed属性确保图标组件正确引用
- **⚠️ 修复HistoryManagement组件RefreshRight图标警告**：解决"Property RefreshRight was accessed during render but is not defined"的Vue警告，使用computed属性确保图标组件正确引用
- **表单验证规则优化**：修改表单验证逻辑，使其根据选择的推送类型动态验证对应的必填字段
- **推送类型切换优化**：优化推送类型切换时的字段清理和验证重置逻辑，提升用户体验
- **HTML结构修复**：修正表单中el-row和el-col标签的嵌套结构，消除页面渲染警告
- 🔧 修复消息推送管理功能中"新增推送配置"保存失败的问题
  - 统一所有推送管理组件使用认证的axios实例（@/utils/request）替代原始axios
  - 解决API请求缺少JWT认证头导致的"未找到有效的token"错误
- ⚠️ 修复Vue组件图标引用警告问题
  - 修复TemplateManagement.vue中Delete图标引用问题
  - 修复HistoryManagement.vue中RefreshRight图标引用问题
  - 添加computed属性正确处理图标引用
- 📝 改进表单验证错误处理
  - 增强ConfigManagement.vue中的表单验证器错误捕获
  - 添加数据类型安全检查和null/undefined保护
  - 优化表单验证清除逻辑，使用$nextTick避免组件状态不一致
- 💬 **优化错误提示信息显示**
  - 改进所有API调用的错误处理逻辑，提供更详细的错误信息
  - 根据HTTP状态码提供针对性的错误提示（400、401、403、404、500等）
  - 在开发环境下输出详细的错误调试信息
  - 改进网络连接失败等特殊情况的错误提示

### 新增功能

- 🎯 **变更详情页面附件管理支持拖拽上传**
  - 所有附件卡片（OA流程、签字存档、变更操作表、补充资料）现在都支持拖拽文件上传
  - 拖拽悬停时显示视觉反馈和动画效果
  - 自动进行文件类型和大小验证
  - 支持单文件拖拽上传，多文件时自动提示
  - 上传中状态的视觉反馈
  - 已有文件时显示不同的视觉状态
  - 未上传文件时显示拖拽提示信息
  - 响应式设计，移动端友好

### 技术改进

- **环境变量配置统一**：修复环境变量命名不一致问题，将 `DB_DATABASE`统一改为 `DB_NAME`，确保API能正确连接到cmdb数据库
- **axios配置规范化**：统一所有消息推送管理页面使用 `@/utils/request`导入axios，确保请求自动携带认证头
- **数据库连接验证**：确认所有消息推送相关表已正确创建，数据库连接状态正常
- **动态验证机制**：使用自定义验证器根据config_type值动态决定哪些字段是必填的
- **字段联动清理**：切换推送类型时自动清空非当前类型的字段值，避免数据混乱
- **验证状态管理**：改进表单验证状态的清理和重置机制
- **验证器异常处理**：为所有表单验证器添加try-catch错误处理，防止验证过程中的异常导致页面警告
- **数据类型安全**：在验证器中添加数据类型检查和转换，确保处理各种边界情况
- 🔄 统一环境变量命名（DB_DATABASE改为DB_NAME）
- 🗃️ 增强API错误处理和日志记录
- 🧹 清理临时调试文件和冗余代码

### 文档更新

- 📖 更新README.md版本徽章
- 📝 完善CHANGELOG.md记录

## [2.2.3.0] - 2025年6月16日

### 更新

- **🎉 新增消息推送管理功能模块**：在系统管理菜单下新增"消息推送管理"功能，支持多平台消息推送和自动化通知
- **推送配置管理**：支持Webhook、邮件、钉钉、企业微信四种推送方式的配置管理，包括URL配置、认证信息、超时设置等
- **推送任务管理**：支持变更通知、事件通知和自定义通知三种任务类型，提供多种调度方式（手动、单次、每日、每周、每月、Cron表达式）
- **消息模板管理**：支持自定义消息模板，提供变量替换功能，支持文本、HTML、Markdown、JSON四种消息格式
- **推送历史管理**：记录所有推送历史，提供推送状态跟踪、重试机制和统计分析功能
- **变更信息推送集成**：支持与变更管理系统集成，自动推送变更编号、变更名称、变更人员等信息
- **完整的权限控制**：实现基于用户角色的权限管理，支持页面访问权限控制

### 优化

- **模块化设计**：采用统一的前后端分离架构，遵循项目现有的命名规范和目录结构
- **文件结构优化**：将消息推送管理主文件 `msg_push_management.vue` 重构为 `msg_push_management/index.vue`，与子组件统一放置在同一目录下，提升代码组织结构的清晰度和可维护性
- **用户体验优化**：提供直观的标签页界面，支持配置测试、任务预览、模板变量配置等功能
- **数据库设计优化**：使用规范的表结构设计，支持索引优化、外键约束和数据完整性保证
- **API设计规范**：遵循RESTful API设计原则，提供统一的错误处理和响应格式
- **全局搜索功能增强**：在全局搜索视图中新增虚拟机信息搜索支持，提升搜索覆盖范围

### 修复

- **修复全局搜索视图中虚拟机表名和数据类型错误**：将视图中的 `cmdb_virtual_machine` 表名修正为正确的 `cmdb_vm_registry`，并修复了不同表中 `function_purpose` 字段数据类型不一致问题（`cmdb_vm_registry` 为 `text` 类型，其他表为 `varchar(255)` 类型），通过类型转换解决 UNION 视图创建失败的问题
- **修复消息推送管理后端依赖缺失问题**：安装了 `node-cron`（v4.1.0）用于定时任务调度和 `nodemailer`（v7.0.3）用于邮件发送功能，解决了模块找不到的错误
- **修复消息推送管理数据库SQL语法错误**：将MySQL语法修正为PostgreSQL语法，包括COMMENT语法改为COMMENT ON COLUMN语句，数据类型规范化（SERIAL、VARCHAR、INTEGER、BOOLEAN等），添加CASCADE删除，使用DO块进行条件检查，确保SQL脚本在PostgreSQL环境下正常执行
- **修复消息推送管理数据字典插入语句缺少必需字段**：补充了 `created_at`、`updated_at`、`del_flag` 字段，并在 WHERE 条件中添加 `del_flag = '0'` 检查，确保数据字典记录正确插入和唯一性检查
- **修复消息推送管理页面权限SQL语句错误**：修正了 `cmdb_pages` 表插入语句，添加必需的 `page_code` 字段，移除不存在的 `page_category` 和 `is_active` 字段，补充标准字段（`created_at`、`created_by`、`updated_at`、`updated_by`、`del_flag`），并优化了父页面查找逻辑和用户权限插入语句
- **修复用户页面权限表字段错误**：移除了不存在的 `permission_type` 字段，该字段在实际的 `cmdb_user_page_permissions` 表结构中不存在，修正后的插入语句与其他页面权限SQL保持一致
- **修复消息推送管理后端路由配置错误**：解决了 `Route.post() requires a callback function but got a [object Undefined]` 错误，将消息推送控制器重构为直接使用Express Router，并添加了缺失的 `retry_msg_push` API路由
- **修复消息推送管理前端组件兼容性问题**：修正了ConfigManagement.vue组件中的HTML结构错误（`el-form-item`和 `el-col`标签嵌套问题），将Vue 2语法 `this.$set`替换为Vue 3兼容的响应式更新方式，确保测试按钮的loading状态能正确显示

## [2.2.2.3] - 2025年6月16日

### 更新

- 新增完整的Cursor Rules开发规范文档集合，提升AI代码协助能力
- 创建后端开发规范文档(backend.mdc)，包含Node.js + Express.js + PostgreSQL技术栈指南
- 创建数据库设计规范文档(database.mdc)，包含完整的PostgreSQL数据库设计标准
- 创建API开发规范文档(api.mdc)，包含RESTful API设计和开发最佳实践
- 创建前端开发规范文档(frontend.mdc)，包含Vue.js 3 + Element Plus开发指南

### 优化

- 规范化项目开发流程，统一前后端技术栈和开发标准
- 完善项目文档结构，为AI辅助开发提供详细的上下文信息
- 统一命名规范：资产管理(cmdb_前缀)、AI平台(AI_前缀)、运维管理(ops_前缀)
- 建立完整的数据库设计规范，包含表命名、视图命名、序列命名等标准
- 定义统一的API响应格式和错误处理机制
- 改进CORS中间件错误处理逻辑，避免因CORS验证失败导致的500错误
- 简化前端版本信息获取逻辑，移除不必要的测试请求

### 修复

- 🛠️ **重大修复**: 修复事件管理保存时500内部服务器错误的问题
- 增强事件管理updateEventManagement方法的日期时间处理逻辑，添加格式验证和错误处理
- 修复事件管理API缺少返回结果验证的问题，添加RETURNING *和rowCount检查
- 改进事件管理错误处理机制，提供更详细的PostgreSQL错误代码识别和错误信息
- 修复CORS中间件中callback(new Error(...))导致500错误的问题，改为callback(null, false)返回正确的CORS错误
- 删除前端SidebarVersionInfo组件中多余的/api/test接口调用，解决404 Not Found错误
- 完善开发规范文档，解决项目开发过程中的规范不统一问题

## [2.2.2.2] - 2025年6月13日

### 更新

- 事件记录导出Word文档主标题现在使用事件标题而非固定的"事件管理报告"标题
- 根据当前事件记录表单内容重新调整Word导出结构，完善章节标题和内容对应关系

### 优化

- 优化事件记录导出Word文档结构，按序号重新组织内容章节：
  - 一、事件简要经过
  - 二、事件影响范围、影响程度、影响人数、直接资金损失情况
  - 三、事件导致的后果、发生原因和事件性质判断
  - 四、已采取的措施及效果
  - 五、总结及知识库
- 统一基本信息表格字段标签：将"报告人"改为"值班经理"，"报告时间"改为"发生时间"，与表单保持一致
- 优化导出文件命名格式，改为"事件编号_事件标题.docx"格式，提升文件可识别性
- 改进HTTP响应头编码处理，使用RFC 5987标准支持中文文件名正确显示

### 修复

- 修复导出Word文档中事件标题显示异常的字符编码问题，确保中文内容正确显示
- 修复处理人姓名显示逻辑，支持多个处理人的正确查询和显示（逗号分隔处理）
- 修复文件删除功能中物理文件未真正删除的问题，增加用户确认对话框和物理文件清理逻辑

## [2.2.2.1] - 2025年6月13日

### 更新

- 新增Nginx CORS安全配置文件，实施严格的域名白名单策略

### 优化

- 移除config.js文件，统一使用.env环境变量配置，简化项目架构
- 优化后端CORS配置，使用安全工具模块实现严格的域名白名单验证
- 增强Nginx安全配置，添加多层安全HTTP头部防护
- 实施敏感文件访问控制，禁止访问配置文件等敏感资源

### 修复

- 🛡️ **重大安全修复**: 修复Nginx配置中11处CORS安全漏洞，移除所有通配符(*)设置
- 修复后端Express CORS配置安全问题，实施严格的域名白名单验证
- 修复auth_middleware.js和login_api.js中config.js依赖问题，改为直接使用环境变量
- 加强CORS安全策略，防止数据泄露、CSRF攻击和凭据盗用风险
- 修复事件分析报告模板下载时文件名前出现"UTF-8"前缀的问题，优化Content-Disposition头解析逻辑

## [2.2.2.0] - 2025年6月12日

### 更新

- 支持事件模板快速填充功能
- 支持事件分析报告模板下载
- 支持事件导出功能
- 事件管理列表页面操作列新增"导出"按钮，支持直接从列表页导出事件Word报告
- 实现事件处理模板样例的权限控制，只有admin用户可以编辑模板内容，所有用户可使用快速填充功能

### 优化

- 优化事件管理Word导出格式，统一字体规范：一级标题使用黑体三号，二级标题使用黑体四号，正文使用宋体小四
- 改进Word文档行间距设置：正文内容使用1.5倍行间距，表格内容使用1.0倍行间距，提升文档可读性
- 优化事件管理列表页面操作列布局，调整列宽为140px，提供更合适的按钮显示空间
- 移除Word导出文档的页脚信息，简化文档结构

### 修复

- 修复Word导出文档中文本换行显示问题，实现自动段落分割和正确的换行处理
- 修复Word导出文档字体颜色不统一问题，统一设置为黑色字体，提升文档专业性

## [2.2.1.9] - 2025年6月11日

### 更新

- 变更管理页面操作列新增下载按钮，支持直接下载变更操作表文件
- 新增变更时间状态显示功能，包含已过期、今天、明天、本周、未来、未设定六种状态
- 搜索区域新增"是否过期"筛选选项，支持已过期/未过期两种筛选条件
- 页面顶部新增状态图例说明，帮助用户理解不同状态的含义

### 优化

- 调整变更系统列宽度，设置最小宽度50px，最大宽度80px，提升表格空间利用率
- 计划变更时间列增加时间状态标签显示，使用不同颜色区分状态等级
- 优化操作列布局，增加按钮间距提升视觉效果
- 改进搜索表单布局，将过期筛选单独成行，优化表单排列

### 修复

- 修复下载按钮显示逻辑，只在存在变更操作表文件时启用下载功能
- 修复时间状态判断算法，使用当前日期准确计算过期状态
- 优化后端过期筛选查询条件，使用数据库CURRENT_DATE函数确保判断准确性

## [2.2.1.8] - 2025年6月11日

### 更新

- 实现系统管理员责任表页面管理员字段与用户表(cmdb_users)的关联
- 新增网络设备、实体服务器、虚拟机、应用系统、主机扫描结果等页面的管理员字段选择功能
- 系统管理员责任表页面新增主岗和备岗字段的用户选择功能

### 优化

- 将CMDB各页面中的管理员字段从手工输入改为与用户表关联的下拉选择
- 为所有管理员选择框添加可搜索(filterable)和可清空(clearable)功能，提升用户体验
- 优化表单验证规则，将管理员字段提示从"请输入"改为"请选择"，触发事件从'blur'改为'change'
- 移除搜索表单中的"所有"选项，使用clearable属性提供更好的清空体验
- 统一用户选择显示格式，所有管理员字段都显示用户真实姓名(real_name)而非用户名

### 修复

- 修复系统管理员责任表页面主岗和备岗选择显示无数据的问题，将API请求方式从GET修改为POST
- 修复应用系统页面管理员字段设计逻辑，新增/编辑表单中的管理员字段设为disabled状态（从关联服务器获取）
- 修复主机扫描结果页面用户列表数据引用问题，复用现有的userList数据而非创建新的usersList

## [2.2.1.7] - 2025年6月11日

### 更新

- 在交易日历值班排班表单中新增人员姓名标签显示功能，选择人员后在下拉框下方显示所选人员的真实姓名
- 不同值班角色使用不同颜色的标签进行区分（主班：蓝色，副班：绿色，值班经理：橙色，仿真/巡检：灰色）

### 优化

- 优化交易日历中值班信息的显示样式，移除高度限制，确保多人值班时能完整显示所有人员姓名
- 调整值班信息文本对齐方式为左对齐，改善多人值班时的显示效果
- 增日历中排班人员信息的字体大小（从10px增加到12px），提升可读性
- 优化人员标签的内边距和字体粗细，使标签显示更加清晰
- 完善人员标签的对齐方式，设置为上下居中、左对齐，提升视觉效果
- 调整不同值班角色的透明度，形成视觉层次（副班85%透明度，仿真85%透明度，巡检70%透明度）

### 修复

- 修复多人值班时前端显示为空的问题，通过优化数据库视图v_ops_calendar处理逗号分隔的用户名
- 创建数据库视图修复脚本，使用string_to_array和string_agg函数正确处理多人值班的姓名聚合
- 修复CSS兼容性问题，为所有-webkit-line-clamp属性添加标准line-clamp属性
- 修复事件管理中改进计划不显示的问题

## [2.2.1.6] - 2025年6月11日

### 更新

- 完善交易日历中排班导入功能，支持Excel文件批量导入值班排班信息
- 支持Excel日期格式(如2025/6/15)和标准日期格式(如2025-06-15)的自动识别和转换
- 新增排班导入模板下载功能，提供标准的Excel模板格式
- 实现值班人员姓名与cmdb_users表real_name字段的自动匹配功能
- 添加导入数据预览功能，支持数据验证和错误信息提示
- 支持五个值班岗位的批量导入：主班、副班、值班经理、仿真人员、巡检人员
- 支持事件管理中的处理人多选和人员展示

### 优化

- 优化排班导入的用户匹配逻辑，支持用户名和真实姓名两种匹配方式
- 改进Excel日期序列号的转换算法，确保日期解析的准确性
- 优化导入预览界面，提供详细的数据验证状态和错误信息显示
- 完善导入模板格式，使用更直观的列名(主班、副班等)
- 增强日期格式验证，支持多种常见的日期输入格式

### 修复

- 修复排班导入功能中400错误的问题，解决Excel解析时的语法错误
- 修复导入模板中列名不匹配导致的解析失败问题
- 修复日期字段处理逻辑错误，确保Excel日期格式正确转换为数据库格式
- 修复用户字段验证中的错误信息显示问题，提供更准确的错误提示
- 修复导入功能中事务处理逻辑，确保数据导入的一致性和完整性
- 修复排班导入时created_by和updated_by字段使用系统默认值的问题，现在正确使用当前登录用户的username
- 修复事件管理中的时区问题

## [2.2.1.5] - 2025年6月10日

### 更新

### 优化

- 优化系统管理员责任表（公司）页面字段

### 修复

## [2.2.1.4] - 2025年6月9日

### 更新

- 新增事件管理功能模块，包含事件列表、详情页面和完整的CRUD操作
- 新增事件编号自动生成功能，格式为SJ-YYYYMMDD-四位数序号，支持每日自动重置序号
- 在事件详情页面新增"改进计划"字段，支持事件处理后记录改进措施和预防计划
- 创建事件管理数据库表和视图，支持与数据字典的关联显示
- 新增事件类型、事件状态、事件级别等数据字典配置，使用代码T、V、U分别管理
- 实现事件管理页面的搜索和筛选功能，支持多维度查询条件
- 添加事件管理权限控制，支持基于用户角色的访问和操作限制

### 优化

- 优化事件详情页面UI布局，将事件编号独立显示，提升视觉效果和空间利用率
- 改进表单验证规则，确保必填字段的数据完整性和业务规则正确性
- 统一事件状态数据字典格式，符合cmdb_data_dictionary表结构规范
- 优化事件编号显示逻辑，新增模式下显示编号生成规则提示，编辑模式下显示实际编号
- 完善响应式设计，确保事件管理页面在不同屏幕尺寸下的良好显示效果
- 优化数据库视图查询性能，使用JOIN关联显示字典名称和用户真实姓名

### 修复

- 修复新增事件时timestamp字段空字符串导致的数据库错误，对空值进行null转换处理
- 修复事件状态字典数据与表结构不匹配的问题，调整插入语句字段顺序和类型
- 修复事件详情页面字段显示不完整的问题，确保所有必要字段正确显示和保存
- 修复事件编号生成规则中并发问题，使用数据库查询确保编号唯一性
- 修复前端状态选项获取错误，将EVENT_STATUS类型改为V类型数据字典

## [2.2.1.3] - 2025年6月6日

### 更新

### 优化

- 交易日历页面图例"有变更"蓝点样式优化：改为圆形小点，并调整边框颜色为更亮的蓝色（#66b1ff）。

### 修复

- 修复前端变更数据时，获取当前用户不正确的问题，全部从localStorage.getItem('loginUsername') 中获取当前登录用户值
- 修复变更管理页面变更编号生成规则，编号按当天递增，次日归1。

## [2.2.1.2] - 2025年6月3日

### 更新

- 为交易日历的值班排班操作添加用户操作日志记录功能
- 为变更管理页面的所有操作（新增、更新、删除）添加用户操作日志记录功能
- 在仪表盘的"最近活动"中显示详细的值班排班变更信息和变更管理操作信息
- 创建专门的数据库更新脚本，支持交易日历和变更管理操作日志的特殊显示格式
- 新增变更管理删除功能的后端API实现
- 在变更详情页面添加删除按钮，支持基于用户权限的访问控制

### 优化

- 为仪表盘页面的最近活动数据区增加滑动条功能，支持浏览更多活动记录
- 最近活动区域设置400px最大高度，超出部分可通过滚动查看
- 在最近活动卡片头部增加记录总数显示，方便用户了解活动数量
- 滑动条样式与系统其他页面保持一致，使用统一的滚动条设计规范
- 优化用户日志视图，为交易日历和变更管理操作提供更友好的日志信息显示
- 交易日历操作日志显示具体的日期和值班人员变更详情，提高操作可追溯性
- 变更管理操作日志显示变更编号、标题等关键信息，便于追踪变更记录的生命周期
- 完善变更管理功能，支持完整的CRUD操作并记录详细的操作日志
- 实现基于role_code字段的权限控制，只有包含'D'权限的用户才能删除变更记录
- 删除按钮在无权限时显示为灰色不可点击状态，并提供权限提示信息
- 清理和删除测试模块，减少程序文件大小：删除AI平台相关页面、API调试测试页面、后端测试API等

### 修复

- 修复用户日志中显示"未知数据"的问题，为无法匹配表注释的操作提供友好的描述信息
- 优化用户日志视图的匹配逻辑，根据URL模式提供更准确的操作描述
- 过滤掉无意义的通用操作日志（如"创建了数据"、"更新了数据"等），只显示有具体意义的操作

## [2.2.1.1] - 2025年5月30日

### 更新

- 添加API调试测试页面，用于诊断和解决生产环境中的API请求问题

### 优化

- 完善响应式设计，在不同屏幕尺寸下都能提供良好的用户体验
- 统一对话框和表格样式，与系统其他页面保持一致的交互体验
- 创建统一的日期工具函数，规范前端和数据库之间的日期格式转换
- 添加详细的调试日志，便于排查日期转换和数据匹配问题

### 修复

- 优化nginx配置中的静态资源处理，确保SPA路由和静态资源访问不冲突
- 直接修复主服务器和备用服务器的nginx配置文件，解决405错误和静态资源404问题
- 统一两台服务器的配置结构，添加专门的/assets/路径处理和HTTP方法保护
- 在主服务器nginx配置中添加应用切换功能，支持随时切换到备用服务器
- 新增8080端口的切换服务，提供可视化的切换管理页面和代理功能
- 创建自动化切换脚本，支持一键切换主备服务器和状态检查
- 优化交易日历页面操作说明的布局，将说明文字移入日历容器内部，确保与日历主体完美对齐，并添加响应式设计
- 修复SPA应用中带路由前缀的静态资源404错误，添加URL重写规则将/模块名/assets/路径重定向到/assets/路径

## [2.2.1.0] - 2025年5月29日

### 更新

- 在运维管理栏目下新增交易日历页面，支持月度日历视图展示
- 实现工作日与非工作日的不同颜色显示，基于wrk字段正确判断工作日状态
- 集成变更管理信息，在日历上显示计划变更日期和变更名称缩略信息
- 支持点击有变更的日期查看详细变更列表，并可直接跳转到变更详情页面
- 新增值班排班功能，支持五个值班岗位：主班、副班、值班经理、仿真、巡检
- 在日历中显示值班人员信息，数据库存储username，前端显示real_name
- 支持点击日期进行值班排班设置，提供用户选择下拉框
- 添加交易日历数据库表和视图，支持工作日、交易日、节假日和值班人员字段
- 新增交易日历相关API接口，包括获取日历数据、变更详情、用户列表和值班排班功能
- 扩展值班排班功能，新增巡检人员岗位，完善值班人员管理体系
- 实现值班排班权限控制系统，支持基于用户的编辑和查看权限管理
- 在用户管理页面增加交易日历权限配置功能，只允许admin用户进行权限设置

### 优化

- 优化交易日历界面设计，与系统其他页面保持统一的视觉风格和布局规范
- 简化界面布局，将日历控制栏与主体合并为一个卡片，提升界面整洁度
- 简化值班信息颜色方案，从5种颜色减少到3种主色调，降低视觉疲劳
- 优化月份切换功能，移除页面刷新机制，改为数据重新加载，提升用户交互体验
- 实现月份记忆功能，使用localStorage保存用户选择的月份，页面刷新后保持在选择的月份而非回到当前月
- 简化颜色方案，只区分工作日和非工作日两种状态，降低视觉复杂度
- 优化卡片头部布局，采用三栏式设计：左侧标题和统计、中间图例说明、右侧操作控件
- 改进日历单元格样式，使用更清晰的颜色区分和更好的视觉层次
- 优化交易日历中的变更信息显示，改为右上角角标形式，节省空间并突出显示

### 修复

- 修复交易日历表主键定义错误，移除不存在的EXCHANGEID字段引用
- 修复日期显示不一致问题，优化日期格式转换逻辑，确保前端显示与数据库存储一致
- 修复变更计划时间查询错误，正确处理日期格式转换，确保查询到正确日期的变更信息
- 修复本月变更统计显示异常问题，重写统计逻辑确保正确计算当前月份的变更数量
- 解决有变更日期无法进行值班排班的功能冲突，增加右键菜单支持直接进行值班排班
- 添加操作提示说明，指导用户正确使用左键和右键进行不同操作
- 修复月度变更统计计算错误，添加数据类型检查和详细调试日志
- 修复变更详情页面计划时间显示不一致问题，统一为日期格式选择和显示
- 修复变更详情页面计划时间时区转换问题，避免显示前一天的日期偏移
- 修复值班排班权限控制逻辑错误，确保view权限用户无法编辑，edit权限用户可查看可编辑
- 优化权限控制交互逻辑，view权限或无权限用户无法点击日历单元格弹出值班排班表单，只能查看变更记录
- 强化权限验证逻辑，修复权限初始化问题，确保无权限用户严格无法访问值班排班功能
- 修复交易日历变更详情显示代码而非名称的问题，变更级别显示字典名称，变更负责人和实施人显示真实姓名
- 修复交易日历月份切换时1号位置显示错误的问题，重构日历计算算法使用更可靠的日期计算方法
- 优化交易日历月份切换机制，使用nextTick确保DOM更新完成后再获取数据，提升渲染稳定性
- 诊断并修复生产环境中全局搜索跳转后数据字典API返回405错误的问题，添加详细的调试日志和错误处理
- 针对生产环境nginx代理配置问题，添加临时的GET到POST请求转换机制
- 创建修复版本的nginx配置文件，解决负载均衡和缓存导致的HTTP方法变更问题
- 增强API调试测试页面，支持测试不同HTTP方法和请求头配置
- 为备用服务器（172.19.129.82）创建修复版本的nginx配置文件
- 创建自动化修复脚本，支持一键修复主服务器和备用服务器的405错误问题
- 完善部署指南，包含两台服务器的详细修复步骤和验证方法
- 修复生产环境静态资源404错误，在nginx配置中添加专门的/assets/路径处理规则
- 创建静态资源测试页面，用于诊断和验证静态文件加载问题

## [2.2.0.7] - 2025年5月28日

### 优化

- 优化附件下载逻辑，使用直接流

### 修复

- 修复文件下载链接环境不匹配的问题，统一使用直接文件流下载方式，避免依赖静态文件服务和环境变量配置

## [2.2.0.6] - 2025年5月26日

### 更新

- 在变更管理附件管理中新增【补充资料】上传功能，支持与其他附件相同的文件格式
- 添加补充资料文件存储路径配置，需要在.env中配置FILE_UPLOAD_SUPPLEMENTARY_PATH
- 新增数据库字段supplementary_material用于存储补充资料文件路径
- 在变更详情页面的附件管理上方新增【变更模板下载】功能，支持选择模板名称后直接下载
- 新增完整的API文档，涵盖所有模块的接口说明、参数格式、响应示例和错误码说明

### 优化

- 优化变更管理页面数据查询，改为使用v_ops_change_management视图获取数据，提高查询效率和数据一致性
- 优化变更管理页面默认显示条件，默认显示OA流程和签字存档均为"未上传"的记录，按变更编号从大到小排序
- 优化文件下载机制，统一使用直接文件流下载，简化FILE_URL_PREFIX配置依赖
- 优化nginx配置，注释掉不必要的/files/代理配置，简化部署复杂度
- 完善README.md文档，添加详细的项目介绍、技术栈、功能特性、部署指南等内容
- 优化变更详情页面布局，提高空间利用率和用户体验
- 将变更编号改为单独显示，不占用表单框位置，节省空间
- 将变更名称调整为表单第一位，符合用户操作习惯
- 将所有变更字段整合到一个统一的"变更信息"卡片中，提高页面一致性
- 优化表单布局为三行结构：变更名称(全宽) + 基本信息(3列) + 系统人员(2列)
- 移除冗余的详细展示区域和分隔线设计，简化页面结构
- 统一使用Element Plus表单组件，保持一致的交互体验
- 使用统一的视觉风格和Element Plus设计规范
- 添加响应式设计，确保在移动端的良好显示效果

### 修复

- 修复补充资料上传时提示"无效的类型"的问题，完善后端API对supplementary_material文件类型的支持
- 修复前端无法显示已上传的补充资料文件的问题，在后端API查询中添加supplementary_material字段
- 修复nginx配置缺少/files/路径转发导致文件下载失败的问题，更新主服务器和备用服务器的nginx配置文件

## [2.2.0.5] - 2025年5月23日

### 更新

- 在运维管理栏目下新增变更模板管理页面，提供模板上传、预览和下载功能
- 支持Word和Excel格式的变更模板文件管理
- 实现模板文件在线预览功能，无需下载即可查看文件内容
- 添加模板文件上传功能，支持文件类型和大小验证
- 为变更模板管理页面的删除按钮添加用户级别的权限控制，只有拥有删除权限的用户才能看到删除按钮

### 优化

- 优化变更模板管理页面的用户界面，使用卡片样式展示文件信息
- 移除文件预览功能，直接提供下载选项，解决内网环境无法预览的问题
- 优化文件上传组件，提供文件类型和大小限制提示，增强文件后缀检查
- 优化模板列表展示，移除ID列，增加更新时间和更新人字段展示
- 优化文件上传路径配置，添加专用的变更模板文件存储路径
- 优化变更模板管理页面，移除文件类型列，简化界面展示
- 优化变更模板管理功能，添加覆盖上传按钮，支持更新现有模板文件
- 优化中文文件名处理机制，使用iconv-lite库实现更准确的编码检测和转换
- 优化变更模板管理页面的权限控制，使用role_code机制控制删除按钮的显示
- 优化文件上传错误处理，提供友好的错误提示，避免显示技术错误信息

### 修复

- 修复变更模板管理表中file_type字段长度不足导致的上传失败问题
- 修复中文文件名上传时出现乱码的问题，优化文件名编码处理机制
- 修复变更模板覆盖上传功能无法正常工作的问题，添加文件上传中间件
- 修复文件上传后原始文件名显示乱码的问题，从前端直接传递原始文件名
- 修复文件上传中断问题，简化文件上传处理流程，增强错误处理
- 修复覆盖上传功能中sanitizeParam未定义的问题，统一参数处理函数
- 修复文件上传验证问题，确保只允许Word和Excel格式的文件上传
- 修复覆盖上传功能中文件类型验证问题，确保只允许.doc、.docx、.xls、.xlsx格式的文件
- 修复变更模板上传路径配置问题，确保文件正确存储在指定目录
- 修复上传文件后显示文件名乱码的问题，优化文件名编码处理逻辑
- 修复变更模板文件上传到错误目录的问题，确保文件存储在change_templates目录
- 修复数据库中文件名中文字符保存不正确的问题，优化数据库编码设置
- 修复上传中文文件名的文件后数据库中显示乱码的问题，完善文件名编码处理机制
- 修复变更模板管理中created_by和updated_by字段保存为system的问题，改为保存当前登录用户的username
- 修复中文文件名在数据库中保存为乱码的问题，增强文件名编码处理算法
- 修复特定中文文件名编码问题，添加专门的中文文件名处理工具
- 修复下载文件时无法使用原始文件名的问题，实现直接下载功能
- 修复当前登录用户获取为system的问题，改为使用auth.js中的getUsername函数获取，默认用户名设置为admin
- 统一解决所有文件格式的中文文件名乱码问题，使用硬编码方式处理特定文件类型的乱码，支持Word、Excel、PDF等多种文件格式
- 修复模板预览功能，添加加载状态和错误处理，优化用户体验

## [2.2.0.4] - 2025年5月22日

### 更新

- 变更管理页面变更系统支持多选，提高灵活性
- 变更管理页面搜索栏增加变更系统搜索条件，方便按系统筛选变更记录
- 变更详情页面增加多选选项的详细展示区域，直观展示变更涉及的系统和实施人
- 系统管理责任公司页面增加必填字段验证，确保关键信息完整性
- 系统管理责任公司页面主岗和备岗字段改为从用户表中选择，提高数据准确性
- 系统管理责任公司页面JR/T 0059-2010备份能力标准字段使用数据字典Q类型，规范数据存储
- 系统管理责任公司页面业务线条字段使用数据字典R类型，规范数据存储
- 系统管理责任公司页面业务系统大类字段使用数据字典S类型，规范数据存储

### 优化

- 变更管理页面取消变更描述字段的填入和展示，简化变更管理流程
- 优化变更详情页面的用户体验，使用卡片和标签样式展示多选字段，提高可读性
- 变更详情页面下拉选项框支持预输入快速搜索，提高用户选择效率
- 系统管理责任公司页面必填字段添加红色星号标注，提高用户体验
- 系统管理责任公司页面表格中显示用户真实姓名而非用户名，提高可读性
- 系统管理责任公司页面JR/T 0059-2010备份能力标准字段前端展示dict_name，后端存储dict_code，提高数据一致性
- 系统管理责任公司页面搜索栏中的主岗字段改为下拉选择框，支持快速筛选
- 系统管理责任公司页面业务线条和业务系统大类字段前端展示dict_name，后端存储dict_code，提高数据一致性
- 系统管理责任公司页面搜索栏增加业务线条和业务系统大类搜索条件，方便按业务类型筛选系统

### 修复

- 系统管理责任公司页面修复业务线条和业务系统大类搜索条件无效的问题，修改前后端代码确保搜索条件正确传递和处理

## [2.2.0.3] - 2025年5月21日

### 更新

- 实现事件管理页面，使用MVC架构组织代码，参考ITIL最佳实践
- 添加共享文件存储配置，支持两个节点服务共同访问同一个附件存储路径

### 优化

- 优化Nginx配置，添加共享文件服务配置，提高系统可靠性
- 提供详细的共享存储部署指南，包括NFS和SMB两种方案

### 修复

## [2.2.0.2] - 2025年5月21日

### 更新

- 变更详情页面附件管理增加手动刷新按钮，方便用户主动刷新附件状态

### 优化

- 统一变更管理页面中变更级别的字段名，前后端统一使用change_level，表格显示使用change_level_name_display
- 优化变更管理页面数据查询，直接从数据库获取数据而不依赖视图，提高查询效率
- 优化变更详情页面附件上传功能，上传或删除附件后智能多次刷新确保显示最新状态，提升用户体验
- 优化附件状态刷新机制，增加强制更新模式，解决缓存问题
- 优化变更详情页面数据刷新机制，实现父子组件协同刷新，确保数据一致性
- 优化变更详情页面附件管理刷新按钮位置，放置在右侧，提升界面布局美观度
- 优化变更管理页面操作按钮，将"查看"按钮改为"详情"按钮，提高用户体验
- 优化全局搜索功能，支持变更管理搜索结果，点击结果自动填入关键词并搜索，提升用户体验
- 优化页面权限管理，修复运维管理页面（变更管理和事件管理）在权限树中不可见的问题

### 修复

- 修复变更管理页面中变更实施人字段无法显示数据的问题
- 修复变更管理页面中变更级别字段无法显示数据的问题
- 修复变更管理页面搜索栏查询失败的问题
- 修复变更管理页面新增变更时created_by和updated_by字段未正确设置为当前用户的问题
- 修复变更管理页面搜索栏中使用变更级别无法搜索到结果的问题
- 修复变更管理页面表格中变更级别显示错误的问题
- 修复添加变更管理记录时参数数量不匹配的问题
- 修复变更管理和变更详情页面中变更级别选项显示问题，确保只显示数据字典P类型的选项
- 修复变更管理页面数据表中变更级别未显示的问题
- 修复变更详情页面附件上传功能中文件名乱码问题，优化文件上传处理逻辑
- 修复变更详情页面附件上传后状态刷新不及时的问题，实现智能多次刷新机制
- 修复变更详情页面附件状态刷新不生效的问题，解决数据缓存和组件更新问题
- 修复变更详情页面附件状态刷新需要刷新整个页面的问题，实现组件级别的精确刷新
- 修复变更管理页面新增变更时created_by和updated_by字段默认为admin或system的问题，确保使用当前登录用户
- 修复变更管理页面点击详情按钮时始终打开第一条记录的问题，确保正确打开对应记录

## [2.2.0.1] - 2025年5月20日

### 更新

- 优化变更管理页面界面，参考最新设计规范
- 在数据字典页面添加关键词搜索功能，支持同时搜索字典类型名称和字典名称
- 全栈实现变更管理功能，包括数据库表设计、后端API和前端界面
- 更新后端架构文档，添加新模块MVC架构实施指南

### 优化

- 优化侧边栏菜单布局，移除占位元素，改用CSS解决版本号遮挡问题

### 修复

- 修复数据字典新增时可能出现的主键冲突问题
- 修复数据字典表序列值不同步导致的插入错误
- 修复数据字典关键词搜索时的变量未定义错误
- 修复前端依赖问题，使用原生JavaScript实现日期格式化功能

## [*******] - 2025年5月19日

### 更新

- 更新前端和后端架构文档，添加详细的命名规范
- 完善模块划分规范，明确不同模块的命名前缀
- 添加新增模块开发规范，规范化开发流程
- 为运维管理模块添加页面权限配置，包括变更管理和事件管理页面
- 新增"运维管理"主菜单，位于自动发现菜单下方
- 在运维管理菜单下添加"变更管理"和"事件管理"两个子菜单
- 实现变更管理和事件管理的前端页面，包括搜索、列表展示等功能
- 使用"ops_"前缀命名运维管理相关页面，区别于CMDB模块
- 在设备管理页面添加序列号搜索功能
  - 前端：在搜索栏添加序列号输入框
  - 后端：在 get_cmdb_device_management 接口添加序列号搜索支持
- 优化变更管理页面界面，参考最新设计规范
  - 添加顶部导航标签，提高页面层次感
  - 重新设计搜索区域，使其更加紧凑直观
  - 新增OA流程和签字存档字段，完善变更管理流程
  - 优化表格结构，增加所属系统和变更级别列
  - 改进操作按钮样式，使用文本按钮节省空间

### 优化

- 统一前后端命名规则，提高代码可维护性
- 完善文档结构，便于新开发人员快速理解项目架构
- 规范化API接口和数据库表命名，保持一致性
- 优化页面权限管理，确保新增页面的权限控制与现有系统一致
- 使用Element Plus的图标组件，保持界面风格一致性
- 优化表格布局，固定操作列，提高用户体验
- 添加状态和优先级的颜色标识，增强视觉效果
- 统一命名规范，使模块划分更加清晰
- 优化设备管理页面的搜索功能，支持按序列号进行精确查询
- 保持与其他搜索条件一致的模糊匹配方式，提高用户体验
- 改进变更管理页面的搜索功能，支持按关键词进行全文搜索
- 优化变更管理表格的视觉效果，使用圆角标签显示状态信息
- 改进变更管理页面的响应式布局，适配不同屏幕尺寸

### 修复

## [2.1.0.1] - 2025年5月14日

### 更新

- 更新前端知识库组件文件名，从knowledge_base.vue改为ai_knowledge_base.vue
- 更新前端文档智能修订组件文件名，从document_editor.vue改为ai_document_editor.vue

### 优化

- 文件命名更加规范，与AI平台下的其他功能组件保持一致

### 修复

## [2.1.0.0] - 2025年5月12日

### 更新

- 新增"AI平台"作为独立的主菜单，位于报表中心菜单下方
- 添加"文档智能修订"功能，通过嵌入式iframe集成外部AI服务
- 预留"智能数据分析"功能卡片，为后续扩展做准备
- 添加AI平台相关的数据库表结构和页面权限配置

### 优化

- 优化AI平台页面的响应式布局，适配不同屏幕尺寸
- 使用卡片悬浮效果提升用户交互体验
- 优化iframe加载方式，提高外部服务的集成效率
- 完善AI平台的权限控制机制，确保安全访问
- 优化系统管理员责任表（公司）页面中"通用功能域"和"经营机构功能域"的展示方式

### 修复

- 修复菜单权限检查逻辑，确保新增页面的权限控制正常工作

## [*******6] - 2025年5月9日

### 更新

- 修改系统管理员责任表（公司）页面中"通用功能域"和"经营机构功能域"的展示方式
- 功能域数据现在以逗号分隔并且不换行显示，提高表格的紧凑性

### 优化

- 优化表格显示效果，使功能域数据更加清晰易读
- 保持核心功能域的红色标注，确保重要信息突出显示

## [*******5] - 2025年5月9日

### 更新

- 移除系统管理员责任表（公司）页面中"通用功能域"和"经营机构功能域"的查询条件
- 保留表格中的功能域显示和表单中的功能域选择功能

### 优化

- 简化搜索界面，减少不必要的筛选条件，提高用户体验
- 优化页面布局，使搜索区域更加紧凑

## [*******4] - 2025年5月9日

### 更新

- 在系统管理员责任表（公司）页面新增"通用功能域"和"经营机构功能域"字段，均为多选
- 创建功能域类型表（cmdb_function_domain_types），用于存储功能域数据
- 在表格中添加"通用功能域"和"经营机构功能域"列，并使用红色标注核心功能域
- 添加核心功能域标识，使用红色文字和"(核心)"标签突出显示

### 优化

- 在添加和编辑表单中添加"通用功能域"和"经营机构功能域"字段，使用多选下拉框实现
- 使用collapse-tags属性优化多选显示效果，提高用户体验
- 优化表单布局，保持各字段样式一致，提高用户体验

## [*******3] - 2025年5月9日

### 更新

- 在系统管理员责任表（公司）页面新增"等保等级"字段，包含"一级"、"二级"、"三级"三个选项
- 在表格中添加"等保等级"列，方便用户查看系统等保等级

### 优化

- 在搜索条件中添加"等保等级"筛选项，方便用户按等保等级筛选数据
- 在添加和编辑表单中添加"等保等级"字段，使用下拉选择框实现，提高用户体验
- 优化表单布局，保持各字段样式一致，提高用户体验

## [*******2] - 2025年5月9日

### 更新

- 在系统管理员责任表（公司）页面新增"信创状态"字段，包含"未信创"、"完成开发或测试"、"非全栈双轨"、"非全栈单轨"、"全栈双轨"、"全栈单轨"六个选项
- 在表格中添加"信创状态"列，方便用户查看系统信创进度

### 优化

- 在搜索条件中添加"信创状态"筛选项，方便用户按信创状态筛选数据
- 在添加和编辑表单中添加"信创状态"字段，使用下拉选择框实现，提高用户体验
- 优化表单布局，保持各字段样式一致，提高用户体验

## [*******1] - 2025年5月9日

### 更新

- 在系统管理员责任表（公司）页面新增"运行状态"字段，包含"建设中"、"运行中"、"已下线"三个选项
- 在表格中添加"运行状态"列，方便用户查看系统当前状态

### 优化

- 在搜索条件中添加"运行状态"筛选项，方便用户按系统状态筛选数据
- 在添加和编辑表单中添加"运行状态"字段，使用下拉选择框实现，提高用户体验
- 优化表单布局，保持各字段样式一致，提高用户体验

## [*******0] - 2025年5月9日

### 更新

- 在应用系统信息页面增加"功能用途"字段，位于主机名后面，用于描述应用系统的主要功能
- 在IP资产管理页面搜索栏增加"是否在线"搜索条件，可选"在线"和"离线"两个选项

### 优化

- 应用系统信息页面的"是否在线"字段优化了取值逻辑，从自动发现结果中进行判断
- IP资产管理页面的"虚拟机状态"字段优化了取值逻辑，从自动发现结果中进行判断，提高了状态判断的准确性
- 优化IP资产管理页面的水平滚动条显示位置，固定在当前屏幕底部，方便用户查看靠上的数据时进行水平滚动
- 改进所有页面的水平滚动条，使其始终可见，不需要鼠标悬停才显示，提高用户体验
- 调整IP资产管理页面搜索卡片高度，确保所有搜索项和按钮正常显示

### 修复

- 修复调度任务页面偶发的DOM操作错误，增强组件稳定性

## [*******] - 2025年5月8日

### 更新

- 在系统管理员责任表（公司）页面增加"技术路线"字段，位于建设方式字段后面，使用数据字典类型O
- 优化系统管理员责任表（公司）页面中"系统属性"字段，使用数据字典类型C，参考信创小类的实现方式

### 优化

- 优化系统管理员责任表（公司）页面中的数据字典字段实现，统一使用字典名称作为显示和搜索值
- 改进系统管理员责任表（公司）页面的搜索功能，修复系统属性字段搜索不到数据的问题
- 优化表单布局，保持各字段样式一致，提高用户体验

### 修复

- 修复系统管理员责任表（公司）页面中系统属性字段搜索不到数据的问题

## [*******] - 2025年5月7日

### 更新

- 在需求与问题收集页面增加"计划开发时间"和"开发周期(人天)"两个字段，位于方案类型字段后面
- 在表格中添加"计划开发时间"和"开发周期(人天)"列，方便用户查看和管理开发计划
- 在详情对话框中显示"计划开发时间"和"开发周期(人天)"信息，提供完整的问题详情

### 优化

- 优化需求与问题收集页面的表单布局，使新增字段与现有字段保持一致的样式
- 使用日期选择器组件实现计划开发时间字段，提高用户体验
- 使用数字输入框组件实现开发周期字段，限制只能输入整数，避免输入错误

### 修复

- 修复日期字段为空时导致的"invalid input syntax for type date"错误，优化日期字段处理逻辑
- 将"问题描述"、"提出时间"和"上报人"设置为必填字段，确保数据完整性
- 增加表单验证，防止用户提交不完整的问题记录

## [*******] - 2025年5月7日

### 更新

- 在应用系统信息页面增加"关联主从机IP"字段，位于主从角色字段后面
- 当主从角色不为单机时，关联主从机IP字段为必填项
- 支持在关联主从机IP字段中输入多个IP地址，使用英文逗号分隔
- 添加IP地址格式验证，确保输入的IP地址格式正确
- 将应用系统信息表单中的"归属业务系统名称"字段改为下拉选择框，选项来自系统管理员责任表（公司）
- 将"归属业务系统名称"字段设置为必填项，确保每个应用系统都有明确的归属
- 将搜索栏中的"归属业务系统名称"字段也改为下拉选择框，保持与表单一致的交互体验
- 将"部署应用"和"生产属性"字段设置为必填项，确保应用系统信息的完整性
- 将"备注"字段移动到表单的末尾位置，优化表单布局和用户体验

### 优化

- 优化问题收集表中问题描述字段的显示宽度，增加预览内容，提高用户体验
- 优化问题收集表页面，添加问题描述搜索功能，方便用户快速查找问题
- 优化问题收集表页面的操作列，固定在右侧并设置固定宽度，提高用户体验
- 优化主从角色判断逻辑，支持同时识别字典代码和字典名称，提高表单验证的准确性
- 优化版本更新脚本，在创建新版本时自动添加"更新"、"优化"和"修复"三个分类标题

### 修复

- 修复编辑表单打开时，主从角色为单机但关联主从IP字段仍显示为必填的问题
- 修复问题收集表页面导出数据时，文件名错误的问题

## [*******] - 2025年4月30日

### 更新

- 在报表中心的网络设备年限情况统计和实体服务器年限情况统计页面中增加"是否信创"筛选条件
- 优化筛选条件收藏功能，支持保存和应用"是否信创"筛选条件

## [*******] - 2025年4月30日

### 更新

- 为网络设备和实体服务器添加"是否信创"字段，实现与网络设备相同的验证逻辑
- 修复部分bug
- 优化问题收集表前端样式

## [*******] - 2025年4月29日

### 更新

- 优化网络设备信息表单，当"是否单点"选择"否"时，"互备主机IP"字段设为必填
- 优化网络设备信息表单，移除"架构模式"字段，保留数据库字段作为后期扩展
- 为实体服务器管理添加"是否单点"和"互备主机IP"字段，实现与网络设备相同的验证逻辑
- 优化虚拟机信息表单，将"是否存在弱密码"字段的值从"true/false"改为"是/否"，提高用户体验
- 优化全局搜索功能，点击搜索结果时自动将IP带入目标页面的搜索条件中，提升用户体验
- 增强全局搜索功能，保留搜索结果状态，搜索结果会保存在浏览器的localStorage中，用户离开页面后返回时无需重新搜索
- 优化报表中心的网络设备年限统计和服务器年限统计页面，保留查询结果状态，提升用户体验

### 修复

- 修复监控IP列表插入时可能出现的主键冲突问题
- 修复全局搜索中应用系统信息的路由路径错误问题

## [*******] - 2025年4月28日

### 更新

- 优化调度任务选择关联任务的逻辑，已关联的任务会自动显示为已选中状态
- 修复调度任务表单中状态排序功能，支持按状态正确排序
- 优化Nginx配置，提升前端性能和安全性
- 修复发现任务管理页面按调度任务ID查询时的SQL错误
- 优化队列状态对话框，添加滑动条，提高用户体验
- 修复主备切换时登录卡住的问题，增强系统可靠性
- 优化数据字典缓存机制，提高系统性能和数据一致性

### 修复

- 修复用户首次登录卡住的的问题

### 优化

- 调度任务表单中添加"已关联任务自动选中"提示，提高用户体验
- 添加"已关联任务处理说明"提示框，说明如何取消关联任务
- 修复表单中的错误处理，提高代码健壮性
- 网络设备信息表单优化：
  - 当"是否单点"选择"否"时，"互备主机IP"字段设为必填
  - 支持在"互备主机IP"字段中输入多个IP地址，使用英文逗号分隔
  - 添加IP地址格式验证，确保输入的IP地址格式正确
- 实体服务器管理表单优化：
  - 添加"是否单点"和"互备主机IP"字段，与网络设备保持一致
  - 当"是否单点"选择"否"时，"互备主机IP"字段设为必填
  - 支持在"互备主机IP"字段中输入多个IP地址，使用英文逗号分隔
  - 添加IP地址格式验证，确保输入的IP地址格式正确
- 全面优化Nginx配置，包括：
  - 启用Gzip压缩，减少传输数据大小
  - 配置浏览器缓存策略，提高页面加载速度
  - 优化静态资源处理，减少服务器负载
  - 增强安全相关HTTP头，提高系统安全性
  - 配置服务器端缓存，提高响应速度
  - 优化负载均衡设置，提高系统可靠性
  - 添加健康检查机制，提高系统稳定性
  - 增强主备切换机制，解决登录卡住问题
  - 优化API请求处理，增加超时设置和缓冲区大小
  - 添加服务器标识，便于问题排查
- 队列状态对话框优化：
  - 添加滑动条，限制对话框高度，使其更加紧凑
  - 显示排队中任务的数量，方便用户了解队列状态
  - 自定义滚动条样式，提高用户体验
  - 优化表格高度，避免对话框过长
- 数据字典缓存优化：
  - 将缓存过期时间从5分钟延长至30分钟，减少数据库查询次数
  - 实现数据字典变更时自动清除缓存，确保数据一致性
  - 优化缓存更新机制，在添加、更新、删除操作后立即刷新缓存
  - 提高系统响应速度，特别是对频繁访问的数据字典查询

## [*******] - 2025年4月24日

### 更新

- 优化IP资产管理页面搜索框样式，与网络设备页面保持一致
- 修改IP资产管理页面，移除CMDB登记状态的搜索条件和数据列显示
- 在IP资产管理页面中，为管理状态和是否虚拟机字段提供下拉菜单选择
- 优化发现结果查看页面的批量登记功能，支持直接登记到IP资产管理
- 修复用户日志记录时的唯一约束冲突问题，提高系统稳定性

## [*******] - 2025年4月24日

### 更新

- 用户管理权限控制优化：除了 admin 用户外，其他用户只能查看和编辑自己的信息
- 修复普通用户无法编辑自己信息的问题
- 优化用户权限管理逻辑，确保普通用户不能修改角色权限
- 将项目中的 bcrypt 替换为 bcryptjs，解决内网环境部署时的兼容性问题

## [*******] - 2025年4月24日

### 更新

- 扩充用户管理，增加用户姓名、联系电话、邮箱、企业微信ID字段
- 增加用户访问页面的权限设置，当用户没有该页面的权限时，则无法看见该页面
- 新增页面权限管理功能，支持细粒度的页面权限控制
- 新增页面权限相关的数据库表结构
- 重构路由守卫，增加页面权限验证机制
- 将用户管理相关API独立成模块

### 优化

- 改进用户管理界面，增加更多用户信息字段
- 优化菜单显示逻辑，根据用户权限动态显示菜单项
- 完善用户登录逻辑，增加页面权限验证

## [2.0.2.1] - 2025年4月23日

### 更新

- 新增调度任务管理功能，支持将多个发现任务组织到一个调度任务中统一管理
- 支持手动、一次性、每日和每周四种调度类型，并提供日历和时间选择器
- 添加调度任务执行历史记录功能，跟踪每次执行的结果和状态
- 实现任务队列管理机制，支持最多100个任务的批量调度
- 重构发现任务控制器，将任务队列管理功能独立出来
- 新增调度任务相关的数据库表结构
- 实现调度任务与发现任务的关联机制

### 优化

- 使用日历和时间选择器替代文本输入，提高调度值设置的用户体验
- 为不同的调度类型提供清晰的格式说明和示例
- 改进任务队列管理机制，自动控制并发执行的任务数量
- 优化调度任务详情页面，提供全面的任务信息和执行历史

## [2.0.2.0] - 2025年4月22日

### 更新

- 新增自动发现功能
- 在CMDB发现任务管理页面添加任务ID列，便于任务追踪和管理
- 在CMDB发现任务中添加任务运行时间记录和显示功能

### 优化

- 改进任务队列状态对话框，使其只能通过关闭按钮关闭，避免意外关闭
- 添加查看队列状态按钮，允许用户随时查看批量任务执行进度
- 统一导出数据按钮的图标样式，保持界面一致性
- 修复发现结果查看页面的重置功能，使其正确重置任务ID字段
- 优化自动发现相关页面的表格布局，包括固定操作列和调整列宽
- 美化滚动条样式，使其与资产管理页面保持一致
- 改进分页组件显示，确保正确显示当前设置的每页条数

### 说明

- 明确CMDB发现任务执行消耗的是后端服务器资源，而非用户本地资源

## [*******] - 2025年4月21日

### 更新

- 新增用户登录记录功能，自动记录用户登录时间、IP、状态等信息

### 优化

- 登录失败时记录失败原因，便于安全分析
- 记录用户登录的设备信息，提高系统安全性

## [*******] - 2025年4月21日

### 更新

- 新增报表中心
- 提供网络设备年限情况统计报表
- 提供实体服务器年限情况统计报表
- 提供查询条件的收藏功能

### 修复

- 修复了视图v_cmdb_server_management统计数据有重复的问题
- 修复了视图v_cmdb_device_management统计数据有重复的问题

## [*******] - 2025年4月18日

### 更新

- 新增左侧菜单栏"需求与问题"栏目，位置放在配置管理下方
- 将"问题收集表"从资产管理移动到新的"需求与问题"栏目中

### 优化

- 增加表单字段验证功能，添加必填字段的验证
- 修改表单页面的标题，与侧边栏标题保持一致

## [*******] - 2025年4月16日

### 更新

- 更新全局搜索样式
- 主机扫描结果增加查询条件 增加搜索字段：管理员1，管理员2，指定管理员，管理状态，是否虚拟机，CMDB登记状态（手动）

### 优化

- 优化数据库中cmdb_user_logs表存在明文记录密码的情况
- 优化前端暴露密码哈希值的问题

### 修复

- 修复菜单栏和首页/展示不一致问题

## [2.0.0.6] - 2025年4月10日

### 更新

- 在侧边栏底部添加版本信息显示功能
- 优化侧边栏滚动条样式，提升用户体验

### 优化

- 美化侧边栏滚动条，增加宽度并改进外观
- 优化版本信息对话框层级，确保始终显示在最前端
- 添加全局滚动条样式，提升整体界面一致性
- 使用环境变量配置后端服务地址，提高系统可维护性

### 修复

- 修复后端 CORS 配置问题，解决跨域请求错误
- 修复版本信息对话框与资产管理表格重叠问题

## [2.0.0.5] - 2025年4月9日

### 修复

- 解决后端jwt硬编码问题
- 解决前后端token过期时间不一致问题

## [2.0.0.1] - 2025年3月31日

### 创建

- 创建cmdb系统
