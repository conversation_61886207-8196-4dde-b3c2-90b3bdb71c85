/**
 * 部署重要性字段到设备管理表
 * 执行SQL脚本添加importance字段和相关数据字典
 */

const fs = require('fs');
const path = require('path');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const { connPG } = require('../db/pg');

async function deployImportanceField() {
    try {
        console.log('开始部署重要性字段到设备管理表...');

        // 读取SQL脚本文件
        const sqlFilePath = path.join(__dirname, '../../sql/add_importance_field_to_device_management.sql');
        
        if (!fs.existsSync(sqlFilePath)) {
            throw new Error(`SQL脚本文件不存在: ${sqlFilePath}`);
        }

        const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');
        
        // 执行SQL脚本
        console.log('执行SQL脚本...');
        await connPG.query(sqlScript);
        
        console.log('重要性字段部署成功！');
        
        // 验证数据字典是否添加成功
        const checkDictQuery = `
            SELECT dict_code, dict_name 
            FROM cmdb_data_dictionary 
            WHERE dict_type = 'importance' AND del_flag = '0'
            ORDER BY dict_code
        `;
        
        const dictResult = await connPG.query(checkDictQuery);
        console.log('已添加的重要性字典项:', dictResult.rows);
        
        // 验证表字段是否添加成功
        const checkColumnQuery = `
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'cmdb_device_management'
            AND column_name = 'importance'
        `;
        
        const columnResult = await connPG.query(checkColumnQuery);
        if (columnResult.rows.length > 0) {
            console.log('importance字段已成功添加到cmdb_device_management表:', columnResult.rows[0]);
        } else {
            console.log('警告: importance字段未找到');
        }
        
        // 验证视图是否更新成功
        const checkViewQuery = `
            SELECT column_name
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'v_cmdb_device_management'
            AND column_name = 'importance'
        `;
        
        const viewResult = await connPG.query(checkViewQuery);
        if (viewResult.rows.length > 0) {
            console.log('v_cmdb_device_management视图已成功更新，包含importance字段');
        } else {
            console.log('警告: v_cmdb_device_management视图中未找到importance字段');
        }
        
        console.log('部署验证完成！');
        
    } catch (error) {
        console.error('部署重要性字段失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    deployImportanceField()
        .then(() => {
            console.log('部署完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('部署失败:', error);
            process.exit(1);
        });
}

module.exports = { deployImportanceField };
