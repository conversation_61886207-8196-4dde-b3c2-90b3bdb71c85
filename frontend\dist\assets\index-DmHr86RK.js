import{_ as ae,ah as W,v as oe,B as ne,c as S,a as t,f as x,w as l,b as r,F as E,l as M,h as w,C as le,D as re,m as V,t as se,r as h,R as Z,o as ce,E as p,Q as ie,Y as de,e as g}from"./index-MGgq8mV5.js";import{s as C}from"./request-DGO27LS-.js";import{d as ue}from"./downloadUtils-__kqhe_R.js";const me={name:"EventManagementIndex",components:{Plus:ne,Search:oe,Document:W},setup(){const F=de(),s=h(null),f=Z({keyword:"",priority:"",reporter:"",assignee:"",system:"",startDate:"",endDate:""}),o=h([]),P=h([]),z=h(!1),c=Z({currentPage:1,pageSize:20,total:0}),i=h([]),d=h([]),b=h([]),y=h([]),k=h(!1),D=()=>{const e=localStorage.getItem("role_code"),a=localStorage.getItem("loginUsername");k.value=a==="admin"||e&&e.includes("D")},O=async()=>{try{const e=await C({url:"/api/get_cmdb_data_dictionary",method:"post",data:{dict_type:"T"}});e.code===0&&(i.value=e.msg)}catch{}},U=async()=>{try{const e=await C({url:"/api/get_cmdb_data_dictionary",method:"post",data:{dict_type:"U"}});e.code===0&&(d.value=e.msg.filter(a=>a&&a.dict_code!==null&&a.dict_name!==null))}catch{}},v=async()=>{try{const e=await C({url:"/api/get_system_list",method:"post"});e.code===0&&(b.value=e.msg.filter(a=>a&&a.system_abbreviation!==null&&a.system_abbreviation!==void 0))}catch{}},R=async()=>{try{const e=await C({url:"/api/get_user_list",method:"post"});e.code===0&&(y.value=e.msg.filter(a=>a&&a.username!==null&&a.username!==void 0&&a.real_name!==null&&a.real_name!==void 0))}catch{}},u=async()=>{z.value=!0;try{const e={currentPage:c.currentPage,pageSize:c.pageSize,...f},a=await C({url:"/api/get_ops_event_management",method:"post",data:e});a.code===0?(P.value=a.msg||[],P.value.forEach(_=>{_.formatted_report_time=j(_.report_time)}),c.total=a.total||0):p.error(a.msg||"获取事件列表失败")}catch{p.error("获取事件列表失败")}finally{z.value=!1}},B=e=>{e&&e.length===2?(f.startDate=e[0],f.endDate=e[1]):(f.startDate="",f.endDate="")},m=()=>{c.currentPage=1,u()},T=()=>{s.value&&s.value.resetFields(),Object.keys(f).forEach(e=>{f[e]=""}),o.value=[],c.currentPage=1,u()},L=e=>{c.pageSize=e,c.currentPage=1,u()},Y=e=>{c.currentPage=e,u()},I=()=>{u()},n=()=>{F.push("/ops_event_management/new")},N=e=>{F.push(`/ops_event_management/${e.id}?mode=view`)},q=e=>{F.push(`/ops_event_management/${e.id}`)},G=e=>{if(!k.value){p.error("您没有删除权限");return}ie.confirm(`确定要删除事件 "${e.event_id}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const a=await C({url:"/api/del_ops_event_management",method:"post",data:{id:e.id,username:localStorage.getItem("loginUsername")||"admin"}});a.code===0?(p.success("删除成功"),u()):p.error(`删除失败: ${a.msg}`)}catch{p.error("删除事件失败")}}).catch(()=>{})},J=async e=>{if(!e.id){p.warning("无效的事件记录");return}p.info("正在生成Word文档，请稍候...");const a=`/api/export_ops_event_word/${e.id}`,_=`事件报告_${e.event_id||"unknown"}.docx`;await ue(a,_,{},()=>p.success("事件报告导出成功"),A=>{p.error("导出事件报告失败")})},K=e=>({U00001:"danger",U00002:"warning",U00003:"success",U00004:"info"})[e]||"",j=e=>{if(!e)return"";try{const a=new Date(e);if(isNaN(a.getTime()))return e;const _=te=>String(te).padStart(2,"0"),A=a.getFullYear(),H=_(a.getMonth()+1),Q=_(a.getDate()),X=_(a.getHours()),$=_(a.getMinutes()),ee=_(a.getSeconds());return a.getHours()===0&&a.getMinutes()===0&&a.getSeconds()===0?`${A}-${H}-${Q}`:`${A}-${H}-${Q} ${X}:${$}:${ee}`}catch(a){return console.error("日期格式化错误:",a),e}};return ce(async()=>{D(),await Promise.all([O(),U(),v(),R()]),u()}),{searchFormRef:s,searchForm:f,dateRange:o,tableData:P,tableLoading:z,pagination:c,eventTypeOptions:i,priorityOptions:d,systemOptions:b,userOptions:y,hasDeletePermission:k,formatDateTime:j,handleDateRangeChange:B,handleSearch:m,resetSearch:T,handleSizeChange:L,handleCurrentChange:Y,handleSortChange:I,handleAdd:n,handleView:N,handleEdit:q,handleDelete:G,handleExport:J,getPriorityType:K,Document:W}}},_e={class:"app-container"},pe={class:"button-container"},ge={class:"action-bar unified-action-bar"},fe={class:"action-bar-left"},he={class:"action-buttons"},be={class:"pagination-container"};function ye(F,s,f,o,P,z){const c=r("el-input"),i=r("el-form-item"),d=r("el-col"),b=r("el-option"),y=r("el-select"),k=r("el-date-picker"),D=r("el-row"),O=r("Search"),U=r("el-icon"),v=r("el-button"),R=r("el-form"),u=r("el-card"),B=r("Plus"),m=r("el-table-column"),T=r("el-tag"),L=r("el-table"),Y=r("el-pagination"),I=re("loading");return g(),S("div",_e,[t(u,{class:"search-card"},{default:l(()=>[t(R,{model:o.searchForm,ref:"searchFormRef","label-width":"100px","label-position":"right"},{default:l(()=>[t(D,{gutter:20},{default:l(()=>[t(d,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[t(i,{label:"关键字"},{default:l(()=>[t(c,{modelValue:o.searchForm.keyword,"onUpdate:modelValue":s[0]||(s[0]=n=>o.searchForm.keyword=n),placeholder:"事件编号或标题",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[t(i,{label:"事件级别"},{default:l(()=>[t(y,{modelValue:o.searchForm.priority,"onUpdate:modelValue":s[1]||(s[1]=n=>o.searchForm.priority=n),placeholder:"请选择",clearable:"",class:"form-control"},{default:l(()=>[(g(!0),S(E,null,M(o.priorityOptions,n=>(g(),V(b,{key:n.dict_code,label:n.dict_name,value:n.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(d,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[t(i,{label:"首次发生时间"},{default:l(()=>[t(k,{modelValue:o.dateRange,"onUpdate:modelValue":s[2]||(s[2]=n=>o.dateRange=n),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:o.handleDateRangeChange,class:"form-control"},null,8,["modelValue","onChange"])]),_:1})]),_:1})]),_:1}),t(D,{gutter:20},{default:l(()=>[t(d,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[t(i,{label:"值班经理"},{default:l(()=>[t(y,{modelValue:o.searchForm.reporter,"onUpdate:modelValue":s[3]||(s[3]=n=>o.searchForm.reporter=n),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:l(()=>[(g(!0),S(E,null,M(o.userOptions,n=>(g(),V(b,{key:n.username,label:n.real_name,value:n.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(d,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[t(i,{label:"处理人"},{default:l(()=>[t(y,{modelValue:o.searchForm.assignee,"onUpdate:modelValue":s[4]||(s[4]=n=>o.searchForm.assignee=n),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:l(()=>[(g(!0),S(E,null,M(o.userOptions,n=>(g(),V(b,{key:n.username,label:n.real_name,value:n.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(d,{xs:24,sm:12,md:8,lg:6},{default:l(()=>[t(i,{label:"影响系统"},{default:l(()=>[t(y,{modelValue:o.searchForm.system,"onUpdate:modelValue":s[5]||(s[5]=n=>o.searchForm.system=n),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:l(()=>[(g(!0),S(E,null,M(o.systemOptions,n=>(g(),V(b,{key:n.system_abbreviation,label:n.system_abbreviation,value:n.system_abbreviation},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(D,{gutter:20},{default:l(()=>[t(d,{xs:24,sm:12,md:8,lg:24,class:"search-buttons-col"},{default:l(()=>[t(i,null,{default:l(()=>[x("div",pe,[t(v,{type:"primary",onClick:o.handleSearch},{default:l(()=>[t(U,null,{default:l(()=>[t(O)]),_:1}),w("查询 ")]),_:1},8,["onClick"]),t(v,{onClick:o.resetSearch},{default:l(()=>[w("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),x("div",ge,[x("div",fe,[t(v,{type:"success",onClick:o.handleAdd},{default:l(()=>[t(U,null,{default:l(()=>[t(B)]),_:1}),w("新增事件 ")]),_:1},8,["onClick"])])]),t(u,{class:"table-card"},{default:l(()=>[le((g(),V(L,{data:o.tableData,border:"",stripe:"",style:{width:"100%"},"table-layout":"auto","header-cell-style":{background:"#f5f7fa",color:"#606266"},onSortChange:o.handleSortChange},{default:l(()=>[t(m,{prop:"event_id",label:"事件编号","min-width":"120",sortable:""}),t(m,{prop:"title",label:"事件标题","min-width":"200","show-overflow-tooltip":"",sortable:""}),t(m,{label:"事件级别","min-width":"100",align:"center",sortable:""},{default:l(n=>[t(T,{type:o.getPriorityType(n.row.priority)},{default:l(()=>[w(se(n.row.priority_name_display),1)]),_:2},1032,["type"])]),_:1}),t(m,{prop:"system",label:"影响系统","min-width":"120",sortable:""}),t(m,{prop:"reporter_name",label:"值班经理","min-width":"120",sortable:""}),t(m,{prop:"assignees_name_display",label:"处理人","min-width":"150",sortable:"","show-overflow-tooltip":""}),t(m,{prop:"formatted_report_time",label:"首次发生时间","min-width":"160",sortable:""}),t(m,{label:"操作",width:"160",fixed:"right"},{default:l(n=>[x("div",he,[t(v,{size:"small",type:"primary",onClick:N=>o.handleEdit(n.row),class:"action-btn"},{default:l(()=>[w(" 详情 ")]),_:2},1032,["onClick"]),t(v,{size:"small",type:"warning",onClick:N=>o.handleExport(n.row),icon:o.Document,class:"action-btn"},{default:l(()=>[w(" 导出 ")]),_:2},1032,["onClick","icon"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[I,o.tableLoading]]),x("div",be,[t(Y,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:o.pagination.total,"page-size":o.pagination.pageSize,"current-page":o.pagination.currentPage,"page-sizes":[10,20,50,100],onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])]),_:1})])}const ke=ae(me,[["render",ye],["__scopeId","data-v-9a7dd935"]]);export{ke as default};
