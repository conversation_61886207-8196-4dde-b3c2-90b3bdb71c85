import{_ as H,s as J,A as K,j as W,B as X,c as T,a,f as V,w as l,b as c,h as n,C as Y,D as Z,m,t as z,x as j,r as w,R as O,S as $,T as ee,o as te,U as q,E as r,V as G,Q as ae,e as d}from"./index-MGgq8mV5.js";import{S as le,g as oe,r as se,u as ne,d as re,a as ie}from"./schedule-task-form-CE87gyAH.js";const ce={name:"CmdbScheduleTasks",components:{Plus:X,RefreshRight:W,Download:K,Setting:J,ScheduleTaskForm:le},setup(){const b=w([]),i=w(0),D=w(!1),o=w(!1),h=O({task_name:"",schedule_type:"",status:"",currentPage:1,pageSize:10,sortProp:"created_at",sortOrder:"desc"}),S=O({form:!1}),v=w({}),y=w("add"),C=w(null),p=$(()=>y.value==="add"?"新增调度任务":"编辑调度任务"),_=w(!1),s=async()=>{if(_.value){console.log("组件已卸载，取消获取调度任务列表");return}D.value=!0,b.value=[];try{if(console.log("开始获取调度任务列表:",h),await new Promise(t=>setTimeout(t,50)),_.value){console.log("组件已卸载，取消获取调度任务列表");return}const e=await oe(h);if(console.log("调度任务列表响应:",e),_.value){console.log("组件已卸载，取消处理调度任务列表响应");return}if(e&&e.code===0){if(await new Promise(t=>setTimeout(t,50)),_.value){console.log("组件已卸载，取消设置调度任务列表数据");return}e.msg&&Array.isArray(e.msg)?b.value=e.msg:(console.warn("调度任务列表响应不是数组:",e.msg),b.value=[]),i.value=e.total||0,e.msg&&e.msg.error&&(console.warn("获取调度任务列表有警告:",e.msg.error),_.value||r.warning(`数据加载可能不完整: ${e.msg.error}`))}else console.error("获取调度任务列表失败:",e==null?void 0:e.msg),_.value||r.error((e==null?void 0:e.msg)||"获取调度任务列表失败"),b.value=[],i.value=0}catch(e){if(console.error("获取调度任务列表失败:",e),_.value||r.error(`获取调度任务列表失败: ${e.message||"未知错误"}`),b.value=[],i.value=0,!_.value)try{const t=await G.get("/api/discovery/test_db_connection");if(console.log("数据库连接测试结果:",t.data),t.data&&t.data.code===0){const u=t.data.msg;u&&!u.schedule_tasks_table_exists&&r.error("调度任务表不存在，请联系管理员初始化数据库")}}catch(t){console.error("测试数据库连接失败:",t)}}finally{_.value||(D.value=!1)}};ee(()=>{console.log("调度任务组件即将卸载"),_.value=!0});const Q=()=>{h.currentPage=1,s()},B=()=>{h.task_name="",h.schedule_type="",h.status="",h.currentPage=1,s()},R=()=>{s()},E=()=>{r.info("导出功能开发中")},x=e=>{h.pageSize=e,s()},U=e=>{h.currentPage=e,s()},A=()=>{y.value="add",v.value={task_name:"",description:"",schedule_type:"manual",schedule_value:"",status:"inactive",discovery_task_ids:[]},S.form=!0},f=async e=>{if(!e||typeof e!="object"||!e.id){console.error("编辑调度任务失败: 无效的行数据",e),r.error("编辑调度任务失败: 无效的数据");return}y.value="edit";try{console.log("开始获取调度任务详情，ID:",e.id);const t=await ie({id:e.id});if(t&&t.code===0&&t.msg){const u=t.msg,k=u.items&&Array.isArray(u.items)?u.items.filter(P=>P&&P.discovery_task_id).map(P=>P.discovery_task_id):[];v.value={...e,discovery_task_ids:k},console.log("调度任务详情:",u),console.log("提取的发现任务IDs:",k),console.log("设置的表单数据:",v.value),console.log("编辑调度任务，已关联发现任务数量:",k.length)}else console.warn("获取调度任务详情失败:",t),v.value={...e,discovery_task_ids:[]},r.warning("获取关联发现任务失败，请重新选择关联任务")}catch(t){console.error("获取调度任务详情失败:",t),v.value={...e,discovery_task_ids:[]},r.warning("获取关联发现任务失败，请重新选择关联任务")}q(()=>{S.form=!0})},g=()=>{S.form=!1,s()},I=e=>{ae.confirm(`确定要删除调度任务 "${e.task_name}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{console.log("开始删除调度任务:",e);const t=await re({id:e.id});console.log("删除调度任务响应:",t),t.code===0?(r.success("删除成功"),s()):(console.error("删除调度任务失败:",t.msg),r.error(t.msg||"删除失败"))}catch(t){console.error("删除调度任务失败:",t);let u="删除调度任务失败";t.response&&t.response.data&&t.response.data.msg?u=t.response.data.msg:t.message&&(u=t.message),r.error(u)}}).catch(()=>{})},M=async(e,t)=>{const u={active:"激活",inactive:"停用",paused:"暂停"};try{const k=await ne({id:e.id,status:t});k.code===0?(r.success(`已${u[t]}调度任务`),s()):r.error(k.msg||`${u[t]}失败`)}catch(k){console.error(`${u[t]}调度任务失败:`,k),r.error(`${u[t]}调度任务失败`)}},F=async e=>{try{const t=await se({id:e.id});t.code===0?(r.success("任务已开始执行"),s()):r.error(t.msg||"执行失败")}catch(t){console.error("执行调度任务失败:",t),r.error("执行调度任务失败")}},L=e=>e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-"):"-",N=async()=>{var e;o.value=!0;try{const t=await G.get("/api/discovery/schedule/init_schedule_tables");t.data&&t.data.code===0?(r.success(t.data.msg||"表初始化成功"),s()):r.error(((e=t.data)==null?void 0:e.msg)||"表初始化失败")}catch(t){console.error("初始化数据库表失败:",t),r.error(`初始化数据库表失败: ${t.message||"未知错误"}`)}finally{o.value=!1}};return te(()=>{q(()=>{s()})}),{list:b,total:i,listLoading:D,initLoading:o,listQuery:h,dialogVisible:S,formData:v,formMode:y,dialogTitle:p,scheduleTaskFormRef:C,getList:s,handleSearch:Q,handleReset:B,handleRefresh:R,handleExport:E,handleSizeChange:x,handleCurrentChange:U,handleAdd:A,handleEdit:f,handleFormSubmit:g,handleDelete:I,handleChangeStatus:M,handleRun:F,handleInitTables:N,formatDateTime:L}}},de={class:"app-container"},ue={class:"button-container"},_e={class:"action-bar unified-action-bar"},me={class:"action-bar-left"},fe={class:"action-bar-right"},ge={key:0},he={key:1},ye={key:0},pe={key:1},ve={class:"pagination-container"};function ke(b,i,D,o,h,S){const v=c("el-input"),y=c("el-form-item"),C=c("el-col"),p=c("el-option"),_=c("el-select"),s=c("el-button"),Q=c("el-row"),B=c("el-form"),R=c("el-card"),E=c("Plus"),x=c("el-icon"),U=c("RefreshRight"),A=c("Download"),f=c("el-table-column"),g=c("el-tag"),I=c("el-table"),M=c("el-pagination"),F=c("schedule-task-form"),L=c("el-dialog"),N=Z("loading");return d(),T("div",de,[a(R,{class:"search-card"},{default:l(()=>[a(B,{inline:!0,class:"search-form"},{default:l(()=>[a(Q,{gutter:10},{default:l(()=>[a(C,{xs:24,sm:12,md:6,lg:6},{default:l(()=>[a(y,{label:"任务名称",class:"form-item-with-label"},{default:l(()=>[a(v,{modelValue:o.listQuery.task_name,"onUpdate:modelValue":i[0]||(i[0]=e=>o.listQuery.task_name=e),placeholder:"请输入任务名称",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),a(C,{xs:24,sm:12,md:6,lg:6},{default:l(()=>[a(y,{label:"调度类型",class:"form-item-with-label"},{default:l(()=>[a(_,{modelValue:o.listQuery.schedule_type,"onUpdate:modelValue":i[1]||(i[1]=e=>o.listQuery.schedule_type=e),placeholder:"请选择调度类型",clearable:"",style:{width:"100%"}},{default:l(()=>[a(p,{label:"手动",value:"manual"}),a(p,{label:"一次性",value:"once"}),a(p,{label:"每日",value:"daily"}),a(p,{label:"每周",value:"weekly"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(C,{xs:24,sm:12,md:6,lg:6},{default:l(()=>[a(y,{label:"状态",class:"form-item-with-label"},{default:l(()=>[a(_,{modelValue:o.listQuery.status,"onUpdate:modelValue":i[2]||(i[2]=e=>o.listQuery.status=e),placeholder:"请选择状态",clearable:"",style:{width:"100%"}},{default:l(()=>[a(p,{label:"激活",value:"active"}),a(p,{label:"停用",value:"inactive"}),a(p,{label:"暂停",value:"paused"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(C,{xs:24,sm:12,md:6,lg:6,class:"search-buttons-col"},{default:l(()=>[a(y,{label:" ",class:"form-item-with-label search-buttons"},{default:l(()=>[V("div",ue,[a(s,{type:"primary",onClick:o.handleSearch},{default:l(()=>[n("查询")]),_:1},8,["onClick"]),a(s,{onClick:o.handleReset},{default:l(()=>[n("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),V("div",_e,[V("div",me,[a(s,{type:"primary",onClick:o.handleAdd},{default:l(()=>[a(x,null,{default:l(()=>[a(E)]),_:1}),n(" 新增调度任务 ")]),_:1},8,["onClick"]),a(s,{type:"success",onClick:o.handleRefresh},{default:l(()=>[a(x,null,{default:l(()=>[a(U)]),_:1}),n(" 刷新 ")]),_:1},8,["onClick"])]),V("div",fe,[a(s,{type:"info",onClick:o.handleExport},{default:l(()=>[a(x,null,{default:l(()=>[a(A)]),_:1}),n(" 导出数据 ")]),_:1},8,["onClick"])])]),a(R,{class:"table-card"},{default:l(()=>[Y((d(),m(I,{data:o.list,"element-loading-text":"加载中...",border:"",stripe:"","highlight-current-row":"",style:{width:"100%"},"header-cell-style":{background:"#f8f8f9",color:"#606266"}},{default:l(()=>[a(f,{prop:"id",label:"ID",width:"80",align:"center"}),a(f,{prop:"task_name",label:"任务名称","min-width":"150","show-overflow-tooltip":""}),a(f,{prop:"description",label:"描述","min-width":"200","show-overflow-tooltip":""}),a(f,{label:"调度类型",width:"100",align:"center"},{default:l(e=>[e.row.schedule_type==="manual"?(d(),m(g,{key:0,type:"info"},{default:l(()=>[n("手动")]),_:1})):e.row.schedule_type==="once"?(d(),m(g,{key:1,type:"success"},{default:l(()=>[n("一次性")]),_:1})):e.row.schedule_type==="daily"?(d(),m(g,{key:2,type:"primary"},{default:l(()=>[n("每日")]),_:1})):e.row.schedule_type==="weekly"?(d(),m(g,{key:3,type:"warning"},{default:l(()=>[n("每周")]),_:1})):(d(),m(g,{key:4,type:"info"},{default:l(()=>[n(z(e.row.schedule_type),1)]),_:2},1024))]),_:1}),a(f,{prop:"schedule_value",label:"调度值","min-width":"150","show-overflow-tooltip":""}),a(f,{label:"下次执行时间","min-width":"180","show-overflow-tooltip":""},{default:l(e=>[e.row.next_run_time?(d(),T("span",ge,z(o.formatDateTime(e.row.next_run_time)),1)):(d(),T("span",he,"-"))]),_:1}),a(f,{label:"上次执行时间","min-width":"180","show-overflow-tooltip":""},{default:l(e=>[e.row.last_run_time?(d(),T("span",ye,z(o.formatDateTime(e.row.last_run_time)),1)):(d(),T("span",pe,"-"))]),_:1}),a(f,{label:"关联任务数",width:"100",align:"center"},{default:l(e=>[a(g,{type:"info"},{default:l(()=>[n(z(e.row.task_count||0),1)]),_:2},1024)]),_:1}),a(f,{label:"状态",width:"100",align:"center"},{default:l(e=>[e.row.status==="active"?(d(),m(g,{key:0,type:"success"},{default:l(()=>[n("激活")]),_:1})):e.row.status==="inactive"?(d(),m(g,{key:1,type:"info"},{default:l(()=>[n("停用")]),_:1})):e.row.status==="paused"?(d(),m(g,{key:2,type:"warning"},{default:l(()=>[n("暂停")]),_:1})):(d(),m(g,{key:3,type:"info"},{default:l(()=>[n(z(e.row.status),1)]),_:2},1024))]),_:1}),a(f,{label:"操作",width:"320",align:"center",fixed:"right"},{default:l(e=>[e.row.status!=="active"?(d(),m(s,{key:0,type:"success",size:"small",onClick:t=>o.handleChangeStatus(e.row,"active")},{default:l(()=>[n(" 激活 ")]),_:2},1032,["onClick"])):j("",!0),e.row.status==="active"?(d(),m(s,{key:1,type:"warning",size:"small",onClick:t=>o.handleChangeStatus(e.row,"paused")},{default:l(()=>[n(" 暂停 ")]),_:2},1032,["onClick"])):j("",!0),e.row.status==="active"||e.row.status==="paused"?(d(),m(s,{key:2,type:"info",size:"small",onClick:t=>o.handleChangeStatus(e.row,"inactive")},{default:l(()=>[n(" 停用 ")]),_:2},1032,["onClick"])):j("",!0),a(s,{type:"primary",size:"small",onClick:t=>o.handleRun(e.row)},{default:l(()=>[n(" 执行 ")]),_:2},1032,["onClick"]),a(s,{type:"primary",size:"small",onClick:t=>o.handleEdit(e.row)},{default:l(()=>[n(" 编辑 ")]),_:2},1032,["onClick"]),a(s,{type:"danger",size:"small",onClick:t=>o.handleDelete(e.row)},{default:l(()=>[n(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[N,o.listLoading]]),V("div",ve,[a(M,{"current-page":o.listQuery.currentPage,"onUpdate:currentPage":i[3]||(i[3]=e=>o.listQuery.currentPage=e),"page-size":o.listQuery.pageSize,"onUpdate:pageSize":i[4]||(i[4]=e=>o.listQuery.pageSize=e),"page-sizes":[10,20,50,100],total:o.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange,background:""},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),a(L,{modelValue:o.dialogVisible.form,"onUpdate:modelValue":i[6]||(i[6]=e=>o.dialogVisible.form=e),title:o.dialogTitle,width:"900px","destroy-on-close":"",fullscreen:!1,"close-on-click-modal":!1,"close-on-press-escape":!1},{default:l(()=>[a(F,{ref:"scheduleTaskFormRef","form-data":o.formData,mode:o.formMode,onSubmit:o.handleFormSubmit,onCancel:i[5]||(i[5]=e=>o.dialogVisible.form=!1)},null,8,["form-data","mode","onSubmit"])]),_:1},8,["modelValue","title"])])}const Ce=H(ce,[["render",ke],["__scopeId","data-v-5efdc151"]]);export{Ce as default};
