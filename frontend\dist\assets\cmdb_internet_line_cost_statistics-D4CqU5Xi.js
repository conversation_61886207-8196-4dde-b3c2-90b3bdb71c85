import{_ as H,k as J,ar as K,A as Q,G as X,c as b,a as t,m as U,x as I,w as s,b as g,h as _,f as e,t as o,C as x,as as q,D as Z,F as R,l as A,V as B,E as m,r as f,R as G,p as $,q as tt,e as h,a5 as Y}from"./index-MGgq8mV5.js";const et={name:"InternetLineCostStatistics",components:{Refresh:X,Download:Q,PieChart:K,Warning:J},setup(){const l=f(!1),d=f(!1),C=f(!1),r=f(!1),N=f(!1),n=f(!1),D=f(!1),T=G({groupBy:"provider",startDate:"",endDate:""}),P=G({groupBy:"month",startDate:"",endDate:""}),u=f([]),V=f({}),S=f([]),k=f([]),y=f({}),p=f({detailedUsage:[],totalUsage:{},providerUsage:[]}),E=f({ipUtilization:[],portUsage:[],datacenterDistribution:[],totalStats:{}}),M=f({trendData:[],providerTrend:[],cumulativeData:[],groupBy:"month"}),v=f({distribution:[],providerDistribution:[],lineTypeDistribution:[],bandwidthDistribution:[],totalStats:{}});return{loading:l,alertsLoading:d,showChart:C,usageLoading:r,ipUtilizationLoading:N,trendLoading:n,datacenterLoading:D,filterForm:T,trendForm:P,statisticsData:u,totalStats:V,providerStats:S,contractAlerts:k,alertStats:y,lineUsageStats:p,ipUtilizationStats:E,costTrendStats:M,datacenterStats:v}},methods:{async getStatistics(){this.loading=!0;try{const l=await B.post("/api/get_cost_statistics",{groupBy:this.filterForm.groupBy,startDate:this.filterForm.startDate,endDate:this.filterForm.endDate});if(l.data.code===0){const d=l.data.msg;this.statisticsData=d.statistics||[],this.totalStats=d.total||{},this.providerStats=d.providerStats||[]}else m.error(l.data.msg||"获取统计数据失败")}catch(l){console.error("获取统计数据失败:",l),m.error("获取统计数据失败")}finally{this.loading=!1}},async getContractAlerts(){this.alertsLoading=!0;try{const l=await B.post("/api/get_contract_expiry_alerts",{alertDays:30});if(l.data.code===0){const d=l.data.msg;this.contractAlerts=d.alerts||[],this.alertStats=d.summary||{}}else m.error(l.data.msg||"获取合同提醒失败")}catch(l){console.error("获取合同提醒失败:",l),m.error("获取合同提醒失败")}finally{this.alertsLoading=!1}},resetFilter(){this.filterForm.groupBy="provider",this.filterForm.startDate="",this.filterForm.endDate="",this.getStatistics()},async exportStatistics(){try{const l=await B.post("/api/export_internet_line_data",{exportType:"statistics",groupBy:this.filterForm.groupBy,startDate:this.filterForm.startDate,endDate:this.filterForm.endDate},{responseType:"blob"}),d=new Blob([l.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),C=window.URL.createObjectURL(d),r=document.createElement("a");r.href=C,r.download=`互联网线路费用统计_${new Date().toISOString().slice(0,10)}.xlsx`,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(C),m.success("导出成功")}catch(l){console.error("导出失败:",l),m.error("导出失败")}},toggleChartView(){this.showChart=!this.showChart},formatCurrency(l){return!l||isNaN(l)?"0.00":Number(l).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})},getPercentage(l){return!this.totalStats.total_monthly_cost||!l?0:(l/this.totalStats.total_monthly_cost*100).toFixed(1)},getProviderPercentage(l){return!this.totalStats.total_monthly_cost||!l?0:(l/this.totalStats.total_monthly_cost*100).toFixed(1)},getGroupByLabel(){return{provider:"运营商",lineType:"线路类型",datacenter:"机房"}[this.filterForm.groupBy]||"分组"},getCurrentDateRange(){return this.filterForm.startDate&&this.filterForm.endDate?`${this.filterForm.startDate} 至 ${this.filterForm.endDate}`:this.filterForm.startDate?`${this.filterForm.startDate} 至今`:this.filterForm.endDate?`截至 ${this.filterForm.endDate}`:"全部数据"},getBarColor(l){const d=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#C71585","#FF6347","#32CD32","#1E90FF","#FF69B4","#FFA500","#9370DB"];return d[l%d.length]},async getLineUsageStatistics(){this.usageLoading=!0;try{const l=await B.post("/api/get_line_usage_statistics",{});l.data.code===0?this.lineUsageStats=l.data.msg:m.error(l.data.msg||"获取线路使用统计失败")}catch(l){console.error("获取线路使用统计失败:",l),m.error("获取线路使用统计失败")}finally{this.usageLoading=!1}},async getIpResourceUtilization(){this.ipUtilizationLoading=!0;try{const l=await B.post("/api/get_ip_resource_utilization",{});l.data.code===0?this.ipUtilizationStats=l.data.msg:m.error(l.data.msg||"获取IP资源利用率统计失败")}catch(l){console.error("获取IP资源利用率统计失败:",l),m.error("获取IP资源利用率统计失败")}finally{this.ipUtilizationLoading=!1}},async getCostTrendStatistics(){this.trendLoading=!0;try{const l=await B.post("/api/get_cost_trend_statistics",{groupBy:this.trendForm.groupBy,startDate:this.trendForm.startDate,endDate:this.trendForm.endDate});l.data.code===0?this.costTrendStats=l.data.msg:m.error(l.data.msg||"获取费用趋势统计失败")}catch(l){console.error("获取费用趋势统计失败:",l),m.error("获取费用趋势统计失败")}finally{this.trendLoading=!1}},async getDatacenterResourceDistribution(){this.datacenterLoading=!0;try{const l=await B.post("/api/get_datacenter_resource_distribution",{});l.data.code===0?this.datacenterStats=l.data.msg:m.error(l.data.msg||"获取机房资源分布统计失败")}catch(l){console.error("获取机房资源分布统计失败:",l),m.error("获取机房资源分布统计失败")}finally{this.datacenterLoading=!1}},getUsageRateType(l){return l>=80?"success":l>=50?"warning":"danger"},getTrendPercentage(l){if(!this.costTrendStats.trendData.length)return 0;const d=Math.max(...this.costTrendStats.trendData.map(C=>C.total_monthly_fee));return!d||!l?0:(l/d*100).toFixed(1)},getCumulativePercentage(l){if(!this.costTrendStats.cumulativeData.length)return 0;const d=Math.max(...this.costTrendStats.cumulativeData.map(C=>C.cumulative_monthly_fee));return!d||!l?0:(l/d*100).toFixed(1)},getTrendBarColor(l){const d=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#C71585","#FF6347","#32CD32"];return d[l%d.length]},getDatacenterPercentage(l){var d;return!((d=this.datacenterStats.totalStats)!=null&&d.total_monthly_fee)||!l?0:(l/this.datacenterStats.totalStats.total_monthly_fee*100).toFixed(1)},getDatacenterBarColor(l){const d=["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#C71585","#FF6347","#32CD32","#1E90FF","#FF69B4","#FFA500","#9370DB"];return d[l%d.length]}},mounted(){this.getStatistics(),this.getContractAlerts(),this.getLineUsageStatistics(),this.getIpResourceUtilization(),this.getCostTrendStatistics(),this.getDatacenterResourceDistribution()}},c=l=>($("data-v-860fb192"),l=l(),tt(),l),at={class:"cost-statistics"},st={class:"card-header"},lt=c(()=>e("span",null,"费用统计概览",-1)),rt={class:"stat-item"},ot={class:"stat-value"},nt=c(()=>e("div",{class:"stat-label"},"线路总数",-1)),it={class:"stat-item"},dt={class:"stat-value"},ct=c(()=>e("div",{class:"stat-label"},"月费用总计",-1)),_t={class:"stat-item"},ut={class:"stat-value"},ht=c(()=>e("div",{class:"stat-label"},"年费用总计",-1)),gt={class:"stat-item"},ft={class:"stat-value"},mt=c(()=>e("div",{class:"stat-label"},"平均月费用",-1)),pt={class:"card-header"},vt={class:"chart-container"},bt={class:"chart-header"},yt={class:"chart-content"},wt={class:"chart-bars"},Ct={class:"bar-info"},St={class:"bar-label"},Dt={class:"bar-value"},Ft={class:"bar-container"},Ut={class:"bar-percentage"},xt={class:"card-header"},kt=c(()=>e("span",null,"合同到期提醒",-1)),Bt={class:"alert-stat-item expired"},Pt={class:"alert-stat-value"},Lt=c(()=>e("div",{class:"alert-stat-label"},"已过期合同",-1)),zt={class:"alert-stat-item expiring"},Tt={class:"alert-stat-value"},Vt=c(()=>e("div",{class:"alert-stat-label"},"即将过期合同",-1)),It={class:"alert-stat-item total"},Rt={class:"alert-stat-value"},At=c(()=>e("div",{class:"alert-stat-label"},"受影响月费用",-1)),Yt=c(()=>e("div",{class:"card-header"},[e("span",null,"运营商费用分布")],-1)),Et={class:"card-header"},Mt=c(()=>e("span",null,"线路使用情况统计",-1)),Nt={class:"usage-stat-item"},qt={class:"usage-stat-value"},Gt=c(()=>e("div",{class:"usage-stat-label"},"线路总数",-1)),Ot={class:"usage-stat-item"},Wt={class:"usage-stat-value"},jt=c(()=>e("div",{class:"usage-stat-label"},"已使用线路",-1)),Ht={class:"usage-stat-item"},Jt={class:"usage-stat-value"},Kt=c(()=>e("div",{class:"usage-stat-label"},"未使用线路",-1)),Qt={class:"usage-stat-item"},Xt={class:"usage-stat-value"},Zt=c(()=>e("div",{class:"usage-stat-label"},"总体使用率",-1)),$t={class:"card-header"},te=c(()=>e("span",null,"IP资源利用率统计",-1)),ee={class:"ip-stat-item"},ae={class:"ip-stat-value"},se=c(()=>e("div",{class:"ip-stat-label"},"总映射数",-1)),le={class:"ip-stat-item"},re={class:"ip-stat-value"},oe=c(()=>e("div",{class:"ip-stat-label"},"唯一IP数",-1)),ne={class:"ip-stat-item"},ie={class:"ip-stat-value"},de=c(()=>e("div",{class:"ip-stat-label"},"TCP映射",-1)),ce={class:"ip-stat-item"},_e={class:"ip-stat-value"},ue=c(()=>e("div",{class:"ip-stat-label"},"UDP映射",-1)),he={class:"datacenter-ip-distribution"},ge=c(()=>e("h4",null,"机房IP资源分布",-1)),fe={class:"protocol-distribution"},me={class:"card-header"},pe=c(()=>e("span",null,"费用趋势分析",-1)),ve={class:"trend-controls"},be={class:"trend-chart-container"},ye={class:"trend-chart"},we=c(()=>e("h4",null,"费用趋势图",-1)),Ce={class:"chart-bars"},Se={class:"trend-bar-info"},De={class:"trend-bar-label"},Fe={class:"trend-bar-value"},Ue={class:"trend-bar-count"},xe={class:"trend-bar-container"},ke={class:"trend-bar-percentage"},Be={key:0,class:"cumulative-chart"},Pe=c(()=>e("h4",null,"累计费用趋势",-1)),Le={class:"cumulative-bars"},ze={class:"cumulative-bar-info"},Te={class:"cumulative-bar-label"},Ve={class:"cumulative-bar-value"},Ie={class:"cumulative-bar-count"},Re={class:"cumulative-bar-container"},Ae={class:"card-header"},Ye=c(()=>e("span",null,"机房资源分布统计",-1)),Ee={class:"datacenter-stat-item"},Me={class:"datacenter-stat-value"},Ne=c(()=>e("div",{class:"datacenter-stat-label"},"机房总数",-1)),qe={class:"datacenter-stat-item"},Ge={class:"datacenter-stat-value"},Oe=c(()=>e("div",{class:"datacenter-stat-label"},"线路总数",-1)),We={class:"datacenter-stat-item"},je={class:"datacenter-stat-value"},He=c(()=>e("div",{class:"datacenter-stat-label"},"总月费用",-1)),Je={class:"datacenter-stat-item"},Ke={class:"datacenter-stat-value"},Qe=c(()=>e("div",{class:"datacenter-stat-label"},"平均月费用",-1)),Xe={class:"contract-status"},Ze={class:"datacenter-chart-container"},$e=c(()=>e("h4",null,"机房费用分布图",-1)),ta={class:"datacenter-chart-bars"},ea={class:"datacenter-bar-info"},aa={class:"datacenter-bar-label"},sa={class:"datacenter-bar-value"},la={class:"datacenter-bar-count"},ra={class:"datacenter-bar-container"},oa={class:"datacenter-bar-percentage"};function na(l,d,C,r,N,n){const D=g("el-option"),T=g("el-select"),P=g("el-form-item"),u=g("el-col"),V=g("el-date-picker"),S=g("el-row"),k=g("Refresh"),y=g("el-icon"),p=g("el-button"),E=g("Download"),M=g("el-form"),v=g("el-card"),F=g("el-tag"),O=g("PieChart"),i=g("el-table-column"),L=g("el-table"),W=g("Warning"),j=g("el-badge"),z=Z("loading");return h(),b("div",at,[t(v,{class:"filter-card"},{default:s(()=>[t(M,{inline:!0,model:r.filterForm,"label-width":"100px"},{default:s(()=>[t(S,{gutter:20},{default:s(()=>[t(u,{span:8},{default:s(()=>[t(P,{label:"统计维度"},{default:s(()=>[t(T,{modelValue:r.filterForm.groupBy,"onUpdate:modelValue":d[0]||(d[0]=a=>r.filterForm.groupBy=a),placeholder:"请选择统计维度",onChange:n.getStatistics},{default:s(()=>[t(D,{label:"按运营商",value:"provider"}),t(D,{label:"按线路类型",value:"lineType"}),t(D,{label:"按机房",value:"datacenter"})]),_:1},8,["modelValue","onChange"])]),_:1})]),_:1}),t(u,{span:8},{default:s(()=>[t(P,{label:"开始日期"},{default:s(()=>[t(V,{modelValue:r.filterForm.startDate,"onUpdate:modelValue":d[1]||(d[1]=a=>r.filterForm.startDate=a),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",placeholder:"选择开始日期",clearable:"",onChange:n.getStatistics},null,8,["modelValue","onChange"])]),_:1})]),_:1}),t(u,{span:8},{default:s(()=>[t(P,{label:"结束日期"},{default:s(()=>[t(V,{modelValue:r.filterForm.endDate,"onUpdate:modelValue":d[2]||(d[2]=a=>r.filterForm.endDate=a),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",placeholder:"选择结束日期",clearable:"",onChange:n.getStatistics},null,8,["modelValue","onChange"])]),_:1})]),_:1})]),_:1}),t(S,null,{default:s(()=>[t(u,{span:24},{default:s(()=>[t(P,null,{default:s(()=>[t(p,{type:"primary",onClick:n.getStatistics},{default:s(()=>[t(y,null,{default:s(()=>[t(k)]),_:1}),_(" 刷新统计 ")]),_:1},8,["onClick"]),t(p,{onClick:n.resetFilter},{default:s(()=>[_("重置")]),_:1},8,["onClick"]),t(p,{onClick:n.exportStatistics},{default:s(()=>[t(y,null,{default:s(()=>[t(E)]),_:1}),_(" 导出统计 ")]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),t(v,{class:"overview-card"},{header:s(()=>[e("div",st,[lt,t(F,{type:"info"},{default:s(()=>[_(o(n.getCurrentDateRange()),1)]),_:1})])]),default:s(()=>[t(S,{gutter:20,class:"overview-stats"},{default:s(()=>[t(u,{span:6},{default:s(()=>[e("div",rt,[e("div",ot,o(r.totalStats.total_lines||0),1),nt])]),_:1}),t(u,{span:6},{default:s(()=>[e("div",it,[e("div",dt,"¥"+o(n.formatCurrency(r.totalStats.total_monthly_cost)),1),ct])]),_:1}),t(u,{span:6},{default:s(()=>[e("div",_t,[e("div",ut,"¥"+o(n.formatCurrency(r.totalStats.total_annual_cost)),1),ht])]),_:1}),t(u,{span:6},{default:s(()=>[e("div",gt,[e("div",ft,"¥"+o(n.formatCurrency(r.totalStats.avg_monthly_cost)),1),mt])]),_:1})]),_:1})]),_:1}),t(v,{class:"statistics-card"},{header:s(()=>[e("div",pt,[e("span",null,o(n.getGroupByLabel())+"费用统计",1),t(p,{size:"small",onClick:n.toggleChartView},{default:s(()=>[t(y,null,{default:s(()=>[t(O)]),_:1}),_(" "+o(r.showChart?"表格视图":"图表视图"),1)]),_:1},8,["onClick"])])]),default:s(()=>[x(e("div",null,[x((h(),U(L,{data:r.statisticsData,stripe:"",border:"","element-loading-text":"加载统计数据中..."},{default:s(()=>[t(i,{prop:"group_name",label:n.getGroupByLabel(),"min-width":"120"},null,8,["label"]),t(i,{prop:"line_count",label:"线路数量",width:"100",align:"center"}),t(i,{prop:"total_monthly_fee",label:"月费用合计",width:"120",align:"right"},{default:s(a=>[_(" ¥"+o(n.formatCurrency(a.row.total_monthly_fee)),1)]),_:1}),t(i,{prop:"avg_monthly_fee",label:"月费用平均",width:"120",align:"right"},{default:s(a=>[_(" ¥"+o(n.formatCurrency(a.row.avg_monthly_fee)),1)]),_:1}),t(i,{prop:"total_annual_fee",label:"年费用合计",width:"120",align:"right"},{default:s(a=>[_(" ¥"+o(n.formatCurrency(a.row.total_annual_fee)),1)]),_:1}),t(i,{prop:"min_monthly_fee",label:"最低月费用",width:"120",align:"right"},{default:s(a=>[_(" ¥"+o(n.formatCurrency(a.row.min_monthly_fee)),1)]),_:1}),t(i,{prop:"max_monthly_fee",label:"最高月费用",width:"120",align:"right"},{default:s(a=>[_(" ¥"+o(n.formatCurrency(a.row.max_monthly_fee)),1)]),_:1}),t(i,{label:"费用占比",width:"100",align:"center"},{default:s(a=>[_(o(n.getPercentage(a.row.total_monthly_fee))+"% ",1)]),_:1})]),_:1},8,["data"])),[[z,r.loading]])],512),[[q,!r.showChart]]),x(e("div",vt,[e("div",bt,[e("h3",null,o(n.getGroupByLabel())+"费用分布",1)]),e("div",yt,[e("div",wt,[(h(!0),b(R,null,A(r.statisticsData,(a,w)=>(h(),b("div",{key:w,class:"chart-bar-item"},[e("div",Ct,[e("span",St,o(a.group_name),1),e("span",Dt,"¥"+o(n.formatCurrency(a.total_monthly_fee)),1)]),e("div",Ft,[e("div",{class:"bar-fill",style:Y({width:n.getPercentage(a.total_monthly_fee)+"%",backgroundColor:n.getBarColor(w)})},null,4)]),e("div",Ut,o(n.getPercentage(a.total_monthly_fee))+"%",1)]))),128))])])],512),[[q,r.showChart]])]),_:1}),t(v,{class:"alerts-card"},{header:s(()=>[e("div",xt,[kt,t(j,{value:r.alertStats.totalCount,max:99,type:"warning"},{default:s(()=>[t(p,{size:"small",onClick:n.getContractAlerts},{default:s(()=>[t(y,null,{default:s(()=>[t(W)]),_:1}),_(" 刷新提醒 ")]),_:1},8,["onClick"])]),_:1},8,["value"])])]),default:s(()=>[t(S,{gutter:20,class:"alert-stats"},{default:s(()=>[t(u,{span:8},{default:s(()=>[e("div",Bt,[e("div",Pt,o(r.alertStats.expiredCount||0),1),Lt])]),_:1}),t(u,{span:8},{default:s(()=>[e("div",zt,[e("div",Tt,o(r.alertStats.expiringCount||0),1),Vt])]),_:1}),t(u,{span:8},{default:s(()=>[e("div",It,[e("div",Rt,"¥"+o(n.formatCurrency(r.alertStats.totalAffectedMonthlyFee)),1),At])]),_:1})]),_:1}),x((h(),U(L,{data:r.contractAlerts,stripe:"",border:"","element-loading-text":"加载提醒数据中...","max-height":"400"},{default:s(()=>[t(i,{prop:"line_name",label:"线路名称","min-width":"120"}),t(i,{prop:"provider",label:"运营商",width:"100"}),t(i,{prop:"contract_number",label:"合同编号","min-width":"120"}),t(i,{prop:"contract_end_date",label:"合同结束日期",width:"120"}),t(i,{prop:"days_to_expiry",label:"剩余天数",width:"100",align:"center"},{default:s(a=>[t(F,{type:a.row.days_to_expiry<0?"danger":"warning",size:"small"},{default:s(()=>[_(o(a.row.days_to_expiry<0?"已过期":a.row.days_to_expiry+"天"),1)]),_:2},1032,["type"])]),_:1}),t(i,{prop:"monthly_fee",label:"月费用",width:"100",align:"right"},{default:s(a=>[_(" ¥"+o(n.formatCurrency(a.row.monthly_fee)),1)]),_:1}),t(i,{prop:"alert_type",label:"状态",width:"100",align:"center"},{default:s(a=>[t(F,{type:a.row.alert_type==="已过期"?"danger":"warning",size:"small"},{default:s(()=>[_(o(a.row.alert_type),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])),[[z,r.alertsLoading]])]),_:1}),r.filterForm.groupBy!=="provider"&&r.providerStats.length>0?(h(),U(v,{key:0,class:"provider-stats-card"},{header:s(()=>[Yt]),default:s(()=>[t(L,{data:r.providerStats,stripe:"",border:""},{default:s(()=>[t(i,{prop:"provider",label:"运营商",width:"120"}),t(i,{prop:"count",label:"线路数量",width:"100",align:"center"}),t(i,{prop:"monthly_fee",label:"月费用",width:"120",align:"right"},{default:s(a=>[_(" ¥"+o(n.formatCurrency(a.row.monthly_fee)),1)]),_:1}),t(i,{label:"费用占比",width:"100",align:"center"},{default:s(a=>[_(o(n.getProviderPercentage(a.row.monthly_fee))+"% ",1)]),_:1})]),_:1},8,["data"])]),_:1})):I("",!0),t(v,{class:"usage-stats-card"},{header:s(()=>[e("div",Et,[Mt,t(p,{size:"small",onClick:n.getLineUsageStatistics},{default:s(()=>[t(y,null,{default:s(()=>[t(k)]),_:1}),_(" 刷新数据 ")]),_:1},8,["onClick"])])]),default:s(()=>[t(S,{gutter:20,class:"usage-overview"},{default:s(()=>[t(u,{span:6},{default:s(()=>{var a;return[e("div",Nt,[e("div",qt,o(((a=r.lineUsageStats.totalUsage)==null?void 0:a.total_lines)||0),1),Gt])]}),_:1}),t(u,{span:6},{default:s(()=>{var a;return[e("div",Ot,[e("div",Wt,o(((a=r.lineUsageStats.totalUsage)==null?void 0:a.used_lines)||0),1),jt])]}),_:1}),t(u,{span:6},{default:s(()=>{var a;return[e("div",Ht,[e("div",Jt,o(((a=r.lineUsageStats.totalUsage)==null?void 0:a.unused_lines)||0),1),Kt])]}),_:1}),t(u,{span:6},{default:s(()=>{var a;return[e("div",Qt,[e("div",Xt,o(((a=r.lineUsageStats.totalUsage)==null?void 0:a.overall_usage_rate)||0)+"%",1),Zt])]}),_:1})]),_:1}),x((h(),U(L,{data:r.lineUsageStats.detailedUsage,stripe:"",border:""},{default:s(()=>[t(i,{prop:"provider",label:"运营商",width:"120"}),t(i,{prop:"line_type",label:"线路类型",width:"120"}),t(i,{prop:"datacenter",label:"机房",width:"120"}),t(i,{prop:"total_lines",label:"线路总数",width:"100",align:"center"}),t(i,{prop:"used_lines",label:"已使用",width:"100",align:"center"}),t(i,{prop:"unused_lines",label:"未使用",width:"100",align:"center"}),t(i,{prop:"usage_rate",label:"使用率",width:"100",align:"center"},{default:s(a=>[t(F,{type:n.getUsageRateType(a.row.usage_rate)},{default:s(()=>[_(o(a.row.usage_rate)+"% ",1)]),_:2},1032,["type"])]),_:1}),t(i,{prop:"total_mappings",label:"映射数量",width:"100",align:"center"}),t(i,{prop:"used_ip_count",label:"使用IP数",width:"100",align:"center"})]),_:1},8,["data"])),[[z,r.usageLoading]])]),_:1}),t(v,{class:"ip-utilization-card"},{header:s(()=>[e("div",$t,[te,t(p,{size:"small",onClick:n.getIpResourceUtilization},{default:s(()=>[t(y,null,{default:s(()=>[t(k)]),_:1}),_(" 刷新数据 ")]),_:1},8,["onClick"])])]),default:s(()=>[t(S,{gutter:20,class:"ip-overview"},{default:s(()=>[t(u,{span:6},{default:s(()=>{var a;return[e("div",ee,[e("div",ae,o(((a=r.ipUtilizationStats.totalStats)==null?void 0:a.total_mappings)||0),1),se])]}),_:1}),t(u,{span:6},{default:s(()=>{var a;return[e("div",le,[e("div",re,o(((a=r.ipUtilizationStats.totalStats)==null?void 0:a.total_unique_ips)||0),1),oe])]}),_:1}),t(u,{span:6},{default:s(()=>{var a;return[e("div",ne,[e("div",ie,o(((a=r.ipUtilizationStats.totalStats)==null?void 0:a.total_tcp)||0),1),de])]}),_:1}),t(u,{span:6},{default:s(()=>{var a;return[e("div",ce,[e("div",_e,o(((a=r.ipUtilizationStats.totalStats)==null?void 0:a.total_udp)||0),1),ue])]}),_:1})]),_:1}),e("div",he,[ge,x((h(),U(L,{data:r.ipUtilizationStats.datacenterDistribution,stripe:"",border:""},{default:s(()=>[t(i,{prop:"datacenter",label:"机房",width:"120"}),t(i,{prop:"total_mappings",label:"映射总数",width:"100",align:"center"}),t(i,{prop:"unique_ips",label:"唯一IP数",width:"100",align:"center"}),t(i,{prop:"lines_used",label:"使用线路数",width:"100",align:"center"}),t(i,{prop:"tcp_count",label:"TCP数量",width:"100",align:"center"}),t(i,{prop:"udp_count",label:"UDP数量",width:"100",align:"center"}),t(i,{label:"协议分布",width:"150",align:"center"},{default:s(a=>[e("div",fe,[t(F,{size:"small",type:"success"},{default:s(()=>[_("TCP: "+o(a.row.tcp_count),1)]),_:2},1024),t(F,{size:"small",type:"warning",style:{"margin-left":"5px"}},{default:s(()=>[_("UDP: "+o(a.row.udp_count),1)]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[z,r.ipUtilizationLoading]])])]),_:1}),t(v,{class:"cost-trend-card"},{header:s(()=>[e("div",me,[pe,e("div",ve,[t(T,{modelValue:r.trendForm.groupBy,"onUpdate:modelValue":d[3]||(d[3]=a=>r.trendForm.groupBy=a),size:"small",onChange:n.getCostTrendStatistics},{default:s(()=>[t(D,{label:"按月",value:"month"}),t(D,{label:"按季度",value:"quarter"}),t(D,{label:"按年",value:"year"})]),_:1},8,["modelValue","onChange"]),t(p,{size:"small",onClick:n.getCostTrendStatistics,style:{"margin-left":"10px"}},{default:s(()=>[t(y,null,{default:s(()=>[t(k)]),_:1}),_(" 刷新 ")]),_:1},8,["onClick"])])])]),default:s(()=>[x((h(),b("div",be,[e("div",ye,[we,e("div",Ce,[(h(!0),b(R,null,A(r.costTrendStats.trendData,(a,w)=>(h(),b("div",{key:w,class:"trend-bar-item"},[e("div",Se,[e("span",De,o(a.period),1),e("span",Fe,"¥"+o(n.formatCurrency(a.total_monthly_fee)),1),e("span",Ue,o(a.line_count)+"条线路",1)]),e("div",xe,[e("div",{class:"trend-bar-fill",style:Y({width:n.getTrendPercentage(a.total_monthly_fee)+"%",backgroundColor:n.getTrendBarColor(w)})},null,4)]),e("div",ke,o(n.getTrendPercentage(a.total_monthly_fee))+"%",1)]))),128))])]),r.costTrendStats.cumulativeData.length>0?(h(),b("div",Be,[Pe,e("div",Le,[(h(!0),b(R,null,A(r.costTrendStats.cumulativeData,(a,w)=>(h(),b("div",{key:w,class:"cumulative-bar-item"},[e("div",ze,[e("span",Te,o(a.period),1),e("span",Ve,"¥"+o(n.formatCurrency(a.cumulative_monthly_fee)),1),e("span",Ie,"累计"+o(a.cumulative_line_count)+"条",1)]),e("div",Re,[e("div",{class:"cumulative-bar-fill",style:Y({width:n.getCumulativePercentage(a.cumulative_monthly_fee)+"%",backgroundColor:"#67C23A"})},null,4)])]))),128))])])):I("",!0)])),[[z,r.trendLoading]])]),_:1}),t(v,{class:"datacenter-distribution-card"},{header:s(()=>[e("div",Ae,[Ye,t(p,{size:"small",onClick:n.getDatacenterResourceDistribution},{default:s(()=>[t(y,null,{default:s(()=>[t(k)]),_:1}),_(" 刷新数据 ")]),_:1},8,["onClick"])])]),default:s(()=>[t(S,{gutter:20,class:"datacenter-overview"},{default:s(()=>[t(u,{span:6},{default:s(()=>{var a;return[e("div",Ee,[e("div",Me,o(((a=r.datacenterStats.totalStats)==null?void 0:a.total_datacenters)||0),1),Ne])]}),_:1}),t(u,{span:6},{default:s(()=>{var a;return[e("div",qe,[e("div",Ge,o(((a=r.datacenterStats.totalStats)==null?void 0:a.total_lines)||0),1),Oe])]}),_:1}),t(u,{span:6},{default:s(()=>{var a;return[e("div",We,[e("div",je,"¥"+o(n.formatCurrency((a=r.datacenterStats.totalStats)==null?void 0:a.total_monthly_fee)),1),He])]}),_:1}),t(u,{span:6},{default:s(()=>{var a;return[e("div",Je,[e("div",Ke,"¥"+o(n.formatCurrency((a=r.datacenterStats.totalStats)==null?void 0:a.avg_monthly_fee)),1),Qe])]}),_:1})]),_:1}),x((h(),U(L,{data:r.datacenterStats.distribution,stripe:"",border:""},{default:s(()=>[t(i,{prop:"datacenter",label:"机房",width:"120"}),t(i,{prop:"total_lines",label:"线路数",width:"80",align:"center"}),t(i,{prop:"provider_count",label:"运营商数",width:"80",align:"center"}),t(i,{prop:"line_type_count",label:"线路类型数",width:"100",align:"center"}),t(i,{prop:"total_monthly_fee",label:"月费用",width:"120",align:"right"},{default:s(a=>[_(" ¥"+o(n.formatCurrency(a.row.total_monthly_fee)),1)]),_:1}),t(i,{prop:"total_mappings",label:"IP映射数",width:"100",align:"center"}),t(i,{prop:"unique_ips",label:"唯一IP数",width:"100",align:"center"}),t(i,{label:"合同状态",width:"120",align:"center"},{default:s(a=>[e("div",Xe,[a.row.expired_contracts>0?(h(),U(F,{key:0,type:"danger",size:"small"},{default:s(()=>[_(" 过期: "+o(a.row.expired_contracts),1)]),_:2},1024)):I("",!0),a.row.expiring_contracts>0?(h(),U(F,{key:1,type:"warning",size:"small",style:{"margin-left":"5px"}},{default:s(()=>[_(" 即将过期: "+o(a.row.expiring_contracts),1)]),_:2},1024)):I("",!0)])]),_:1})]),_:1},8,["data"])),[[z,r.datacenterLoading]]),e("div",Ze,[$e,e("div",ta,[(h(!0),b(R,null,A(r.datacenterStats.distribution,(a,w)=>(h(),b("div",{key:w,class:"datacenter-chart-bar-item"},[e("div",ea,[e("span",aa,o(a.datacenter),1),e("span",sa,"¥"+o(n.formatCurrency(a.total_monthly_fee)),1),e("span",la,o(a.total_lines)+"条线路",1)]),e("div",ra,[e("div",{class:"datacenter-bar-fill",style:Y({width:n.getDatacenterPercentage(a.total_monthly_fee)+"%",backgroundColor:n.getDatacenterBarColor(w)})},null,4)]),e("div",oa,o(n.getDatacenterPercentage(a.total_monthly_fee))+"%",1)]))),128))])])]),_:1})])}const da=H(et,[["render",na],["__scopeId","data-v-860fb192"]]);export{da as default};
