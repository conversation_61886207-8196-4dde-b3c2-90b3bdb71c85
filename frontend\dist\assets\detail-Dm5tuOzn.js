import{_ as we,al as ye,s as be,v as De,am as xe,W as Ce,A as de,an as ke,a6 as Ue,Z as Ve,ah as re,O as ce,c as _,a as l,w as o,f as a,C as Fe,x as b,h as g,t as U,D as Se,m as S,b as p,F as L,l as O,r as D,S as ae,R as me,o as Te,X as qe,p as Ie,q as Ne,E as u,Y as ze,Q as ne,e as m}from"./index-MGgq8mV5.js";import{s as z}from"./request-DGO27LS-.js";import{d as ue}from"./downloadUtils-__kqhe_R.js";const Pe={name:"EventManagementDetail",components:{Delete:ce,Document:re,InfoFilled:Ve,User:Ue,UploadFilled:ke,Download:de,Edit:Ce,CircleCheck:xe,Search:De,Setting:be,FolderOpened:ye},setup(){const x=ze(),c=qe(),Q=D(null),e=ae(()=>c.params.id==="new"?"new":c.query.mode==="view"?"view":"edit"),r=me({id:null,event_id:"",title:"",priority:"U00002",system:"",reporter:"",assignee:[],report_time:"",description:"",process:"",solution:"",improvement_plan:"",consequence_cause_analysis:""}),se={title:[{required:!0,message:"请输入事件标题",trigger:"blur"}],priority:[{required:!0,message:"请选择事件级别",trigger:"change"}],system:[{required:!0,message:"请选择影响系统",trigger:"change"}],reporter:[{required:!0,message:"请选择报告人",trigger:"change"}],assignee:[{required:!0,message:"请选择处理人",trigger:"change"}],report_time:[{required:!0,message:"请选择报告时间",trigger:"change"}],description:[{required:!0,message:"请输入事件描述",trigger:"blur"}]},h=D([]),E=D([]),v=D([]),C=D(!1),y=D(!1),T=D(!1),q=D(null),k=D(!1),V=D([]),F=D([]),X=ae(()=>{const t=localStorage.getItem("role_code");return localStorage.getItem("loginUsername")==="admin"||t&&t.includes("D")}),P=ae(()=>localStorage.getItem("loginUsername")==="admin"),Z=async()=>{try{const t=await z({url:"/api/get_cmdb_data_dictionary",method:"post",data:{dict_type:"U"}});t.code===0&&(h.value=t.msg.filter(i=>i&&i.dict_code!==null&&i.dict_name!==null))}catch{}},M=async()=>{try{const t=await z({url:"/api/get_system_list",method:"post"});t.code===0&&(E.value=t.msg.filter(i=>i&&i.system_abbreviation!==null&&i.system_abbreviation!==void 0))}catch{}},G=async()=>{try{const t=await z({url:"/api/get_user_list",method:"post"});t.code===0&&(v.value=t.msg.filter(i=>i&&i.username!==null&&i.username!==void 0&&i.real_name!==null&&i.real_name!==void 0))}catch{}},J=t=>{if(!t)return"未知用户";const i=v.value.find(s=>s.username===t);return i?i.real_name:t},I=t=>{if(!t)return null;try{if(typeof t=="string"&&t.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)){const[i,s]=t.split(" "),[d,f,R]=i.split("-").map(Number),[B,H,N]=s.split(":").map(Number);return new Date(d,f-1,R,B,H,N)}return new Date(t)}catch{return null}},K=t=>{if(!t)return"";if(!(t instanceof Date))return t;const i=t.getFullYear(),s=String(t.getMonth()+1).padStart(2,"0"),d=String(t.getDate()).padStart(2,"0"),f=String(t.getHours()).padStart(2,"0"),R=String(t.getMinutes()).padStart(2,"0"),B=String(t.getSeconds()).padStart(2,"0");return`${i}-${s}-${d} ${f}:${R}:${B}`},$=async t=>{C.value=!0;try{const i=await z({url:"/api/get_ops_event_management_detail",method:"post",data:{id:t}});if(i.code===0){const s=i.msg;if(Object.keys(r).forEach(d=>{s[d]!==void 0&&(d==="assignee"?s[d]&&typeof s[d]=="string"?r[d]=s[d].includes(",")?s[d].split(",").map(f=>f.trim()).filter(f=>f):[s[d]]:Array.isArray(s[d])?r[d]=s[d]:r[d]=s[d]?[s[d]]:[]:d==="report_time"?r[d]=I(s[d]):r[d]=s[d])}),s.supplementary_material){const d=Y(s.supplementary_material);let f=d||"分析报告.pdf";s.event_id&&d&&!d.startsWith(s.event_id)&&(f=`${s.event_id}_${d}`),F.value=[{id:s.id,name:f,originalName:d,path:s.supplementary_material,eventNumber:s.event_id}]}else F.value=[]}else u.error(i.msg||"获取事件详情失败"),x.push("/ops_event_management")}catch{u.error("获取事件详情失败"),x.push("/ops_event_management")}finally{C.value=!1}},ee=()=>{Q.value.validate(async t=>{if(t){y.value=!0;try{const i=e.value==="new"?"/api/add_ops_event_management":"/api/update_ops_event_management",s={...r};s.username=localStorage.getItem("loginUsername")||"admin",s.report_time&&(s.report_time=K(s.report_time)),console.log("提交数据:",s);const d=await z({url:i,method:"post",data:s});d.code===0?(u.success(e.value==="new"?"事件创建成功":"事件更新成功"),x.push("/ops_event_management")):u.error(d.msg||`${e.value==="new"?"创建":"更新"}失败`)}catch(i){console.error("提交失败:",i),u.error(`${e.value==="new"?"创建":"更新"}失败`)}finally{y.value=!1}}})},te=()=>{ne.confirm(`确定要删除事件 "${r.event_id}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then(async()=>{T.value=!0;try{const t=await z({url:"/api/del_ops_event_management",method:"post",data:{id:r.id,username:localStorage.getItem("loginUsername")||"admin"}});t.code===0?(u.success("删除成功"),x.push("/ops_event_management")):u.error(`删除失败: ${t.msg}`)}catch(t){console.error("删除事件失败:",t),u.error("删除事件失败")}finally{T.value=!1}}).catch(()=>{console.log("用户取消删除操作")})},W=()=>{x.push("/ops_event_management")},A=me({description:{title:"事件简要经过记录格式",content:`事件简要经过：
• 15时28分，态势感知平台发出告警:SIP风险事件，官网服务器************** 遭到病毒攻击，官网系统管理员立即联系珮金技术进行杀毒处理，网络安全管理员将疑似攻击IP加入黑名单，随后告警解除。
• 16时58分，态势感知平台再次发出告警:SIP风险事件，珮金中继服务器************* 遭到病毒攻击，网络安全管理员将疑似攻击IP加入黑名单，告警解除， 随后联系eset防病毒厂家协助安装杀毒软件。`},process:{title:"事件影响范围、影响程度、影响人数、直接资金损失情况记录格式",content:`事件影响：
• 长江期货APP上海联通（珮金）站点关闭，暂时对客户无影响`},consequence:{title:"事件导致的后果、发生原因和事件性质判断记录格式",content:`事件性质判断：
• 此次事件是互联网上的病毒攻击。`},solution:{title:"已采取的措施及效果记录格式",content:`已采取的措施及效果：
官网：
• 已完成************** 杀毒  ；
• 官网系统管理员确定官网只需要访问研报的域名，其他地址可以禁用，网络安全管理员已将其余互联网权限关闭；
• 在************** 服务器上删除DNS配置。
珮金：
• 系统管理员将受攻击的珮金中继*************站点暂时关机，以防病毒扩散，已通知客户服务中心，珮金一个站点例行维护；
• 将受影响网段另3台服务器*************、*************、*************也安装eset杀毒软件。`},improvement:{title:"总结及知识库记录格式",content:`经验教训：
• 最小化开放互联网策略：不管是互联网映射还是服务器上网,都要明确IP与端口,进行安全扫描并走完审批流程后再开放。对于已经开放的但限制限少的上网策略,请对应系统运维同事确认上网的具体需求后重新填写服务器上网需求表并走审批流程,然后再调整上网策略。以后再申请互联网权限均以最小化权限开放。 
• 加强安全扫描：深信服工程师表示目前的安全扫描偏系统漏洞扫描,对WEB漏扫描能力不强。已要求深信服在以后扫描时都加上专门的WEB漏洞扫描。
• 所有互联网linux服务部署杀毒软件：目前只有WINDOWS服务器安装了杀毒软件,后续将在所有开放互联网服务或上网的linux服务器上安装杀毒软件。但由于linux许可证不足,需向供应商加购linux许可。

知识库内容：
• `}}),le=t=>{console.log("fillTemplateContent called with type:",t),console.log("Current formData:",r);const s={description:"description",process:"process",solution:"solution",improvement:"improvement_plan",consequence:"consequence_cause_analysis"}[t],d=A[t];console.log("Field mapping:",{type:t,field:s,hasTemplate:!!d}),s&&d?(console.log("Current field value:",r[s]),console.log("Template content:",d.content),r[s]&&r[s].trim()?ne.confirm(`${d.title.replace("记录格式","")}字段已有内容，是否覆盖？`,"确认操作",{confirmButtonText:"覆盖",cancelButtonText:"取消",type:"warning"}).then(()=>{r[s]=d.content,console.log("Template filled successfully, new value:",r[s]),u.success("模板内容已填充")}).catch(()=>{console.log("User cancelled template fill")}):(r[s]=d.content,console.log("Template filled successfully, new value:",r[s]),u.success("模板内容已填充"))):(console.error("Invalid field or template:",{field:s,hasTemplate:!!d}),u.error("模板填充失败：无效的字段或模板"))},Y=t=>{if(!t)return"";const i=t.split("/");return i[i.length-1]||t},oe=t=>{if(console.log("文件改变:",t),e.value==="new"||!r.id||!r.event_id||r.event_id==="系统自动生成")return u.warning("请先保存事件记录，生成事件编号后再选择分析报告文件"),!1;V.value=[t]},n=t=>{console.log("移除文件:",t),V.value=V.value.filter(i=>i.uid!==t.uid)},j=t=>/\.(doc|docx|pdf|xls|xlsx|ppt|pptx|txt)$/i.test(t.name)?t.size/1024/1024<10?!0:(u.error("文件大小不能超过 10MB!"),!1):(u.error("只能上传 doc/docx/pdf/xls/xlsx/ppt/pptx/txt 格式的文件!"),!1),_e=async()=>{var t,i,s,d;if(V.value.length===0){u.warning("请先选择文件");return}if(e.value==="new"||!r.id||!r.event_id||r.event_id==="系统自动生成"){u.warning("请先保存事件记录，生成事件编号后再上传分析报告");return}k.value=!0;try{const f=localStorage.getItem("loginUsername")||"admin",R=new URLSearchParams({event_id:r.id,username:f}).toString(),B=new FormData;B.append("file",V.value[0].raw);const H=await fetch(`/api/upload_event_attachment?${R}`,{method:"POST",body:B}),N=await H.json();if(H.ok&&N.code===0){u.success("文件上传成功");const ge=((t=N.data)==null?void 0:t.filename)||`${r.event_id||((i=N.data)==null?void 0:i.eventNumber)}_分析报告`;F.value.push({name:ge,id:r.id,path:(s=N.data)==null?void 0:s.path,eventNumber:r.event_id||((d=N.data)==null?void 0:d.eventNumber)}),ie()}else u.error(N.msg||"文件上传失败")}catch(f){console.error("文件上传失败:",f),u.error("文件上传失败")}finally{k.value=!1}},ie=()=>{V.value=[],q.value&&q.value.clearFiles()},pe=async t=>{try{await ne.confirm(`确定要删除文件 "${t.name}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const i=await z({url:"/api/delete_event_attachment",method:"post",data:{id:t.id,filePath:t.path,username:localStorage.getItem("loginUsername")||"admin"}});i.code===0?(u.success("文件删除成功"),F.value=F.value.filter(s=>s.id!==t.id)):u.error(i.msg||"文件删除失败")}catch(i){i==="cancel"?console.log("用户取消删除操作"):(console.error("文件删除失败:",i),u.error("文件删除失败"))}},fe=async t=>{try{if(!t.path){u.error("文件路径不存在");return}const i=await z({url:"/api/download_ops_event_file",method:"post",data:{event_id:r.id,file_type:"supplementary_material"},responseType:"blob"}),s=new Blob([i]),d=window.URL.createObjectURL(s),f=document.createElement("a");f.href=d;const R=t.originalName||t.name||"分析报告";f.download=R,document.body.appendChild(f),f.click(),document.body.removeChild(f),window.URL.revokeObjectURL(d),u.success("文件下载成功")}catch(i){console.error("文件下载失败:",i),u.error("文件下载失败")}},ve=async()=>{const t="分析报告-模板.docx",i=`/api/download_ops_event_template/${encodeURIComponent(t)}`;await ue(i,t,{},()=>u.success("事件分析报告模板下载成功"),s=>{console.error("下载事件分析报告模板失败:",s),u.error("下载事件分析报告模板失败")})},he=async()=>{if(e.value==="new"||!r.id){u.warning("请先保存事件后再导出");return}u.info("正在生成Word文档，请稍候...");const t=`/api/export_ops_event_word/${r.id}`,i=r.title?r.title.replace(/[\\/:*?"<>|]/g,"_"):"",s=`${r.event_id||"unknown"}_${i}.docx`;await ue(t,s,{},()=>u.success("事件记录导出成功"),d=>{console.error("导出事件记录失败:",d),u.error("导出事件记录失败")})};return Te(async()=>{if(await Promise.all([Z(),M(),G()]),e.value==="new")r.reporter=localStorage.getItem("loginUsername")||"admin",r.report_time=new Date,r.event_id="系统自动生成";else{const t=c.params.id;t&&t!=="new"&&await $(t)}}),{mode:e,formRef:Q,formData:r,formRules:se,priorityOptions:h,systemOptions:E,userOptions:v,formLoading:C,submitLoading:y,deleteLoading:T,hasDeletePermission:X,isAdminUser:P,eventTemplates:A,uploadRef:q,uploadLoading:k,pendingFiles:V,uploadedFiles:F,handleFileChange:oe,handleFileRemove:n,beforeUpload:j,submitUpload:_e,clearPendingFiles:ie,removeUploadedFile:pe,downloadUploadedFile:fe,getFileNameFromPath:Y,handleSubmit:ee,handleDelete:te,handleCancel:W,getUserRealName:J,fillTemplateContent:le,downloadAnalysisTemplate:ve,exportToWord:he,Document:re,Download:de,Delete:ce}}},w=x=>(Ie("data-v-d8bba13b"),x=x(),Ne(),x),Re={class:"app-container"},Le={class:"card-header"},Oe={key:0},Be={key:1},Me={key:2},Ee={class:"header-buttons"},We={class:"main-content"},Ae={class:"left-panel"},Ye={class:"event-id-section"},je={class:"event-id-text"},He={key:0,class:"auto-generate-text"},Qe={key:1},Xe={class:"form-section"},Ze={class:"section-title"},Ge=w(()=>a("span",null,"基本信息",-1)),Je={key:0,class:"tag-display"},Ke={class:"form-section"},$e={class:"section-title"},et=w(()=>a("span",null,"详细描述",-1)),tt={key:0,class:"auto-fill-button-container"},lt={key:0,class:"auto-fill-button-container"},ot={key:0,class:"auto-fill-button-container"},at={key:0,class:"auto-fill-button-container"},nt={key:0,class:"auto-fill-button-container"},st={key:0,class:"delete-section"},it={class:"delete-button-container"},dt={class:"right-panel"},rt={class:"template-separator"},ct=w(()=>a("span",{style:{"margin-left":"8px"}},"事件记录模板样例",-1)),mt={class:"template-content"},ut={class:"template-section"},_t={class:"template-card-header"},pt=w(()=>a("span",null,"事件简要经过模板",-1)),ft={class:"template-example"},vt={class:"template-section"},ht={class:"template-card-header"},gt=w(()=>a("span",null,"事件影响范围、程度、人数、资金损失情况模板",-1)),wt={class:"template-example"},yt={class:"template-section"},bt={class:"template-card-header"},Dt=w(()=>a("span",null,"事件后果、原因和性质判断模板",-1)),xt={class:"template-example"},Ct={class:"template-section"},kt={class:"template-card-header"},Ut=w(()=>a("span",null,"已采取的措施及效果模板",-1)),Vt={class:"template-example"},Ft={class:"template-section"},St={class:"template-card-header"},Tt=w(()=>a("span",null,"总结及知识库模板",-1)),qt={class:"template-example"},It={class:"bottom-attachment-section"},Nt=w(()=>a("span",{style:{"margin-left":"8px"}},"事件分析报告管理",-1)),zt={class:"attachment-upload-panel"},Pt={class:"panel-title"},Rt=w(()=>a("span",null,"事件分析报告附件上传",-1)),Lt={key:0,class:"upload-disabled-tip"},Ot={key:1,class:"upload-area"},Bt=w(()=>a("div",{class:"el-upload__text"},[g(" 将分析报告文件拖到此处，或"),a("em",null,"点击上传")],-1)),Mt=w(()=>a("div",{class:"el-upload__tip"}," 支持 doc/docx/pdf/xls/xlsx/ppt/pptx/txt 格式，文件大小不超过10MB ",-1)),Et={key:0,class:"uploaded-files"},Wt=w(()=>a("div",{class:"file-list-header"},"已上传的文件：",-1)),At={class:"file-name"},Yt={class:"file-actions"},jt={key:1,class:"upload-actions"},Ht={key:2,class:"uploaded-files"},Qt=w(()=>a("div",{class:"file-list-header"},"已上传的文件：",-1)),Xt={class:"file-name"},Zt={class:"file-actions"},Gt={class:"template-download-panel"},Jt={class:"panel-title"},Kt=w(()=>a("span",null,"下载模板",-1)),$t={class:"download-content"},el=w(()=>a("div",{class:"download-description"},[a("p",null,"下载事件分析报告模板，用于编写标准化的事件分析报告。")],-1)),tl={class:"download-buttons"};function ll(x,c,Q,e,r,se){const h=p("el-button"),E=p("InfoFilled"),v=p("el-icon"),C=p("el-input"),y=p("el-form-item"),T=p("el-option"),q=p("el-select"),k=p("el-col"),V=p("el-date-picker"),F=p("el-row"),X=p("el-tag"),P=p("Document"),Z=p("el-form"),M=p("el-divider"),G=p("Delete"),J=p("Edit"),I=p("el-card"),K=p("CircleCheck"),$=p("Search"),ee=p("Setting"),te=p("FolderOpened"),W=p("UploadFilled"),A=p("el-alert"),le=p("el-upload"),Y=p("Download"),oe=Se("loading");return m(),_("div",Re,[l(I,{class:"box-card"},{header:o(()=>[a("div",Le,[e.mode==="new"?(m(),_("span",Oe,"新增事件")):e.mode==="edit"?(m(),_("span",Be,"编辑事件")):(m(),_("span",Me,"事件详情")),a("div",Ee,[l(h,{type:"primary",onClick:e.handleCancel},{default:o(()=>[g("返回列表")]),_:1},8,["onClick"]),e.mode!=="view"?(m(),S(h,{key:0,type:"success",onClick:e.handleSubmit,loading:e.submitLoading},{default:o(()=>[g(U(e.mode==="new"?"创建":"保存"),1)]),_:1},8,["onClick","loading"])):b("",!0),e.mode!=="new"&&e.formData.id?(m(),S(h,{key:1,type:"warning",onClick:e.exportToWord,icon:e.Document},{default:o(()=>[g(" 导出事件记录 ")]),_:1},8,["onClick","icon"])):b("",!0)])])]),default:o(()=>[a("div",We,[a("div",Ae,[a("div",Ye,[a("span",je,[g(" 事件编号： "),e.mode==="new"?(m(),_("span",He,"系统自动生成")):(m(),_("span",Qe,U(e.formData.event_id||"系统自动生成"),1))])]),Fe((m(),S(Z,{ref:"formRef",model:e.formData,rules:e.formRules,"label-width":"140px",class:"event-form"},{default:o(()=>[a("div",Xe,[a("div",Ze,[l(v,null,{default:o(()=>[l(E)]),_:1}),Ge]),l(y,{label:"事件标题",prop:"title",class:"required-field"},{default:o(()=>[l(C,{modelValue:e.formData.title,"onUpdate:modelValue":c[0]||(c[0]=n=>e.formData.title=n),disabled:e.mode==="view",placeholder:"请输入事件标题",size:"default"},null,8,["modelValue","disabled"])]),_:1}),l(F,{gutter:16},{default:o(()=>[l(k,{span:12},{default:o(()=>[l(y,{label:"影响系统",prop:"system",class:"required-field"},{default:o(()=>[l(q,{modelValue:e.formData.system,"onUpdate:modelValue":c[1]||(c[1]=n=>e.formData.system=n),disabled:e.mode==="view",placeholder:"请选择影响系统",style:{width:"100%"},filterable:"",clearable:"",size:"default"},{default:o(()=>[(m(!0),_(L,null,O(e.systemOptions,n=>(m(),S(T,{key:n.system_abbreviation,label:n.system_abbreviation,value:n.system_abbreviation},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(k,{span:12},{default:o(()=>[l(y,{label:"事件级别",prop:"priority",class:"required-field"},{default:o(()=>[l(q,{modelValue:e.formData.priority,"onUpdate:modelValue":c[2]||(c[2]=n=>e.formData.priority=n),disabled:e.mode==="view",placeholder:"请选择事件级别",style:{width:"100%"},filterable:"",clearable:"",size:"default"},{default:o(()=>[(m(!0),_(L,null,O(e.priorityOptions,n=>(m(),S(T,{key:n.dict_code,label:n.dict_name,value:n.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(k,{span:12},{default:o(()=>[l(y,{label:"首次发生时间",prop:"report_time",class:"required-field"},{default:o(()=>[l(V,{modelValue:e.formData.report_time,"onUpdate:modelValue":c[3]||(c[3]=n=>e.formData.report_time=n),disabled:e.mode==="view",type:"datetime",placeholder:"选择报告时间",style:{width:"100%"},format:"YYYY-MM-DD HH:mm:ss",size:"default"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),l(k,{span:12},{default:o(()=>[l(y,{label:"值班经理",prop:"reporter",class:"required-field"},{default:o(()=>[l(q,{modelValue:e.formData.reporter,"onUpdate:modelValue":c[4]||(c[4]=n=>e.formData.reporter=n),disabled:e.mode==="view",placeholder:"请选择值班经理",style:{width:"100%"},filterable:"",clearable:"",size:"default"},{default:o(()=>[(m(!0),_(L,null,O(e.userOptions,n=>(m(),S(T,{key:n.username,label:n.real_name,value:n.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(y,{label:"处理人",prop:"assignee",class:"required-field"},{default:o(()=>[l(q,{modelValue:e.formData.assignee,"onUpdate:modelValue":c[5]||(c[5]=n=>e.formData.assignee=n),disabled:e.mode==="view",placeholder:"请选择处理人（可多选）",style:{width:"100%"},filterable:"",clearable:"",multiple:"","collapse-tags":"","collapse-tags-tooltip":"","max-collapse-tags":3,size:"default"},{default:o(()=>[(m(!0),_(L,null,O(e.userOptions,n=>(m(),S(T,{key:n.username,label:n.real_name,value:n.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"]),e.formData.assignee&&e.formData.assignee.length>0?(m(),_("div",Je,[(m(!0),_(L,null,O(e.formData.assignee,n=>(m(),S(X,{key:n,class:"display-tag",type:"primary",effect:"light",size:"small"},{default:o(()=>[g(U(e.getUserRealName(n)),1)]),_:2},1024))),128))])):b("",!0)]),_:1})]),a("div",Ke,[a("div",$e,[l(v,null,{default:o(()=>[l(P)]),_:1}),et]),l(y,{label:"事件简要经过",prop:"description",class:"required-field"},{default:o(()=>[l(C,{modelValue:e.formData.description,"onUpdate:modelValue":c[6]||(c[6]=n=>e.formData.description=n),disabled:e.mode==="view",type:"textarea",rows:8,placeholder:"请详细描述事件的具体情况、影响范围等","show-word-limit":"",maxlength:"2000"},null,8,["modelValue","disabled"]),e.mode!=="view"?(m(),_("div",tt,[l(h,{size:"small",type:"info",plain:"",onClick:c[7]||(c[7]=n=>e.fillTemplateContent("description")),class:"auto-fill-button"},{default:o(()=>[g(" 自动填充 ")]),_:1})])):b("",!0)]),_:1}),l(y,{label:"事件影响范围、影响程度、影响人数、直接资金损失情况"},{default:o(()=>[l(C,{modelValue:e.formData.process,"onUpdate:modelValue":c[8]||(c[8]=n=>e.formData.process=n),disabled:e.mode==="view",type:"textarea",rows:8,placeholder:"请详细描述事件影响范围、影响程度、影响人数、直接资金损失情况","show-word-limit":"",maxlength:"2000"},null,8,["modelValue","disabled"]),e.mode!=="view"?(m(),_("div",lt,[l(h,{size:"small",type:"info",plain:"",onClick:c[9]||(c[9]=n=>e.fillTemplateContent("process")),class:"auto-fill-button"},{default:o(()=>[g(" 自动填充 ")]),_:1})])):b("",!0)]),_:1}),l(y,{label:"事件导致的后果、发生原因和事件性质判断"},{default:o(()=>[l(C,{modelValue:e.formData.consequence_cause_analysis,"onUpdate:modelValue":c[10]||(c[10]=n=>e.formData.consequence_cause_analysis=n),disabled:e.mode==="view",type:"textarea",rows:8,placeholder:"请详细描述事件导致的后果、发生原因和事件性质判断","show-word-limit":"",maxlength:"2000"},null,8,["modelValue","disabled"]),e.mode!=="view"?(m(),_("div",ot,[l(h,{size:"small",type:"info",plain:"",onClick:c[11]||(c[11]=n=>e.fillTemplateContent("consequence")),class:"auto-fill-button"},{default:o(()=>[g(" 自动填充 ")]),_:1})])):b("",!0)]),_:1}),l(y,{label:"已采取的措施及效果"},{default:o(()=>[l(C,{modelValue:e.formData.solution,"onUpdate:modelValue":c[12]||(c[12]=n=>e.formData.solution=n),disabled:e.mode==="view",type:"textarea",rows:8,placeholder:"请详细记录采取的应急措施、处理步骤及实际效果","show-word-limit":"",maxlength:"2000"},null,8,["modelValue","disabled"]),e.mode!=="view"?(m(),_("div",at,[l(h,{size:"small",type:"info",plain:"",onClick:c[13]||(c[13]=n=>e.fillTemplateContent("solution")),class:"auto-fill-button"},{default:o(()=>[g(" 自动填充 ")]),_:1})])):b("",!0)]),_:1}),l(y,{label:"总结及知识库"},{default:o(()=>[l(C,{modelValue:e.formData.improvement_plan,"onUpdate:modelValue":c[14]||(c[14]=n=>e.formData.improvement_plan=n),disabled:e.mode==="view",type:"textarea",rows:8,placeholder:"请总结事件处理经验、教训及形成的知识库内容","show-word-limit":"",maxlength:"2000"},null,8,["modelValue","disabled"]),e.mode!=="view"?(m(),_("div",nt,[l(h,{size:"small",type:"info",plain:"",onClick:c[15]||(c[15]=n=>e.fillTemplateContent("improvement")),class:"auto-fill-button"},{default:o(()=>[g(" 自动填充 ")]),_:1})])):b("",!0)]),_:1})])]),_:1},8,["model","rules"])),[[oe,e.formLoading]]),e.formData.id&&e.hasDeletePermission?(m(),_("div",st,[l(M),a("div",it,[l(h,{type:"danger",loading:e.deleteLoading,onClick:e.handleDelete,class:"delete-button"},{default:o(()=>[l(v,null,{default:o(()=>[l(G)]),_:1}),g(" 删除事件记录 ")]),_:1},8,["loading","onClick"])])])):b("",!0)]),a("div",dt,[a("div",rt,[l(M,{"content-position":"center"},{default:o(()=>[l(v,null,{default:o(()=>[l(P)]),_:1}),ct]),_:1})]),a("div",mt,[a("div",ut,[l(I,{shadow:"hover",class:"template-card"},{header:o(()=>[a("div",_t,[l(v,null,{default:o(()=>[l(J)]),_:1}),pt])]),default:o(()=>[a("div",ft,[a("pre",null,U(e.eventTemplates.description.content),1)])]),_:1})]),a("div",vt,[l(I,{shadow:"hover",class:"template-card"},{header:o(()=>[a("div",ht,[l(v,null,{default:o(()=>[l(K)]),_:1}),gt])]),default:o(()=>[a("div",wt,[a("pre",null,U(e.eventTemplates.process.content),1)])]),_:1})]),a("div",yt,[l(I,{shadow:"hover",class:"template-card"},{header:o(()=>[a("div",bt,[l(v,null,{default:o(()=>[l($)]),_:1}),Dt])]),default:o(()=>[a("div",xt,[a("pre",null,U(e.eventTemplates.consequence.content),1)])]),_:1})]),a("div",Ct,[l(I,{shadow:"hover",class:"template-card"},{header:o(()=>[a("div",kt,[l(v,null,{default:o(()=>[l(ee)]),_:1}),Ut])]),default:o(()=>[a("div",Vt,[a("pre",null,U(e.eventTemplates.solution.content),1)])]),_:1})]),a("div",Ft,[l(I,{shadow:"hover",class:"template-card"},{header:o(()=>[a("div",St,[l(v,null,{default:o(()=>[l(te)]),_:1}),Tt])]),default:o(()=>[a("div",qt,[a("pre",null,U(e.eventTemplates.improvement.content),1)])]),_:1})])])])]),a("div",It,[l(M,{"content-position":"center"},{default:o(()=>[l(v,null,{default:o(()=>[l(P)]),_:1}),Nt]),_:1}),l(F,{gutter:24},{default:o(()=>[l(k,{span:12},{default:o(()=>[a("div",zt,[a("div",Pt,[l(v,null,{default:o(()=>[l(W)]),_:1}),Rt]),e.mode==="new"||!e.formData.id||!e.formData.event_id||e.formData.event_id==="系统自动生成"?(m(),_("div",Lt,[l(A,{title:"请先保存事件记录",description:"需要先保存事件并生成事件编号后，才能上传分析报告文件",type:"info",closable:!1,"show-icon":""})])):e.mode!=="view"?(m(),_("div",Ot,[l(le,{ref:"uploadRef","auto-upload":!1,multiple:!1,"show-file-list":!0,"on-change":e.handleFileChange,"on-remove":e.handleFileRemove,"before-upload":e.beforeUpload,accept:".doc,.docx,.pdf,.xls,.xlsx,.ppt,.pptx,.txt",drag:"",class:"analysis-upload"},{default:o(()=>[l(v,{class:"el-icon--upload"},{default:o(()=>[l(W)]),_:1}),Bt,Mt]),_:1},8,["on-change","on-remove","before-upload"]),e.uploadedFiles.length>0?(m(),_("div",Et,[Wt,(m(!0),_(L,null,O(e.uploadedFiles,n=>(m(),_("div",{key:n.name,class:"file-item"},[l(v,null,{default:o(()=>[l(P)]),_:1}),a("span",At,U(n.name),1),a("div",Yt,[l(h,{size:"small",type:"primary",text:"",onClick:j=>e.downloadUploadedFile(n),icon:e.Download},{default:o(()=>[g(" 下载 ")]),_:2},1032,["onClick","icon"]),e.mode!=="view"?(m(),S(h,{key:0,size:"small",type:"danger",text:"",onClick:j=>e.removeUploadedFile(n),icon:e.Delete},{default:o(()=>[g(" 删除 ")]),_:2},1032,["onClick","icon"])):b("",!0)])]))),128))])):b("",!0),e.pendingFiles.length>0?(m(),_("div",jt,[l(h,{type:"success",size:"small",onClick:e.submitUpload,loading:e.uploadLoading},{default:o(()=>[g(" 确认上传 ")]),_:1},8,["onClick","loading"]),l(h,{size:"small",onClick:e.clearPendingFiles},{default:o(()=>[g(" 清空 ")]),_:1},8,["onClick"])])):b("",!0)])):b("",!0),e.mode==="view"&&e.uploadedFiles.length>0?(m(),_("div",Ht,[Qt,(m(!0),_(L,null,O(e.uploadedFiles,n=>(m(),_("div",{key:n.name,class:"file-item"},[l(v,null,{default:o(()=>[l(P)]),_:1}),a("span",Xt,U(n.name),1),a("div",Zt,[l(h,{size:"small",type:"primary",text:"",onClick:j=>e.downloadUploadedFile(n),icon:e.Download},{default:o(()=>[g(" 下载 ")]),_:2},1032,["onClick","icon"])])]))),128))])):b("",!0)])]),_:1}),l(k,{span:12},{default:o(()=>[a("div",Gt,[a("div",Jt,[l(v,null,{default:o(()=>[l(Y)]),_:1}),Kt]),a("div",$t,[el,a("div",tl,[l(h,{size:"default",type:"primary",onClick:e.downloadAnalysisTemplate,icon:e.Document,class:"download-button"},{default:o(()=>[g(" 下载事件分析报告模板 ")]),_:1},8,["onClick","icon"])])])])]),_:1})]),_:1})])]),_:1})])}const sl=we(Pe,[["render",ll],["__scopeId","data-v-d8bba13b"]]);export{sl as default};
