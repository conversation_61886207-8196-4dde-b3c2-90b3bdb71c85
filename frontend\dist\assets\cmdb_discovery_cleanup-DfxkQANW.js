import{s as v}from"./request-DGO27LS-.js";import{f as G}from"./dateUtils-BmBarIng.js";import{_ as O,L as J,M as K,G as X,N as Y,H as Z,j as $,O as ee,P as te,k as oe,c as w,a as e,m as P,x as V,w as o,b as i,f as a,t as d,h as c,C as k,D as ae,n as ne,F as se,l as le,E as u,Q as ie,p as re,q as ce,e as f}from"./index-MGgq8mV5.js";function de(t){return v({url:"/api/discovery/cleanup/stats",method:"post",data:t})}function ue(t){return v({url:"/api/discovery/cleanup/execute",method:"post",data:t})}function pe(t){return v({url:"/api/discovery/cleanup/config",method:"post",data:t})}function _e(t){return v({url:"/api/discovery/cleanup/config/update",method:"post",data:t})}function he(t){return v({url:"/api/discovery/cleanup/history",method:"post",data:t})}function ge(t){return v({url:"/api/discovery/cleanup/preview",method:"post",data:t})}function fe(t){return v({url:"/api/discovery/cleanup/validate-cron",method:"post",data:t})}function me(){return v({url:"/api/discovery/cleanup/cron-examples",method:"get"})}const ye={name:"DiscoveryCleanup",components:{Warning:oe,Check:te,Delete:ee,RefreshRight:$,QuestionFilled:Z,DataBoard:Y,Refresh:X,View:K,Close:J},data(){return{statsData:{expiredCount:0,activeCount:0,deletedCount:0,totalCount:0},cleanupForm:{expireDays:1},showPreview:!1,previewData:{rows:[],total:0},previewPagination:{currentPage:1,pageSize:10},historyData:{rows:[],total:0},historyPagination:{currentPage:1,pageSize:10},configForm:{cleanup_enabled:!0,cleanup_expire_days:1,cleanup_schedule:"0 2 * * *",cleanup_batch_size:1e3},statsLoading:!1,previewLoading:!1,cleanupLoading:!1,historyLoading:!1,configLoading:!1,cronValidation:{error:null,description:null,nextExecution:null},cronExamplesVisible:!1,cronExamples:[],cronExamplesLoading:!1}},mounted(){this.init()},methods:{async init(){await this.refreshStats(),await this.loadHistory(),await this.loadConfig()},async refreshStats(){this.statsLoading=!0;try{const t=await de({expireDays:this.cleanupForm.expireDays});t.code===0?this.statsData=t.data:u.error(t.msg||"获取统计信息失败")}catch(t){console.error("获取统计信息失败:",t),u.error("获取统计信息失败")}finally{this.statsLoading=!1}},async previewCleanup(){this.previewLoading=!0;try{const t=await ge({expireDays:this.cleanupForm.expireDays,currentPage:this.previewPagination.currentPage,pageSize:this.previewPagination.pageSize});t.code===0?(this.previewData=t.data,this.showPreview=!0):u.error(t.msg||"预览数据失败")}catch(t){console.error("预览数据失败:",t),u.error("预览数据失败")}finally{this.previewLoading=!1}},async executeCleanup(){try{await ie.confirm(`确定要清理 ${this.cleanupForm.expireDays} 天前的发现结果数据吗？此操作不可恢复！`,"确认清理",{confirmButtonText:"确定清理",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}),this.cleanupLoading=!0;const t=await ue({expireDays:this.cleanupForm.expireDays});t.code===0?(u.success(t.msg||"清理完成"),await this.refreshStats(),await this.loadHistory(),this.showPreview=!1):u.error(t.msg||"清理失败")}catch(t){t!=="cancel"&&(console.error("执行清理失败:",t),u.error("执行清理失败"))}finally{this.cleanupLoading=!1}},async loadHistory(){this.historyLoading=!0;try{const t=await he({currentPage:this.historyPagination.currentPage,pageSize:this.historyPagination.pageSize});t.code===0?this.historyData=t.data:u.error(t.msg||"获取历史记录失败")}catch(t){console.error("获取历史记录失败:",t),u.error("获取历史记录失败")}finally{this.historyLoading=!1}},async loadConfig(){this.configLoading=!0;try{const t=await pe({});if(t.code===0){const s=t.data.config;this.configForm={cleanup_enabled:s.cleanup_enabled==="true",cleanup_expire_days:parseInt(s.cleanup_expire_days)||1,cleanup_schedule:s.cleanup_schedule||"0 2 * * *",cleanup_batch_size:parseInt(s.cleanup_batch_size)||1e3}}else u.error(t.msg||"获取配置失败")}catch(t){console.error("获取配置失败:",t),u.error("获取配置失败")}finally{this.configLoading=!1}},async saveConfig(){this.configLoading=!0;try{const t={cleanup_enabled:this.configForm.cleanup_enabled?"true":"false",cleanup_expire_days:this.configForm.cleanup_expire_days.toString(),cleanup_schedule:this.configForm.cleanup_schedule,cleanup_batch_size:this.configForm.cleanup_batch_size.toString()},s=await _e({config:t});s.code===0?u.success("配置保存成功"):u.error(s.msg||"配置保存失败")}catch(t){console.error("保存配置失败:",t),u.error("保存配置失败")}finally{this.configLoading=!1}},handlePreviewSizeChange(t){this.previewPagination.pageSize=t,this.previewCleanup()},handlePreviewCurrentChange(t){this.previewPagination.currentPage=t,this.previewCleanup()},handleHistorySizeChange(t){this.historyPagination.pageSize=t,this.loadHistory()},handleHistoryCurrentChange(t){this.historyPagination.currentPage=t,this.loadHistory()},formatDateTime(t){return G(t)},getDaysAgo(t){const s=new Date,S=new Date(t),F=Math.abs(s-S);return Math.ceil(F/(1e3*60*60*24))},getLogLevelType(t){return{info:"success",warning:"warning",error:"danger"}[t]||"info"},async validateCronExpression(){var t;if(!this.configForm.cleanup_schedule){this.cronValidation={error:null,description:null,nextExecution:null};return}try{const s=await fe({cronExpression:this.configForm.cleanup_schedule});s.code===0?this.cronValidation={error:null,description:s.data.description,nextExecution:s.data.nextExecution}:this.cronValidation={error:s.msg||((t=s.data)==null?void 0:t.error)||"Cron表达式格式错误",description:null,nextExecution:null}}catch(s){console.error("验证Cron表达式失败:",s),this.cronValidation={error:"验证Cron表达式失败",description:null,nextExecution:null}}},async showCronExamples(){this.cronExamplesVisible=!0,this.cronExamplesLoading=!0;try{const t=await me();t.code===0?this.cronExamples=t.data.examples||[]:u.error("获取Cron示例失败")}catch(t){console.error("获取Cron示例失败:",t),u.error("获取Cron示例失败")}finally{this.cronExamplesLoading=!1}},selectCronExample(t){this.configForm.cleanup_schedule=t,this.cronExamplesVisible=!1,this.validateCronExpression()}}},p=t=>(re("data-v-afd2c828"),t=t(),ce(),t),ve={class:"discovery-cleanup"},xe=p(()=>a("div",{class:"page-header"},[a("p",{class:"description",style:{"line-height":"1.5","margin-bottom":"10px",color:"#E6A23C","background-color":"#FDF6EC",padding:"8px 12px","border-radius":"4px","border-left":"4px solid #E6A23C"}}," 管理自动发现结果的数据清理，自动清理超过指定天数的过期数据 ")],-1)),we={class:"stats-content"},Ce={class:"stats-icon expired"},be={class:"stats-info"},De={class:"stats-number"},Pe=p(()=>a("div",{class:"stats-label"},"过期数据",-1)),Ve={class:"stats-content"},ze={class:"stats-icon active"},Ee={class:"stats-info"},ke={class:"stats-number"},Se=p(()=>a("div",{class:"stats-label"},"有效数据",-1)),Fe={class:"stats-content"},Le={class:"stats-icon deleted"},Be={class:"stats-info"},He={class:"stats-number"},Te=p(()=>a("div",{class:"stats-label"},"已清理数据",-1)),Ue={class:"stats-content"},Me={class:"stats-icon total"},Ie={class:"stats-info"},Ne={class:"stats-number"},Re=p(()=>a("div",{class:"stats-label"},"总数据量",-1)),Ae={class:"card-header"},Qe=p(()=>a("span",null,"清理操作",-1)),je=p(()=>a("span",{class:"form-help"},"天前的数据将被清理",-1)),We={class:"card-header"},qe={class:"pagination-container"},Ge={class:"card-header"},Oe=p(()=>a("span",null,"清理历史",-1)),Je={class:"pagination-container"},Ke={class:"card-header"},Xe=p(()=>a("span",null,"清理配置",-1)),Ye={key:0,class:"form-help"},Ze={key:0,class:"next-execution"},$e={key:1,class:"form-error"},et=p(()=>a("p",null,[a("strong",null,"格式："),c("分钟 小时 日 月 星期")],-1)),tt=p(()=>a("p",null,[a("strong",null,"字段范围：")],-1)),ot=p(()=>a("ul",{style:{margin:"5px 0","padding-left":"20px"}},[a("li",null,"分钟：0-59"),a("li",null,"小时：0-23"),a("li",null,"日：1-31"),a("li",null,"月：1-12"),a("li",null,"星期：0-7 (0和7都表示周日)")],-1)),at=p(()=>a("p",null,[a("strong",null,"特殊字符："),c("* (任意值)、- (范围)、, (列表)、/ (步长)")],-1)),nt={class:"cron-examples"},st=p(()=>a("h4",null,"常用示例：",-1)),lt={class:"example-content"},it={class:"example-expression"},rt={class:"example-description"},ct={class:"example-category"},dt={class:"dialog-footer"};function ut(t,s,S,F,n,r){const M=i("Warning"),_=i("el-icon"),m=i("el-card"),y=i("el-col"),L=i("Check"),B=i("Delete"),I=i("DataBoard"),C=i("el-row"),b=i("Refresh"),h=i("el-button"),z=i("el-input-number"),x=i("el-form-item"),N=i("View"),H=i("el-form"),R=i("Close"),g=i("el-table-column"),D=i("el-tag"),T=i("el-table"),U=i("el-pagination"),A=i("el-switch"),Q=i("QuestionFilled"),j=i("el-input"),W=i("el-alert"),q=i("el-dialog"),E=ae("loading");return f(),w("div",ve,[xe,e(C,{gutter:20,class:"stats-row"},{default:o(()=>[e(y,{xs:24,sm:12,md:6,lg:6},{default:o(()=>[e(m,{class:"stats-card"},{default:o(()=>[a("div",we,[a("div",Ce,[e(_,null,{default:o(()=>[e(M)]),_:1})]),a("div",be,[a("div",De,d(n.statsData.expiredCount||0),1),Pe])])]),_:1})]),_:1}),e(y,{xs:24,sm:12,md:6,lg:6},{default:o(()=>[e(m,{class:"stats-card"},{default:o(()=>[a("div",Ve,[a("div",ze,[e(_,null,{default:o(()=>[e(L)]),_:1})]),a("div",Ee,[a("div",ke,d(n.statsData.activeCount||0),1),Se])])]),_:1})]),_:1}),e(y,{xs:24,sm:12,md:6,lg:6},{default:o(()=>[e(m,{class:"stats-card"},{default:o(()=>[a("div",Fe,[a("div",Le,[e(_,null,{default:o(()=>[e(B)]),_:1})]),a("div",Be,[a("div",He,d(n.statsData.deletedCount||0),1),Te])])]),_:1})]),_:1}),e(y,{xs:24,sm:12,md:6,lg:6},{default:o(()=>[e(m,{class:"stats-card"},{default:o(()=>[a("div",Ue,[a("div",Me,[e(_,null,{default:o(()=>[e(I)]),_:1})]),a("div",Ie,[a("div",Ne,d(n.statsData.totalCount||0),1),Re])])]),_:1})]),_:1})]),_:1}),e(m,{class:"operation-card"},{header:o(()=>[a("div",Ae,[Qe,e(h,{link:"",onClick:r.refreshStats},{default:o(()=>[e(_,null,{default:o(()=>[e(b)]),_:1}),c(" 刷新统计 ")]),_:1},8,["onClick"])])]),default:o(()=>[e(H,{inline:!0,class:"operation-form"},{default:o(()=>[e(x,{label:"过期天数"},{default:o(()=>[e(z,{modelValue:n.cleanupForm.expireDays,"onUpdate:modelValue":s[0]||(s[0]=l=>n.cleanupForm.expireDays=l),min:1,max:365,placeholder:"过期天数",style:{width:"120px"}},null,8,["modelValue"]),je]),_:1}),e(x,null,{default:o(()=>[e(h,{type:"primary",onClick:r.previewCleanup,loading:n.previewLoading},{default:o(()=>[e(_,null,{default:o(()=>[e(N)]),_:1}),c(" 预览清理数据 ")]),_:1},8,["onClick","loading"]),e(h,{type:"warning",onClick:r.executeCleanup,loading:n.cleanupLoading},{default:o(()=>[e(_,null,{default:o(()=>[e(B)]),_:1}),c(" 执行清理 ")]),_:1},8,["onClick","loading"])]),_:1})]),_:1})]),_:1}),n.showPreview?(f(),P(m,{key:0,class:"preview-card"},{header:o(()=>[a("div",We,[a("span",null,"预览清理数据 ("+d(n.previewData.total)+" 条)",1),e(h,{link:"",onClick:s[1]||(s[1]=l=>n.showPreview=!1)},{default:o(()=>[e(_,null,{default:o(()=>[e(R)]),_:1}),c(" 关闭预览 ")]),_:1})])]),default:o(()=>[k((f(),P(T,{data:n.previewData.rows,border:"",stripe:"",style:{width:"100%"},"max-height":400},{default:o(()=>[e(g,{prop:"ip_address",label:"IP地址",width:"200"}),e(g,{prop:"hostname",label:"主机名",width:"200"}),e(g,{prop:"discovery_time",label:"发现时间",width:"180"},{default:o(l=>[c(d(r.formatDateTime(l.row.discovery_time)),1)]),_:1}),e(g,{prop:"created_at",label:"创建时间",width:"180"},{default:o(l=>[c(d(r.formatDateTime(l.row.created_at)),1)]),_:1}),e(g,{label:"过期天数",width:"100"},{default:o(l=>[e(D,{type:"warning"},{default:o(()=>[c(d(r.getDaysAgo(l.row.discovery_time))+" 天前 ",1)]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[E,n.previewLoading]]),a("div",qe,[e(U,{"current-page":n.previewPagination.currentPage,"onUpdate:currentPage":s[2]||(s[2]=l=>n.previewPagination.currentPage=l),"page-size":n.previewPagination.pageSize,"onUpdate:pageSize":s[3]||(s[3]=l=>n.previewPagination.pageSize=l),"page-sizes":[10,20,50,100],total:n.previewData.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:r.handlePreviewSizeChange,onCurrentChange:r.handlePreviewCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})):V("",!0),e(m,{class:"history-card"},{header:o(()=>[a("div",Ge,[Oe,e(h,{link:"",onClick:r.loadHistory},{default:o(()=>[e(_,null,{default:o(()=>[e(b)]),_:1}),c(" 刷新历史 ")]),_:1},8,["onClick"])])]),default:o(()=>[k((f(),P(T,{data:n.historyData.rows,border:"",stripe:"",style:{width:"100%"},"max-height":300},{default:o(()=>[e(g,{prop:"log_level",label:"级别",width:"80"},{default:o(l=>[e(D,{type:r.getLogLevelType(l.row.log_level)},{default:o(()=>[c(d(l.row.log_level),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"message",label:"操作",width:"150"}),e(g,{prop:"details",label:"详情","min-width":"200","show-overflow-tooltip":""}),e(g,{prop:"created_by",label:"操作人",width:"100"}),e(g,{prop:"created_at",label:"操作时间",width:"180"},{default:o(l=>[c(d(r.formatDateTime(l.row.created_at)),1)]),_:1})]),_:1},8,["data"])),[[E,n.historyLoading]]),a("div",Je,[e(U,{"current-page":n.historyPagination.currentPage,"onUpdate:currentPage":s[4]||(s[4]=l=>n.historyPagination.currentPage=l),"page-size":n.historyPagination.pageSize,"onUpdate:pageSize":s[5]||(s[5]=l=>n.historyPagination.pageSize=l),"page-sizes":[10,20,50],total:n.historyData.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:r.handleHistorySizeChange,onCurrentChange:r.handleHistoryCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),e(m,{class:"config-card"},{header:o(()=>[a("div",Ke,[Xe,e(h,{link:"",onClick:r.loadConfig},{default:o(()=>[e(_,null,{default:o(()=>[e(b)]),_:1}),c(" 刷新配置 ")]),_:1},8,["onClick"])])]),default:o(()=>[e(H,{model:n.configForm,"label-width":"120px",class:"config-form"},{default:o(()=>[e(C,{gutter:20},{default:o(()=>[e(y,{span:12},{default:o(()=>[e(x,{label:"启用自动清理"},{default:o(()=>[e(A,{modelValue:n.configForm.cleanup_enabled,"onUpdate:modelValue":s[6]||(s[6]=l=>n.configForm.cleanup_enabled=l),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:o(()=>[e(x,{label:"默认过期天数"},{default:o(()=>[e(z,{modelValue:n.configForm.cleanup_expire_days,"onUpdate:modelValue":s[7]||(s[7]=l=>n.configForm.cleanup_expire_days=l),min:1,max:365,style:{width:"120px"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:20},{default:o(()=>[e(y,{span:12},{default:o(()=>[e(x,{label:"执行时间"},{default:o(()=>[e(j,{modelValue:n.configForm.cleanup_schedule,"onUpdate:modelValue":s[8]||(s[8]=l=>n.configForm.cleanup_schedule=l),placeholder:"Cron表达式，如：0 2 * * *",onBlur:r.validateCronExpression,class:ne({"is-error":n.cronValidation.error})},{append:o(()=>[e(h,{onClick:r.showCronExamples,type:"primary",size:"small"},{default:o(()=>[e(_,null,{default:o(()=>[e(Q)]),_:1})]),_:1},8,["onClick"])]),_:1},8,["modelValue","onBlur","class"]),n.cronValidation.error?V("",!0):(f(),w("div",Ye,[c(d(n.cronValidation.description||"Cron表达式，默认每天凌晨2点执行")+" ",1),n.cronValidation.nextExecution?(f(),w("span",Ze," (下次执行: "+d(r.formatDateTime(n.cronValidation.nextExecution))+") ",1)):V("",!0)])),n.cronValidation.error?(f(),w("div",$e,d(n.cronValidation.error),1)):V("",!0)]),_:1})]),_:1}),e(y,{span:12},{default:o(()=>[e(x,{label:"批处理大小"},{default:o(()=>[e(z,{modelValue:n.configForm.cleanup_batch_size,"onUpdate:modelValue":s[9]||(s[9]=l=>n.configForm.cleanup_batch_size=l),min:100,max:1e4,style:{width:"120px"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(x,null,{default:o(()=>[e(h,{type:"primary",onClick:r.saveConfig,loading:n.configLoading},{default:o(()=>[e(_,null,{default:o(()=>[e(L)]),_:1}),c(" 保存配置 ")]),_:1},8,["onClick","loading"]),e(h,{onClick:r.loadConfig},{default:o(()=>[e(_,null,{default:o(()=>[e(b)]),_:1}),c(" 重置 ")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(q,{modelValue:n.cronExamplesVisible,"onUpdate:modelValue":s[11]||(s[11]=l=>n.cronExamplesVisible=l),title:"Cron 表达式示例",width:"800px","destroy-on-close":""},{footer:o(()=>[a("span",dt,[e(h,{onClick:s[10]||(s[10]=l=>n.cronExamplesVisible=!1)},{default:o(()=>[c("关闭")]),_:1})])]),default:o(()=>[k((f(),w("div",null,[e(W,{title:"Cron 表达式格式说明",type:"info",closable:!1,style:{"margin-bottom":"20px"}},{default:o(()=>[et,tt,ot,at]),_:1}),a("div",nt,[st,e(C,{gutter:20},{default:o(()=>[(f(!0),w(se,null,le(n.cronExamples,l=>(f(),P(y,{span:12,key:l.expression},{default:o(()=>[e(m,{class:"example-card",shadow:"hover",onClick:pt=>r.selectCronExample(l.expression),style:{cursor:"pointer","margin-bottom":"10px"}},{default:o(()=>[a("div",lt,[a("div",it,[e(D,{type:"primary"},{default:o(()=>[c(d(l.expression),1)]),_:2},1024)]),a("div",rt,d(l.description),1),a("div",ct,[e(D,{size:"small",type:"info"},{default:o(()=>[c(d(l.category),1)]),_:2},1024)])])]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})])])),[[E,n.cronExamplesLoading]])]),_:1},8,["modelValue"])])}const ft=O(ye,[["render",ut],["__scopeId","data-v-afd2c828"]]);export{ft as default};
