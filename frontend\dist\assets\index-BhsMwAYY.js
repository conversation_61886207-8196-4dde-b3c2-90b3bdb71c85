import{_ as ae,A as te,v as le,B as oe,c as P,a as e,f as k,w as a,b as i,y as ne,F as B,l as T,h as c,C as re,D as se,m as w,t as F,r as D,R as W,o as ce,Y as de,p as ie,q as ue,E as f,Q as me,e as _}from"./index-MGgq8mV5.js";import{s as L}from"./request-DGO27LS-.js";import{u as G,w as _e}from"./xlsx-DH6WiNtO.js";import{F as pe}from"./FileSaver.min-Cr9SGvul.js";const he={name:"OpsChangeManagement",components:{Plus:oe,Search:le,Download:te},setup(){const C=de(),s=D(null),r=W({keyword:"",changeLevel:"",system:"",requester:"",implementer:"",startDate:"",endDate:"",nonCompliant:"",oaProcess:"",signedArchive:"",isOverdue:"",sortProp:"planned_change_time",sortOrder:"desc"}),o=D([]),J=l=>{l?(r.startDate=l[0],r.endDate=l[1]):(r.startDate="",r.endDate="")},S=D([]),z=D(!1),p=D({}),d=W({currentPage:1,pageSize:10,total:0}),h=D([]),v=D([]),U=D([]),A=async()=>{try{const l=await L({url:"/api/get_cmdb_data_dictionary",method:"post",data:{dict_type:"P"}});l.code===0&&(h.value=l.msg.filter(n=>n&&n.dict_code!==null&&n.dict_name!==null))}catch(l){console.error("获取变更级别列表失败:",l),f.error("获取变更级别列表失败")}},E=async()=>{try{const l=await L({url:"/api/get_user_list",method:"post"});l.code===0&&(U.value=l.msg.filter(n=>n&&n.username!==null&&n.username!==void 0&&n.real_name!==null&&n.real_name!==void 0))}catch(l){console.error("获取用户列表失败:",l),f.error("获取用户列表失败")}},V=async()=>{try{const l=await L({url:"/api/get_system_list",method:"post"});l.code===0&&(v.value=l.msg.filter(n=>n&&n.system_abbreviation!==null&&n.system_abbreviation!==void 0))}catch(l){console.error("获取系统列表失败:",l),f.error("获取系统列表失败")}},b=async()=>{z.value=!0,console.log("搜索表单数据:",r);try{const l={...r,currentPage:d.currentPage,pageSize:d.pageSize,change_level:r.changeLevel};console.log("发送请求数据:",l);const n=await L({url:"/api/get_ops_change_management",method:"post",data:l});n.code===0?(S.value=n.msg,d.total=n.total):f.error(`获取列表失败: ${n.msg}`)}catch(l){console.error("获取变更管理列表失败:",l),f.error("获取变更管理列表失败")}finally{z.value=!1}},I=({prop:l,order:n})=>{r.sortProp=l,r.sortOrder=n==="ascending"?"asc":"desc",b()},R=()=>{d.currentPage=1,b()},Y=()=>{r.keyword="",r.changeLevel="",r.system="",r.requester="",r.implementer="",r.nonCompliant="",r.oaProcess="",r.signedArchive="",r.isOverdue="",r.startDate="",r.endDate="",r.sortProp="planned_change_time",r.sortOrder="desc",o.value=[],d.currentPage=1,s.value&&s.value.resetFields(),b(),f.success("搜索条件已重置")},q=l=>{d.pageSize=l,b()},y=l=>{d.currentPage=l,b()},u=()=>{C.push("/ops_change_management/detail/new")},N=l=>{C.push(`/ops_change_management/detail/${l.id}`)},K=l=>{C.push(`/ops_change_management/detail/${l.id}?tab=attachments`)},H=l=>{me.confirm(`确定要删除变更 "${l.change_id}" 吗？此操作不可恢复。`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const n=await L({url:"/api/del_ops_change_management",method:"post",data:{id:l.id,username:localStorage.getItem("loginUsername")||"admin"}});n.code===0?(f.success("删除成功"),b()):f.error(`删除失败: ${n.msg}`)}catch(n){console.error("删除变更失败:",n),f.error("删除变更失败")}}).catch(()=>{})},t=async l=>{if(!l.operation_sheet){f.warning("该变更没有上传变更操作表");return}try{p.value[l.id]=!0;const n=`/api/download_ops_change_file?changeId=${l.change_id}&fileType=operation_sheet&direct=true`,g=document.createElement("a");g.href=n,g.target="_blank",document.body.appendChild(g),g.click(),document.body.removeChild(g),f.success("变更操作表下载成功")}catch(n){console.error("下载变更操作表失败:",n),f.error("下载变更操作表失败")}finally{p.value[l.id]=!1}},j=l=>{if(!l)return{type:"info",text:"未设定",class:"unset"};const n=new Date,g=new Date(l),x=Math.ceil((g-n)/(1e3*60*60*24));return x<0?{type:"danger",text:"已过期",class:"overdue"}:x===0?{type:"warning",text:"今天",class:"today"}:x===1?{type:"warning",text:"明天",class:"tomorrow"}:x<=7?{type:"primary",text:"本周",class:"this-week"}:{type:"success",text:"未来",class:"future"}},Q=l=>{if(!l)return"";try{const n=new Date(l);if(isNaN(n.getTime()))return l;const g=ee=>String(ee).padStart(2,"0"),x=n.getFullYear(),O=g(n.getMonth()+1),M=g(n.getDate()),Z=g(n.getHours()),m=g(n.getMinutes()),$=g(n.getSeconds());return n.getHours()===0&&n.getMinutes()===0&&n.getSeconds()===0?`${x}-${O}-${M}`:`${x}-${O}-${M} ${Z}:${m}:${$}`}catch(n){return console.error("日期格式化错误:",n),l}},X=()=>{if(!S.value||S.value.length===0){f.warning("没有数据可以导出");return}try{const l=["变更编号","变更名称","变更系统","变更级别","计划变更时间","OA流程","签字存档","变更操作表","变更负责人","变更实施人","创建时间","创建人","更新时间","更新人"],n=S.value.map(m=>[m.change_id||"",m.title||"",m.system||"",m.change_level_name_display||"",m.formatted_change_time||"",m.oa_process?"已上传":"未上传",m.signed_archive?"已上传":"未上传",m.operation_sheet?"已上传":"未上传",m.requester_name||"",m.implementers_name||"",Q(m.created_at),m.created_by||"",Q(m.updated_at),m.updated_by||""]),g=[l,...n],x=G.aoa_to_sheet(g),O=G.book_new();G.book_append_sheet(O,x,"Sheet1");const M=_e(O,{bookType:"xlsx",type:"array"}),Z=new Blob([M],{type:"application/octet-stream"});pe.saveAs(Z,"变更管理数据.xlsx"),f.success("数据导出成功")}catch(l){console.error("导出数据失败:",l),f.error("导出数据失败")}};return ce(async()=>{C.currentRoute.value.query.search_ip&&(r.keyword=C.currentRoute.value.query.search_ip,console.log("从全局搜索跳转，设置关键词:",r.keyword)),await Promise.all([A(),E(),V()]),b()}),{searchFormRef:s,searchForm:r,dateRange:o,tableData:S,tableLoading:z,downloadLoading:p,pagination:d,changeLevelOptions:h,systemOptions:v,userOptions:U,formatDateTime:Q,getChangeTimeStatus:j,handleDateRangeChange:J,handleSearch:R,resetSearch:Y,handleSizeChange:q,handleCurrentChange:y,handleSortChange:I,handleAdd:u,handleView:N,handleUpload:K,handleDelete:H,handleDownloadOperationSheet:t,exportData:X}}},ge=C=>(ie("data-v-dc338c8c"),C=C(),ue(),C),fe={class:"app-container"},be={class:"button-container"},ye={class:"action-bar unified-action-bar"},ve={class:"action-bar-left"},we={class:"action-bar-right"},Ce={class:"status-legend"},ke=ge(()=>k("span",{class:"legend-title"},"状态图例：",-1)),xe={class:"change-time-cell"},De={class:"time-text"},Se={style:{display:"flex","white-space":"nowrap",gap:"8px"}},Ve={class:"pagination"};function Fe(C,s,r,o,J,S){const z=i("el-input"),p=i("el-form-item"),d=i("el-col"),h=i("el-option"),v=i("el-select"),U=i("el-date-picker"),A=i("el-row"),E=i("Search"),V=i("el-icon"),b=i("el-button"),I=i("el-form"),R=i("el-card"),Y=i("Plus"),q=i("Download"),y=i("el-tag"),u=i("el-table-column"),N=i("el-table"),K=i("el-pagination"),H=se("loading");return _(),P("div",fe,[e(R,{class:"search-card"},{default:a(()=>[e(I,{model:o.searchForm,ref:"searchFormRef","label-width":"100px","label-position":"right"},{default:a(()=>[e(A,{gutter:20},{default:a(()=>[e(d,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(p,{label:"关键字"},{default:a(()=>[e(z,{modelValue:o.searchForm.keyword,"onUpdate:modelValue":s[0]||(s[0]=t=>o.searchForm.keyword=t),placeholder:"变更编号/变更名称",clearable:"",onKeyup:ne(o.handleSearch,["enter"]),class:"form-control"},null,8,["modelValue","onKeyup"])]),_:1})]),_:1}),e(d,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(p,{label:"变更级别"},{default:a(()=>[e(v,{modelValue:o.searchForm.changeLevel,"onUpdate:modelValue":s[1]||(s[1]=t=>o.searchForm.changeLevel=t),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:a(()=>[(_(!0),P(B,null,T(o.changeLevelOptions,t=>(_(),w(h,{key:t.dict_code,label:t.dict_name,value:t.dict_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(p,{label:"变更系统"},{default:a(()=>[e(v,{modelValue:o.searchForm.system,"onUpdate:modelValue":s[2]||(s[2]=t=>o.searchForm.system=t),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:a(()=>[(_(!0),P(B,null,T(o.systemOptions,t=>(_(),w(h,{key:t.system_abbreviation,label:t.system_abbreviation,value:t.system_abbreviation},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(p,{label:"变更负责人"},{default:a(()=>[e(v,{modelValue:o.searchForm.requester,"onUpdate:modelValue":s[3]||(s[3]=t=>o.searchForm.requester=t),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:a(()=>[(_(!0),P(B,null,T(o.userOptions,t=>(_(),w(h,{key:t.username,label:t.real_name,value:t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(p,{label:"变更实施人"},{default:a(()=>[e(v,{modelValue:o.searchForm.implementer,"onUpdate:modelValue":s[4]||(s[4]=t=>o.searchForm.implementer=t),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:a(()=>[(_(!0),P(B,null,T(o.userOptions,t=>(_(),w(h,{key:t.username,label:t.real_name,value:t.username},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(p,{label:"未规范"},{default:a(()=>[e(v,{modelValue:o.searchForm.nonCompliant,"onUpdate:modelValue":s[5]||(s[5]=t=>o.searchForm.nonCompliant=t),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:a(()=>[e(h,{label:"是",value:"是"}),e(h,{label:"否",value:"否"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(p,{label:"OA流程"},{default:a(()=>[e(v,{modelValue:o.searchForm.oaProcess,"onUpdate:modelValue":s[6]||(s[6]=t=>o.searchForm.oaProcess=t),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:a(()=>[e(h,{label:"已上传",value:"已上传"}),e(h,{label:"未上传",value:"未上传"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(p,{label:"签字存档"},{default:a(()=>[e(v,{modelValue:o.searchForm.signedArchive,"onUpdate:modelValue":s[7]||(s[7]=t=>o.searchForm.signedArchive=t),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:a(()=>[e(h,{label:"已上传",value:"已上传"}),e(h,{label:"未上传",value:"未上传"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(p,{label:"是否过期"},{default:a(()=>[e(v,{modelValue:o.searchForm.isOverdue,"onUpdate:modelValue":s[8]||(s[8]=t=>o.searchForm.isOverdue=t),placeholder:"请选择",clearable:"",filterable:"",class:"form-control"},{default:a(()=>[e(h,{label:"已过期",value:"已过期"}),e(h,{label:"未过期",value:"未过期"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(d,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(p,{label:"变更时间"},{default:a(()=>[e(U,{modelValue:o.dateRange,"onUpdate:modelValue":s[9]||(s[9]=t=>o.dateRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",onChange:o.handleDateRangeChange,class:"form-control"},null,8,["modelValue","onChange"])]),_:1})]),_:1})]),_:1}),e(A,{gutter:20},{default:a(()=>[e(d,{xs:24,sm:12,md:8,lg:24,class:"search-buttons-col"},{default:a(()=>[e(p,null,{default:a(()=>[k("div",be,[e(b,{type:"primary",onClick:o.handleSearch},{default:a(()=>[e(V,null,{default:a(()=>[e(E)]),_:1}),c("查询 ")]),_:1},8,["onClick"]),e(b,{onClick:o.resetSearch},{default:a(()=>[c("重置")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),k("div",ye,[k("div",ve,[e(b,{type:"success",onClick:o.handleAdd},{default:a(()=>[e(V,null,{default:a(()=>[e(Y)]),_:1}),c("新增变更 ")]),_:1},8,["onClick"])]),k("div",we,[e(b,{type:"info",onClick:o.exportData},{default:a(()=>[e(V,null,{default:a(()=>[e(q)]),_:1}),c(" 导出数据 ")]),_:1},8,["onClick"]),k("div",Ce,[ke,e(y,{type:"danger",size:"small"},{default:a(()=>[c("已过期")]),_:1}),e(y,{type:"warning",size:"small"},{default:a(()=>[c("今天/明天")]),_:1}),e(y,{type:"primary",size:"small"},{default:a(()=>[c("本周")]),_:1}),e(y,{type:"success",size:"small"},{default:a(()=>[c("未来")]),_:1})])])]),e(R,{class:"table-card"},{default:a(()=>[re((_(),w(N,{data:o.tableData,border:"",stripe:"",style:{width:"100%"},"table-layout":"fixed","header-cell-style":{background:"#f5f7fa",color:"#606266"},onSortChange:o.handleSortChange},{default:a(()=>[e(u,{prop:"change_id",label:"变更编号","min-width":"150",sortable:""}),e(u,{prop:"title",label:"变更名称","min-width":"250","show-overflow-tooltip":"",sortable:""}),e(u,{prop:"system",label:"变更系统","min-width":"150","max-width":"200","show-overflow-tooltip":"",sortable:""}),e(u,{prop:"change_level_name_display",label:"变更级别","min-width":"100",sortable:""}),e(u,{label:"计划变更时间","min-width":"150",sortable:"custom",prop:"planned_change_time"},{default:a(t=>[k("div",xe,[k("div",De,F(t.row.formatted_change_time),1),e(y,{type:o.getChangeTimeStatus(t.row.planned_change_time).type,size:"small",class:"time-status-tag"},{default:a(()=>[c(F(o.getChangeTimeStatus(t.row.planned_change_time).text),1)]),_:2},1032,["type"])])]),_:1}),e(u,{prop:"requester_name",label:"变更负责人","min-width":"120",sortable:""}),e(u,{prop:"implementers_name",label:"变更实施人","min-width":"120",sortable:"","show-overflow-tooltip":""}),e(u,{label:"OA流程","min-width":"100",align:"center",sortable:""},{default:a(t=>[t.row.oa_process?(_(),w(y,{key:0,type:"success"},{default:a(()=>[c("已上传")]),_:1})):(_(),w(y,{key:1,type:"info"},{default:a(()=>[c("未上传")]),_:1}))]),_:1}),e(u,{label:"签字存档","min-width":"100",align:"center",sortable:""},{default:a(t=>[t.row.signed_archive?(_(),w(y,{key:0,type:"success"},{default:a(()=>[c("已上传")]),_:1})):(_(),w(y,{key:1,type:"info"},{default:a(()=>[c("未上传")]),_:1}))]),_:1}),e(u,{label:"变更操作表","min-width":"100",align:"center",sortable:""},{default:a(t=>[t.row.operation_sheet?(_(),w(y,{key:0,type:"success"},{default:a(()=>[c("已上传")]),_:1})):(_(),w(y,{key:1,type:"info"},{default:a(()=>[c("未上传")]),_:1}))]),_:1}),e(u,{label:"创建时间","min-width":"150",sortable:"created_at"},{default:a(t=>[c(F(o.formatDateTime(t.row.created_at)),1)]),_:1}),e(u,{label:"创建人","min-width":"100",sortable:"created_by"},{default:a(t=>[c(F(t.row.created_by),1)]),_:1}),e(u,{label:"更新时间","min-width":"150",sortable:"updated_at"},{default:a(t=>[c(F(o.formatDateTime(t.row.updated_at)),1)]),_:1}),e(u,{label:"更新人","min-width":"100",sortable:"updated_by"},{default:a(t=>[c(F(t.row.updated_by),1)]),_:1}),e(u,{label:"操作",fixed:"right","min-width":"190"},{default:a(t=>[k("div",Se,[e(b,{type:"primary",size:"small",onClick:j=>o.handleView(t.row)},{default:a(()=>[c(" 详情 ")]),_:2},1032,["onClick"]),e(b,{type:"success",size:"small",onClick:j=>o.handleDownloadOperationSheet(t.row),loading:o.downloadLoading[t.row.id],disabled:!t.row.operation_sheet},{default:a(()=>[e(V,null,{default:a(()=>[e(q)]),_:1}),c(" 下载操作表 ")]),_:2},1032,["onClick","loading","disabled"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[H,o.tableLoading]]),k("div",Ve,[e(K,{background:"","current-page":o.pagination.currentPage,"page-size":o.pagination.pageSize,total:o.pagination.total,"page-sizes":[10,20,50,100,1e3],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const Ue=ae(he,[["render",Fe],["__scopeId","data-v-dc338c8c"]]);export{Ue as default};
