import{_ as O,A as j,v as H,B as J,c as B,a as e,w as a,f as _,b as n,h as u,m as v,x as V,t as f,y as R,C as L,D as Q,e as h}from"./index-MGgq8mV5.js";import{u as P,w as W}from"./xlsx-DH6WiNtO.js";import{F as X}from"./FileSaver.min-Cr9SGvul.js";const Z={components:{Plus:J,Search:H,Download:j},data(){return{userArr:[],loading:!1,hasDeletePermission:localStorage.getItem("role_code")?localStorage.getItem("role_code").includes("D"):!1,hasUpdatePermission:localStorage.getItem("role_code")?localStorage.getItem("role_code").includes("U"):!1,hasInsertPermission:localStorage.getItem("role_code")?localStorage.getItem("role_code").includes("I"):!1,dialogVisible:{add:!1,edit:!1,delete:!1,detail:!1},detailData:{},search:{description:null,raised_time:null,raised_by:null,is_solved:null,solution_type:null,total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},formData:{id:null,description:"",raised_time:"",raised_by:"",is_solved:"",solution_type:"",planned_dev_time:"",dev_cycle_days:null,solution:"",solved_by:"",solved_time:"",created_time:""},rules:{description:[{required:!0,message:"请输入需求问题描述",trigger:"blur"}],raised_time:[{required:!0,message:"请选择提出日期",trigger:"change"}],raised_by:[{required:!0,message:"请输入上报人",trigger:"blur"}]}}},mounted(){this.loadData(),this.checkPermissions()},methods:{checkPermissions(){const s=localStorage.getItem("role_code");console.log("当前用户角色权限:",s),this.hasDeletePermission=s?s.includes("D"):!1,this.hasUpdatePermission=s?s.includes("U"):!1,this.hasInsertPermission=s?s.includes("I"):!1,console.log("权限状态:",{删除权限:this.hasDeletePermission,更新权限:this.hasUpdatePermission,新增权限:this.hasInsertPermission})},handlePageChange(s){this.search.currentPage=s,this.loadData()},handlePageSizeChange(s){this.search.pageSize=parseInt(s),this.search.currentPage=1,this.loadData()},handleSortChange({prop:s,order:t}){this.search.sortProp=s,this.search.sortOrder=t==="ascending"?"asc":"desc",this.loadData()},async loadData(){try{this.loading=!0;const s=await this.$axios.post("/api/get_cmdb_issue_collection",this.search);this.userArr=s.data.msg,this.search.total=s.data.total}catch(s){console.error("数据加载失败:",s),this.$message.error("数据加载失败")}finally{this.loading=!1}},async submitAdd(){var s,t;try{await this.$refs.addForm.validate();const i={...this.formData};i.planned_dev_time===""&&(i.planned_dev_time=null),i.raised_time===""&&(i.raised_time=null),i.solved_time===""&&(i.solved_time=null),await this.$axios.post("/api/add_cmdb_issue_collection",i),this.$message.success("添加成功"),this.dialogVisible.add=!1,this.loadData()}catch(i){if(i===!1){this.$message.warning("请填写必填字段");return}console.error("添加失败:",i),this.$message.error("添加失败: "+(((t=(s=i.response)==null?void 0:s.data)==null?void 0:t.msg)||i.message))}},async submitEdit(){var s,t;try{await this.$refs.editForm.validate();const i={...this.formData};i.planned_dev_time===""&&(i.planned_dev_time=null),i.raised_time===""&&(i.raised_time=null),i.solved_time===""&&(i.solved_time=null),await this.$axios.post("/api/update_cmdb_issue_collection",i),this.$message.success("更新成功"),this.dialogVisible.edit=!1,this.loadData()}catch(i){if(i===!1){this.$message.warning("请填写必填字段");return}console.error("更新失败:",i),this.$message.error("更新失败: "+(((t=(s=i.response)==null?void 0:s.data)==null?void 0:t.msg)||i.message))}},async submitDelete(){try{await this.$axios.post("/api/del_cmdb_issue_collection",this.formData),this.$message.success("删除成功"),this.loadData(),this.dialogVisible.delete=!1}catch(s){console.error("删除失败:",s),this.$message.error("删除失败")}},handleAdd(){this.dialogVisible.add=!this.dialogVisible.add,this.formData={},this.formData.is_solved="未解决"},handleEdit(s,t){this.dialogVisible.edit=!0,this.formData.id=t.id,this.formData.description=t.description,this.formData.raised_time=t.raised_time,this.formData.raised_by=t.raised_by,this.formData.is_solved=t.is_solved,this.formData.solution_type=t.solution_type||"",this.formData.planned_dev_time=t.planned_dev_time||"",this.formData.dev_cycle_days=t.dev_cycle_days||null,this.formData.solution=t.solution,this.formData.solved_by=t.solved_by,this.formData.solved_time=t.solved_time,this.formData.notification=t.notification},handleDelete(s,t){this.dialogVisible.delete=!this.dialogVisible.delete,this.formData.id=t.id,this.formData.description=t.description},showDetail(s){this.detailData={...s},this.dialogVisible.detail=!0},resetSearch(){this.search={description:null,raised_time:null,raised_by:null,is_solved:null,solution_type:null,total:0,pageSize:10,currentPage:1,sortProp:"updated_at",sortOrder:"desc"},this.$refs.searchFormRef&&this.$refs.searchFormRef.resetFields(),this.loadData(),this.$message.success("搜索条件已重置")},exportData(){const t=this.$refs.table.columns,i=t.map(r=>r.label),S=this.userArr.map(r=>t.map(D=>r[D.property])),l=[i,...S],m=P.aoa_to_sheet(l),c=P.book_new();P.book_append_sheet(c,m,"Sheet1");const d=W(c,{bookType:"xlsx",type:"array"}),y=new Blob([d],{type:"application/octet-stream"});X.saveAs(y,"需求与问题收集.xlsx")}}},$={class:"user-manage"},ee={class:"form-container"},le={class:"dialog-footer"},te={class:"form-container"},ae={class:"dialog-footer"},oe={class:"detail-container"},se={class:"detail-text"},ie={class:"detail-text"},de={class:"dialog-footer"},re={class:"button-container"},ne=["onClick"],ue={key:1},me=["onClick"],pe={style:{display:"flex","white-space":"nowrap","justify-content":"center",gap:"5px"}},_e={class:"pagination"};function fe(s,t,i,S,l,m){const c=n("el-input"),d=n("el-form-item"),y=n("el-date-picker"),r=n("el-option"),D=n("el-select"),M=n("el-input-number"),k=n("el-form"),b=n("el-button"),w=n("el-dialog"),A=n("el-alert"),g=n("el-descriptions-item"),Y=n("el-tag"),F=n("el-descriptions"),x=n("el-col"),C=n("el-row"),q=n("Search"),z=n("el-icon"),E=n("Plus"),G=n("Download"),I=n("el-card"),p=n("el-table-column"),K=n("el-table"),N=n("el-pagination"),T=Q("loading");return h(),B("div",$,[e(w,{modelValue:l.dialogVisible.add,"onUpdate:modelValue":t[11]||(t[11]=o=>l.dialogVisible.add=o),title:"添加需求问题记录",width:"850","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:a(()=>[_("div",le,[e(b,{size:"large",onClick:t[10]||(t[10]=o=>l.dialogVisible.add=!1)},{default:a(()=>[u("返回")]),_:1}),e(b,{size:"large",type:"primary",onClick:m.submitAdd},{default:a(()=>[u("确定")]),_:1},8,["onClick"])])]),default:a(()=>[_("div",ee,[e(k,{ref:"addForm",model:l.formData,rules:l.rules,"label-position":"right","label-width":"130px"},{default:a(()=>[e(d,{label:"需求问题描述:",prop:"description",rules:[{required:!0,message:"请输入需求问题描述",trigger:"blur"}]},{default:a(()=>[e(c,{modelValue:l.formData.description,"onUpdate:modelValue":t[0]||(t[0]=o=>l.formData.description=o),type:"textarea",autosize:{minRows:5,maxRows:12},style:{width:"600px","font-size":"14px"},placeholder:"请详细描述需求问题，支持多行文本",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"提出日期:",prop:"raised_time",rules:[{required:!0,message:"请选择提出日期",trigger:"change"}]},{default:a(()=>[e(y,{modelValue:l.formData.raised_time,"onUpdate:modelValue":t[1]||(t[1]=o=>l.formData.raised_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),e(d,{label:"上报人:",prop:"raised_by",rules:[{required:!0,message:"请输入上报人",trigger:"blur"}]},{default:a(()=>[e(c,{modelValue:l.formData.raised_by,"onUpdate:modelValue":t[2]||(t[2]=o=>l.formData.raised_by=o),style:{width:"380px","font-size":"14px"},clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"是否解决:"},{default:a(()=>[e(D,{modelValue:l.formData.is_solved,"onUpdate:modelValue":t[3]||(t[3]=o=>l.formData.is_solved=o),style:{width:"380px","font-size":"14px"}},{default:a(()=>[e(r,{label:"已解决",value:"已解决"}),e(r,{label:"未解决",value:"未解决"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"方案类型:"},{default:a(()=>[e(D,{modelValue:l.formData.solution_type,"onUpdate:modelValue":t[4]||(t[4]=o=>l.formData.solution_type=o),style:{width:"380px","font-size":"14px"},placeholder:"请选择方案类型"},{default:a(()=>[e(r,{label:"新需求开发",value:"新需求开发"}),e(r,{label:"代码优化",value:"代码优化"}),e(r,{label:"BUG修复",value:"BUG修复"}),e(r,{label:"数据处理",value:"数据处理"}),e(r,{label:"不做任何处理",value:"不做任何处理"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"计划开发日期:"},{default:a(()=>[e(y,{modelValue:l.formData.planned_dev_time,"onUpdate:modelValue":t[5]||(t[5]=o=>l.formData.planned_dev_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择计划开发日期"},null,8,["modelValue"])]),_:1}),e(d,{label:"开发周期(人天):"},{default:a(()=>[e(M,{modelValue:l.formData.dev_cycle_days,"onUpdate:modelValue":t[6]||(t[6]=o=>l.formData.dev_cycle_days=o),min:0,precision:0,style:{width:"380px","font-size":"14px"},placeholder:"请输入开发周期"},null,8,["modelValue"])]),_:1}),e(d,{label:"解决方案:"},{default:a(()=>[e(c,{modelValue:l.formData.solution,"onUpdate:modelValue":t[7]||(t[7]=o=>l.formData.solution=o),type:"textarea",autosize:{minRows:4,maxRows:10},style:{width:"600px","font-size":"14px"},placeholder:"请详细描述解决方案，支持多行文本",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"解决人:"},{default:a(()=>[e(c,{modelValue:l.formData.solved_by,"onUpdate:modelValue":t[8]||(t[8]=o=>l.formData.solved_by=o),style:{width:"380px","font-size":"14px"},clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"解决日期:"},{default:a(()=>[e(y,{modelValue:l.formData.solved_time,"onUpdate:modelValue":t[9]||(t[9]=o=>l.formData.solved_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(w,{modelValue:l.dialogVisible.edit,"onUpdate:modelValue":t[23]||(t[23]=o=>l.dialogVisible.edit=o),title:"编辑需求问题记录",width:"850","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:a(()=>[_("div",ae,[e(b,{size:"large",onClick:t[22]||(t[22]=o=>l.dialogVisible.edit=!1)},{default:a(()=>[u("取消")]),_:1}),e(b,{size:"large",type:"primary",onClick:m.submitEdit},{default:a(()=>[u("更新")]),_:1},8,["onClick"])])]),default:a(()=>[_("div",te,[e(k,{ref:"editForm",model:l.formData,rules:l.rules,"label-position":"right","label-width":"130px"},{default:a(()=>[e(d,{label:"需求问题描述:",prop:"description",rules:[{required:!0,message:"请输入需求问题描述",trigger:"blur"}]},{default:a(()=>[e(c,{modelValue:l.formData.description,"onUpdate:modelValue":t[12]||(t[12]=o=>l.formData.description=o),type:"textarea",autosize:{minRows:5,maxRows:12},style:{width:"600px","font-size":"14px"},placeholder:"请详细描述问题，支持多行文本",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"提出日期:",prop:"raised_time",rules:[{required:!0,message:"请选择提出日期",trigger:"change"}]},{default:a(()=>[e(y,{modelValue:l.formData.raised_time,"onUpdate:modelValue":t[13]||(t[13]=o=>l.formData.raised_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1}),e(d,{label:"上报人:",prop:"raised_by",rules:[{required:!0,message:"请输入上报人",trigger:"blur"}]},{default:a(()=>[e(c,{modelValue:l.formData.raised_by,"onUpdate:modelValue":t[14]||(t[14]=o=>l.formData.raised_by=o),style:{width:"380px","font-size":"14px"},clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"是否解决:"},{default:a(()=>[e(D,{modelValue:l.formData.is_solved,"onUpdate:modelValue":t[15]||(t[15]=o=>l.formData.is_solved=o),style:{width:"380px","font-size":"14px"}},{default:a(()=>[e(r,{label:"已解决",value:"已解决"}),e(r,{label:"未解决",value:"未解决"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"方案类型:"},{default:a(()=>[e(D,{modelValue:l.formData.solution_type,"onUpdate:modelValue":t[16]||(t[16]=o=>l.formData.solution_type=o),style:{width:"380px","font-size":"14px"},placeholder:"请选择方案类型"},{default:a(()=>[e(r,{label:"新需求开发",value:"新需求开发"}),e(r,{label:"代码优化",value:"代码优化"}),e(r,{label:"BUG修复",value:"BUG修复"}),e(r,{label:"数据处理",value:"数据处理"}),e(r,{label:"不做任何处理",value:"不做任何处理"})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"计划开发日期:"},{default:a(()=>[e(y,{modelValue:l.formData.planned_dev_time,"onUpdate:modelValue":t[17]||(t[17]=o=>l.formData.planned_dev_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择计划开发日期"},null,8,["modelValue"])]),_:1}),e(d,{label:"开发周期(人天):"},{default:a(()=>[e(M,{modelValue:l.formData.dev_cycle_days,"onUpdate:modelValue":t[18]||(t[18]=o=>l.formData.dev_cycle_days=o),min:0,precision:0,style:{width:"380px","font-size":"14px"},placeholder:"请输入开发周期"},null,8,["modelValue"])]),_:1}),e(d,{label:"解决方案:"},{default:a(()=>[e(c,{modelValue:l.formData.solution,"onUpdate:modelValue":t[19]||(t[19]=o=>l.formData.solution=o),type:"textarea",autosize:{minRows:4,maxRows:10},style:{width:"600px","font-size":"14px"},placeholder:"请详细描述解决方案，支持多行文本",clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"解决人:"},{default:a(()=>[e(c,{modelValue:l.formData.solved_by,"onUpdate:modelValue":t[20]||(t[20]=o=>l.formData.solved_by=o),style:{width:"380px","font-size":"14px"},clearable:""},null,8,["modelValue"])]),_:1}),e(d,{label:"解决日期:"},{default:a(()=>[e(y,{modelValue:l.formData.solved_time,"onUpdate:modelValue":t[21]||(t[21]=o=>l.formData.solved_time=o),type:"date","value-format":"YYYY-MM-DD",format:"YYYY-MM-DD",style:{width:"380px","font-size":"14px"},placeholder:"选择日期"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"]),e(w,{modelValue:l.dialogVisible.delete,"onUpdate:modelValue":t[25]||(t[25]=o=>l.dialogVisible.delete=o),title:"删除需求问题记录",width:"500","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:a(()=>[_("div",null,[e(b,{onClick:t[24]||(t[24]=o=>l.dialogVisible.delete=!1)},{default:a(()=>[u("取消")]),_:1}),e(b,{type:"danger",onClick:m.submitDelete},{default:a(()=>[u("确认删除")]),_:1},8,["onClick"])])]),default:a(()=>[e(A,{type:"warning",title:`确定要删除需求问题描述为 ${l.formData.description} 的记录吗？`,closable:!1},null,8,["title"])]),_:1},8,["modelValue"]),e(w,{modelValue:l.dialogVisible.detail,"onUpdate:modelValue":t[27]||(t[27]=o=>l.dialogVisible.detail=o),title:"需求问题详情",width:"850","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:a(()=>[_("div",de,[e(b,{onClick:t[26]||(t[26]=o=>l.dialogVisible.detail=!1)},{default:a(()=>[u("关闭")]),_:1})])]),default:a(()=>[_("div",oe,[e(F,{column:1,border:""},{default:a(()=>[e(g,{label:"需求问题描述"},{default:a(()=>[_("div",se,f(l.detailData.description),1)]),_:1}),l.detailData.solution?(h(),v(g,{key:0,label:"解决方案"},{default:a(()=>[_("div",ie,f(l.detailData.solution),1)]),_:1})):V("",!0),e(g,{label:"提出日期"},{default:a(()=>[u(f(l.detailData.raised_time),1)]),_:1}),e(g,{label:"上报人"},{default:a(()=>[u(f(l.detailData.raised_by),1)]),_:1}),e(g,{label:"是否解决"},{default:a(()=>[e(Y,{type:l.detailData.is_solved==="已解决"?"success":"danger"},{default:a(()=>[u(f(l.detailData.is_solved),1)]),_:1},8,["type"])]),_:1}),l.detailData.solution_type?(h(),v(g,{key:1,label:"方案类型"},{default:a(()=>[e(Y,{type:"info"},{default:a(()=>[u(f(l.detailData.solution_type),1)]),_:1})]),_:1})):V("",!0),l.detailData.planned_dev_time?(h(),v(g,{key:2,label:"计划开发日期"},{default:a(()=>[u(f(l.detailData.planned_dev_time),1)]),_:1})):V("",!0),l.detailData.dev_cycle_days?(h(),v(g,{key:3,label:"开发周期(人天)"},{default:a(()=>[u(f(l.detailData.dev_cycle_days),1)]),_:1})):V("",!0),l.detailData.solved_by?(h(),v(g,{key:4,label:"解决人"},{default:a(()=>[u(f(l.detailData.solved_by),1)]),_:1})):V("",!0),l.detailData.solved_time?(h(),v(g,{key:5,label:"解决日期"},{default:a(()=>[u(f(l.detailData.solved_time),1)]),_:1})):V("",!0)]),_:1})])]),_:1},8,["modelValue"]),e(I,{class:"search-card"},{default:a(()=>[e(k,{model:l.search,ref:"searchFormRef","label-width":"80px","label-position":"right"},{default:a(()=>[e(C,{gutter:20},{default:a(()=>[e(x,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(d,{label:"问题描述"},{default:a(()=>[e(c,{modelValue:l.search.description,"onUpdate:modelValue":t[28]||(t[28]=o=>l.search.description=o),placeholder:"请输入关键词",clearable:"",onKeyup:R(m.loadData,["enter"]),class:"form-control"},null,8,["modelValue","onKeyup"])]),_:1})]),_:1}),e(x,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(d,{label:"提出日期"},{default:a(()=>[e(y,{modelValue:l.search.raised_time,"onUpdate:modelValue":t[29]||(t[29]=o=>l.search.raised_time=o),type:"date",placeholder:"选择日期","value-format":"YYYY-MM-DD",clearable:"",class:"form-control"},null,8,["modelValue"])]),_:1})]),_:1}),e(x,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(d,{label:"上报人"},{default:a(()=>[e(c,{modelValue:l.search.raised_by,"onUpdate:modelValue":t[30]||(t[30]=o=>l.search.raised_by=o),placeholder:"请输入上报人",clearable:"",onKeyup:R(m.loadData,["enter"]),class:"form-control"},null,8,["modelValue","onKeyup"])]),_:1})]),_:1}),e(x,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(d,{label:"是否解决"},{default:a(()=>[e(D,{modelValue:l.search.is_solved,"onUpdate:modelValue":t[31]||(t[31]=o=>l.search.is_solved=o),placeholder:"请选择",clearable:"",class:"form-control"},{default:a(()=>[e(r,{label:"所有",value:""}),e(r,{label:"已解决",value:"已解决"}),e(r,{label:"未解决",value:"未解决"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:20},{default:a(()=>[e(x,{xs:24,sm:12,md:8,lg:6},{default:a(()=>[e(d,{label:"方案类型"},{default:a(()=>[e(D,{modelValue:l.search.solution_type,"onUpdate:modelValue":t[32]||(t[32]=o=>l.search.solution_type=o),placeholder:"请选择",clearable:"",class:"form-control"},{default:a(()=>[e(r,{label:"所有",value:""}),e(r,{label:"新需求开发",value:"新需求开发"}),e(r,{label:"代码优化",value:"代码优化"}),e(r,{label:"BUG修复",value:"BUG修复"}),e(r,{label:"数据处理",value:"数据处理"}),e(r,{label:"不做任何处理",value:"不做任何处理"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:20},{default:a(()=>[e(x,{xs:24,sm:12,md:8,lg:24,class:"search-buttons-col"},{default:a(()=>[e(d,null,{default:a(()=>[_("div",re,[e(b,{type:"primary",onClick:m.loadData},{default:a(()=>[e(z,null,{default:a(()=>[e(q)]),_:1}),u("查询 ")]),_:1},8,["onClick"]),e(b,{onClick:m.resetSearch},{default:a(()=>[u("重置")]),_:1},8,["onClick"]),e(b,{type:"success",disabled:!l.hasInsertPermission,onClick:t[33]||(t[33]=o=>l.hasInsertPermission?m.handleAdd():s.$message.warning("您没有新增权限"))},{default:a(()=>[e(z,null,{default:a(()=>[e(E)]),_:1}),u("新增 ")]),_:1},8,["disabled"]),e(b,{type:"info",onClick:m.exportData},{default:a(()=>[e(z,null,{default:a(()=>[e(G)]),_:1}),u("导出 ")]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(I,{class:"table-card"},{default:a(()=>[L((h(),v(K,{data:l.userArr,ref:"table",border:"",stripe:"","table-layout":"auto",onSortChange:m.handleSortChange},{default:a(()=>[V("",!0),e(p,{prop:"description",label:"需求问题描述",sortable:"","min-width":"400"},{default:a(o=>[_("div",{class:"description-text",onClick:U=>m.showDetail(o.row),title:"点击查看详情"},f(o.row.description),9,ne)]),_:1}),e(p,{prop:"raised_time",label:"提出日期",sortable:""}),e(p,{prop:"raised_by",label:"上报人",sortable:""}),e(p,{prop:"is_solved",label:"是否解决",sortable:""},{default:a(o=>[e(Y,{type:o.row.is_solved==="已解决"?"success":"danger"},{default:a(()=>[u(f(o.row.is_solved),1)]),_:2},1032,["type"])]),_:1}),e(p,{prop:"solution_type",label:"方案类型",sortable:""},{default:a(o=>[o.row.solution_type?(h(),v(Y,{key:0,type:"info"},{default:a(()=>[u(f(o.row.solution_type),1)]),_:2},1024)):(h(),B("span",ue,"-"))]),_:1}),e(p,{prop:"planned_dev_time",label:"计划开发日期",sortable:""}),e(p,{prop:"dev_cycle_days",label:"开发周期(人天)",sortable:""}),e(p,{prop:"solution",label:"解决方案",sortable:""},{default:a(o=>[_("div",{class:"truncate-text",onClick:U=>m.showDetail(o.row),title:"点击查看详情"},f(o.row.solution),9,me)]),_:1}),e(p,{prop:"solved_by",label:"解决人",sortable:""}),e(p,{prop:"solved_time",label:"解决日期",sortable:""}),e(p,{prop:"created_at",label:"创建时间",sortable:""}),e(p,{prop:"created_by",label:"创建人",sortable:""}),e(p,{prop:"updated_at",label:"更新时间",sortable:""}),e(p,{prop:"updated_by",label:"更新人",sortable:""}),e(p,{label:"操作",fixed:"right",width:"150"},{default:a(o=>[_("div",pe,[e(b,{size:"small",type:"warning",disabled:!l.hasUpdatePermission,onClick:U=>m.handleEdit(o.$index,o.row)},{default:a(()=>[u("编辑")]),_:2},1032,["disabled","onClick"]),e(b,{size:"small",type:"danger",disabled:!l.hasDeletePermission,onClick:U=>m.handleDelete(o.$index,o.row)},{default:a(()=>[u("删除")]),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data","onSortChange"])),[[T,l.loading]]),_("div",_e,[e(N,{background:"","current-page":l.search.currentPage,"page-size":l.search.pageSize,total:l.search.total,"page-sizes":[10,20,50,100,1e3,1e4],"pager-count":5,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:m.handlePageSizeChange,onCurrentChange:m.handlePageChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}const he=O(Z,[["render",fe],["__scopeId","data-v-b4273ef4"]]);export{he as default};
